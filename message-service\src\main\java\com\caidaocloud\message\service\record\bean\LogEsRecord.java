package com.caidaocloud.message.service.record.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.caidaocloud.record.core.annotation.DynamicIndex;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.zxp.esclientrhl.annotation.ESID;
import org.zxp.esclientrhl.annotation.ESMapping;
import org.zxp.esclientrhl.annotation.ESMetaData;
import org.zxp.esclientrhl.enums.DataType;

import java.util.Date;

/**
 * created by: FoAng
 * create time: 28/5/2024 5:49 下午
 */
@Data
@DynamicIndex
@ESMetaData(indexName = "log_record", number_of_shards = 5,number_of_replicas = 0, autoCreateIndex = false)
public class LogEsRecord {

    @ESID
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("操作人")
    @ESMapping(datatype = DataType.keyword_type)
    private Long operator;

    @ESMapping(nested_class = UserAccountInfo.class)
    private UserAccountInfo operatorInfo;

    @ApiModelProperty("员工ID")
    @ESMapping(datatype = DataType.keyword_type)
    private Long empId;

    @ApiModelProperty("操作来源")
    @ESMapping(datatype = DataType.keyword_type)
    private String operatorSource;

    @ApiModelProperty("操作平台")
    @ESMapping(datatype = DataType.keyword_type)
    private String operatorPlatform;

    @ApiModelProperty("请求uri")
    @ESMapping(datatype = DataType.keyword_type)
    private String uri;

    @ApiModelProperty("请求菜单")
    @ESMapping(datatype = DataType.keyword_type)
    private String menu;

    @ApiModelProperty("请求菜单")
    @ESMapping(datatype = DataType.keyword_type)
    private String serviceName;

    @ApiModelProperty("操作类型")
    @ESMapping(datatype = DataType.keyword_type)
    private String category;

    @ApiModelProperty("操作摘要")
    @ESMapping(datatype = DataType.text_type)
    private String action;

    @ApiModelProperty("操作详细内容")
    @ESMapping(datatype = DataType.text_type)
    private String detail;

    @ApiModelProperty("操作状态")
    @ESMapping(datatype = DataType.boolean_type)
    private Boolean status;

    @ApiModelProperty("操作时间")
    @ESMapping(datatype = DataType.date_type)
    private Date createTime;

    @ApiModelProperty("是否删除")
    @ESMapping(datatype = DataType.integer_type)
    private Integer isDeleted;
}
