package com.caidaocloud.message.service.application.feign;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/2/21 上午11:54
 * @Version 1.0
 **/
@Component
public class DingTalkAgencyFeignClientFallback implements DingTalkAgencyFeignClient {


    @Override
    public Map createTask(Map reqMap, String unionId,  String accessToken) {
        return new HashMap<>();
    }
}

 
    
    
    
    