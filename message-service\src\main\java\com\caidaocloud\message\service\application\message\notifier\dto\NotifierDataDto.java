package com.caidaocloud.message.service.application.message.notifier.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class NotifierDataDto {
    private List<String> empIds;
    private Long dataTime;
    private int type;

    public static NotifierDataDto bulid(NotifyObjectDto obj){
        NotifierDataDto query = new NotifierDataDto();
        query.setDataTime(obj.getDataTime());
        query.setType(obj.getType());
        query.setEmpIds(obj.getEmpIds());
        return query;
    }
}
