package com.caidaocloud.message.service.domain.base.entity;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.service.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.message.service.domain.base.enums.StatusEnum;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.BeanUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/1/18
 */
@Data
public class DataEntity extends DataSimple {
    private EnumSimple status;

    public static void initFieldValue(String identifier, BusinessEventTypeEnum eventTypeEnum, DataEntity target, DataEntity source) {
        initDataSimpleBaseFieldValue(identifier, target, source);

        EnumSimple statusEnumSimple = new EnumSimple();
        switch (eventTypeEnum) {
            case CREATE:
                statusEnumSimple.setValue(StatusEnum.ENABLED.getIndex().toString());
                target.setStatus(statusEnumSimple);
                break;
            case UPDATE:
                if (source != null && source.getStatus() != null) {
                    target.setStatus(source.getStatus());
                } else if (target.getStatus() == null) {
                    statusEnumSimple.setValue(StatusEnum.ENABLED.getIndex().toString());
                    target.setStatus(statusEnumSimple);
                }
                break;
            case ENABLE:
                PreCheck.preCheckArgument(null == source, "数据不存在");
                BeanUtil.copyProperties(source, target, "updateBy", "updateTime", "dataStartTime");
                statusEnumSimple.setValue(StatusEnum.ENABLED.getIndex().toString());
                target.setStatus(statusEnumSimple);
                break;
            case DISABLE:
                PreCheck.preCheckArgument(null == source, "数据不存在");
                BeanUtil.copyProperties(source, target, "updateBy", "updateTime", "dataStartTime");
                statusEnumSimple.setValue(StatusEnum.DEACTIVATED.getIndex().toString());
                target.setStatus(statusEnumSimple);
                break;
            default:
                break;
        }
    }

    public static void initDataSimpleBaseFieldValue(String identifier, DataSimple target, DataSimple source) {
        UserInfo userInfo = UserContext.preCheckUser();

        target.setCreateBy(userInfo.getUserId().toString());
        target.setCreateTime(System.currentTimeMillis());
        target.setUpdateBy(target.getCreateBy());
        target.setUpdateTime(target.getCreateTime());
        target.setIdentifier(identifier);
        target.setDeleted(Boolean.FALSE);
        target.setTenantId(userInfo.getTenantId());

        if (source != null) {
            target.setCreateBy(source.getCreateBy());
            target.setCreateTime(source.getCreateTime());
        }
    }

    public static Long getTimeIfNullSetDefault(Long dateTime) {
        return (dateTime == null || dateTime == 0) ? System.currentTimeMillis() : dateTime;
    }

    public void setStatus(EnumSimple status) {
        this.status = status;
    }

    public void setStatus(Integer status) {
        EnumSimple statusEnumSimple = new EnumSimple();
        statusEnumSimple.setValue(null != status ? status.toString() : null);
        this.setStatus(statusEnumSimple);
    }
}
