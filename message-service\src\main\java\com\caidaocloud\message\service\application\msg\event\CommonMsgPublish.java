package com.caidaocloud.message.service.application.msg.event;

import com.caidaocloud.message.sdk.enums.MessageTaskType;
import com.caidaocloud.message.service.application.msg.dto.DingWorkflowNoticeDto;
import com.caidaocloud.message.service.application.msg.dto.DingtalkToDoNoticeDto;
import com.caidaocloud.message.service.application.msg.dto.MsgDto;
import com.caidaocloud.message.service.application.msg.dto.TemplateRefreshMessageDto;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class CommonMsgPublish {
    private final static String MSG_TEMPLATE_REFRESH_EXCHANGE = "caidaocloud.message.fac.direct.exchange",
        MSG_TEMPLATE_REFRESH_ROUTING_KEY = "routingKey.message.template.refresh",
            MESSAGE_DELAY_EXCHANGE = "message.fac.delayed.exchange",
            WORKFLOW_MESSAGE_EXCHANGE = "workflow.message.notice.handler.direct.exchange",
            WORKFLOW_MESSAGE_ROUTING_KEY = "workflow.message.notice.handler.routingKey",
            SEND_MSG_DELAY_ROUTING_KEY = "routingKey.message.delayed.msg";

    @Resource
    private MqMessageProducer<CommonMessage> producer;

    /**
     * 发送消息到workflow服务
     * @param doNoticeDto
     * @param thirdId
     */
    public void sendDingTaskIdToWorkFlow(DingtalkToDoNoticeDto doNoticeDto, String thirdId) {
        if (doNoticeDto == null || StringUtils.isEmpty(thirdId)) {
            return;
        }
        DingWorkflowNoticeDto noticeDto = new DingWorkflowNoticeDto();
        noticeDto.setTenantId(UserContext.getTenantId());
        noticeDto.setBusinessKey(doNoticeDto.getBusinessKey());
        noticeDto.setTaskId(doNoticeDto.getTaskId());
        noticeDto.setId(doNoticeDto.getDingCaidaoId());
        noticeDto.setThirdId(thirdId);
        noticeDto.setType(MessageTaskType.DING_DING_TODO.getIndex());

        String body = FastjsonUtil.toJson(noticeDto);
        log.info("[sendDingTaskIdToWorkFlow] prepare send message to rabbitmq, body=[{}]", body);
        CommonMessage commonMessage = new CommonMessage();
        commonMessage.setExchange(WORKFLOW_MESSAGE_EXCHANGE);
        commonMessage.setRoutingKey(WORKFLOW_MESSAGE_ROUTING_KEY);
        commonMessage.setBody(body);
        log.info("[sendDingTaskIdToWorkFlow] start send message, body=[{}]", body);
        producer.publish(commonMessage);

    }


    public void templateRefreshMessagePublish(String bid, String tenantId, String userId, String progress) {
        TemplateRefreshMessageDto message = new TemplateRefreshMessageDto();
        message.setBid(bid);
        message.setTenantId(tenantId);
        message.setProgress(progress);
        message.setUserId(userId);
        CommonMessage msg = new CommonMessage();
        String msgBody = FastjsonUtil.toJson(message);
        msg.setBody(msgBody);
        msg.setExchange(MSG_TEMPLATE_REFRESH_EXCHANGE);
        msg.setRoutingKey(MSG_TEMPLATE_REFRESH_ROUTING_KEY);
        log.info("message template RefreshMessagePublish time={} body={}",
                System.currentTimeMillis(), msgBody);
        producer.publish(msg);
    }

    public void publishDelayMsg(MsgDto msgDto, Integer delayTime) {
        CommonMessage msg = new CommonMessage();
        String msgBody = FastjsonUtil.toJson(msgDto);
        log.info("publishDelayMsg.msgBody={}", msgBody);
        msg.setBody(msgBody);
        msg.setExchange(MESSAGE_DELAY_EXCHANGE);
        msg.setRoutingKey(SEND_MSG_DELAY_ROUTING_KEY);
        producer.convertAndSend(msg, delayTime);
    }

}
