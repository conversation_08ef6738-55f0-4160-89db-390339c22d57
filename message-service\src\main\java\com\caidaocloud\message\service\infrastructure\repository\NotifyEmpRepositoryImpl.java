package com.caidaocloud.message.service.infrastructure.repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.caidaocloud.hr.service.dto.EmpWorkInfoDto;
import com.caidaocloud.hr.service.vo.EmpPrivateInfoVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.service.application.feign.HrServiceFeignClient;
import com.caidaocloud.message.service.application.feign.MasterdataFeignClientV2;
import com.caidaocloud.message.service.domain.base.repository.INotifyEmpRepository;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 * @date 2023/7/27
 */
@Repository
public class NotifyEmpRepositoryImpl implements INotifyEmpRepository {

	@Autowired
	private MasterdataFeignClientV2 masterdataFeignClientV2;

	@Autowired
	private HrServiceFeignClient hrServiceFeignClient;

	@Override
	public Result<List<EmpWorkInfoDto>> loadAllEmp() {
		return hrServiceFeignClient.getAllEmpList();
	}

	@Override
	public Result<List<EmpPrivateInfoVo>> loadAllGuardian() {
		return hrServiceFeignClient.allGuardian();
	}

	@Override
	public Result<List<EmpWorkInfoDto>> loadAllHrbp(long currentTimeMillis) {
		return hrServiceFeignClient.getAllOrganizeHrbp(currentTimeMillis);
	}

	@Override
	public Result<List<EmpWorkInfoDto>> loadAllLeader(long currentTimeMillis) {
		return hrServiceFeignClient.getAllOrganizeLeader(currentTimeMillis);
	}

	@Override
	public Result<List<EmpWorkInfoDto>> loadEmpList(String empIds) {
		Result<List<EmpWorkInfoVo>> result = hrServiceFeignClient.getEmpWorkInfoByEmpIds(empIds);
		List<EmpWorkInfoDto> empWorkInfoDtos = ObjectConverter.convertList(result.getData(), EmpWorkInfoDto.class);
		Map<String, EnumSimple> empStatusMap = result.getData().stream().collect(Collectors.toMap(EmpWorkInfoVo::getEmpId, EmpWorkInfoVo::getEmpStatus, (k1, k2) -> k1));

		for (EmpWorkInfoDto empWorkInfoDto : empWorkInfoDtos) {
			empWorkInfoDto.setEmpStatus(empStatusMap.get(empWorkInfoDto.getEmpId()).getValue());
		}
		return Result.ok(empWorkInfoDtos);
	}

	@Override
	public Result<EmpWorkInfoDto> loadLeader(String empId, long datetime) {
		Result<com.caidaocloud.message.service.interfaces.dto.emp.EmpWorkInfoDto> result = masterdataFeignClientV2.loadEmpLeader(empId, datetime);
		EmpWorkInfoDto workInfoDto = convertWorkInfo(result);
		return Result.ok(workInfoDto);
	}

	private EmpWorkInfoDto convertWorkInfo(Result<com.caidaocloud.message.service.interfaces.dto.emp.EmpWorkInfoDto> result) {
		EmpWorkInfoDto workInfoDto = ObjectConverter.convert(result.getData(), EmpWorkInfoDto.class);
		workInfoDto.setEmpStatus(result.getData().getEmpStatus().getValue());
		return workInfoDto;
	}

	@Override
	public Result<List<EmpPrivateInfoVo>> loadPrivateInfoList(String empIdStr) {
		return hrServiceFeignClient.getEmpPrivateInfoByEmpIds(empIdStr);
	}

	@Override
	public Result<EmpWorkInfoDto> loadHrbp(String empId, long datetime) {
		Result<com.caidaocloud.message.service.interfaces.dto.emp.EmpWorkInfoDto> result = masterdataFeignClientV2.loadEmpHrbp(empId, datetime);
		EmpWorkInfoDto workInfoDto = convertWorkInfo(result);
		return Result.ok(workInfoDto);

	}

	@Override
	public Result<EmpWorkInfoDto> loadOrgLeader(String empId, long datetime) {
		Result<com.caidaocloud.message.service.interfaces.dto.emp.EmpWorkInfoDto> result = masterdataFeignClientV2.loadEmpOrgLeader(empId, datetime);
		EmpWorkInfoDto workInfoDto = convertWorkInfo(result);
		return Result.ok(workInfoDto);
	}

	@Override
	public Result<EmpPrivateInfoVo> loadPrivateInfo(String empId) {
		return  masterdataFeignClientV2.loadPrivateInfo(empId);
	}
}
