package com.caidaocloud.message.service.application.msg.service;

import cn.hutool.core.collection.ListUtil;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.condition.tree.*;
import com.caidaocloud.dto.*;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.dto.EmpWorkInfoDto;
import com.caidaocloud.hr.service.vo.EmpPrivateInfoVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.message.sdk.dto.MsgNoticeDto;
import com.caidaocloud.message.sdk.enums.ConditionItems;
import com.caidaocloud.message.sdk.enums.NoticeRule;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.enums.NotificationCycleTypeEnum;
import com.caidaocloud.message.sdk.event.publish.MsgPublish;
import com.caidaocloud.message.service.application.common.constant.CacheCodeConstant;
import com.caidaocloud.message.service.application.common.constant.LangCodeConstant;
import com.caidaocloud.message.service.application.common.enums.*;
import com.caidaocloud.message.service.application.common.service.BaseServiceImpl;
import com.caidaocloud.message.service.application.common.service.MetadataService;
import com.caidaocloud.message.service.application.feign.AuthServiceFeignClient;
import com.caidaocloud.message.service.application.feign.HrFeignClient;
import com.caidaocloud.message.service.application.feign.MasterdataFeignClient;
import com.caidaocloud.message.service.application.message.notifier.dto.RoleUserQueryDto;
import com.caidaocloud.message.service.application.msg.cron.ConditionTaskService;
import com.caidaocloud.message.service.application.msg.dto.EmailMsgDto;
import com.caidaocloud.message.service.application.msg.dto.MessageConfigPageQueryDto;
import com.caidaocloud.message.service.application.msg.dto.MsgDto;
import com.caidaocloud.message.service.application.msg.dto.RelationshipDto;
import com.caidaocloud.message.service.application.msg.event.CommonMsgPublish;
import com.caidaocloud.message.service.application.msg.event.ContractConsumer;
import com.caidaocloud.message.service.application.sms.dto.SmsMessageDto;
import com.caidaocloud.message.service.application.template.service.analysis.TemplateAnalysisService;
import com.caidaocloud.message.service.domain.base.repository.INotifyEmpRepository;
import com.caidaocloud.message.service.domain.base.service.BaseDomainService;
import com.caidaocloud.message.service.domain.base.util.ObjectConvertUtil;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.domain.msg.entity.EmpMsgTemplateRelDo;
import com.caidaocloud.message.service.domain.msg.entity.MergeRule;
import com.caidaocloud.message.service.domain.msg.entity.MsgConfigDo;
import com.caidaocloud.message.service.domain.msg.service.EmpMsgTemplateRelDomainService;
import com.caidaocloud.message.service.domain.msg.service.MsgConfigDomainService;
import com.caidaocloud.message.service.domain.template.entity.TemplateDo;
import com.caidaocloud.message.service.domain.template.service.TemplateDomainService;
import com.caidaocloud.message.service.infrastructure.utils.LangUtil;
import com.caidaocloud.message.service.interfaces.dto.msg.MsgConfigDto;
import com.caidaocloud.message.service.interfaces.dto.msg.MsgConfigQueryDto;
import com.caidaocloud.message.service.interfaces.dto.template.TemplateDto;
import com.caidaocloud.message.service.interfaces.dto.template.TemplateQueryDto;
import com.caidaocloud.message.service.interfaces.vo.msg.*;
import com.caidaocloud.message.service.interfaces.vo.template.TemplateVo;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.google.common.base.Joiner;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.caidaocloud.message.service.application.common.constant.LangCodeConstant.*;
import static com.caidaocloud.message.service.application.common.enums.NoticeTarget.ASSIGN_EMP;
import static java.util.stream.Collectors.toMap;

/**
 * MsgConfigService
 *
 * <AUTHOR>
 * @date 2022/6/6 下午6:07
 */
@Service
@Slf4j
public class MsgConfigService extends BaseServiceImpl<MsgConfigDo, MsgConfigQueryDto> {
    @Resource
    private MsgConfigDomainService domainService;
    @Resource
    private MetadataService metadataService;
    @Resource
    private TemplateDomainService templateDomainService;
    @Resource
    private ConditionTaskService conditionTaskService;
    @Resource
    private EmpMsgTemplateRelDomainService empMsgTemplateRelDomainService;
    @Resource
    private CommonMsgPublish product;
    @Resource
    private MasterdataFeignClient masterdataFeignClient;
    @Resource
    private TemplateAnalysisService templateAnalysisService;
    @Resource
    private EmpMsgTemplateRelService empMsgTemplateRelService;
    @Resource
    private CacheService cacheService;
    @Resource
    private AuthServiceFeignClient authServiceFeignClient;
    @Resource
    private INotifyEmpRepository masterdataEmpRepository;
    @Resource
    private HrFeignClient hrFeignClient;
    @Resource
    private ContractConsumer contractConsumer;
    @Getter(AccessLevel.NONE)
    private static final String UNIT = "天";
    @Resource
    private MsgPublish msgPublish;

    @Override
    protected BaseDomainService getDomainService() {
        return domainService;
    }

    private static final int MAX_SIZE = 100;

    @Deprecated
    public List<ConditionItem> getConditionList() {
        var noContainChildList = ConditionOperator.noContainChild();
        Map<String, MetadataPropertyVo> metadataCacheMap = new HashMap<>();
        List<ConditionItem> result = new ArrayList();
        ConditionItem condition = new ConditionItem();
        condition.setName("用工类型");
        condition.setCode("hr#EmpWorkInfo#empType$dictValue");
        condition.setOperators(noContainChildList);
        condition.setType(ConditionCallType.OUTSIDE_SERVICE);
        condition.setComponent(ValueComponent.DICT_SELECTOR);
        Map<String, String> empTypeMap = new HashMap<>(2);
        empTypeMap.put("belongModule", "Employee");
        empTypeMap.put("typeCode", "EmployType");
        condition.setDataSourceParams(empTypeMap);
        condition.setComponentValueEnum(Lists.list());
        result.add(condition);

        condition = new ConditionItem();
        condition.setName("转正状态");
        condition.setCode("hr#EmpWorkInfo#confirmationStatus");
        condition.setOperators(noContainChildList);
        condition.setType(ConditionCallType.OUTSIDE_SERVICE);
        condition.setComponent(ValueComponent.ENUM);
        condition.setComponentValueEnum(getValueEnum("confirmationStatus", metadataCacheMap, "entity.hr.EmpWorkInfo"));
        result.add(condition);

        condition = new ConditionItem();
        condition.setName("合同公司");
        condition.setCode("hr#EmpWorkInfo#company");
        condition.setOperators(noContainChildList);
        condition.setType(ConditionCallType.OUTSIDE_SERVICE);
        condition.setComponent(ValueComponent.COMPANY);
        condition.setComponentValueEnum(Lists.list());
        result.add(condition);

        condition = new ConditionItem();
        condition.setName("任职组织");
        condition.setCode("hr#EmpWorkInfo#organize");
        condition.setOperators(ConditionOperator.enableOpts());
        condition.setType(ConditionCallType.OUTSIDE_SERVICE);
        condition.setComponent(ValueComponent.ORG);
        condition.setComponentValueEnum(Lists.list());
        result.add(condition);

        condition = new ConditionItem();
        condition.setName("工作地");
        condition.setCode("hr#EmpWorkInfo#workplace");
        condition.setOperators(noContainChildList);
        condition.setType(ConditionCallType.OUTSIDE_SERVICE);
        condition.setComponent(ValueComponent.WORKPLACE);
        condition.setComponentValueEnum(Lists.list());
        result.add(condition);

        condition = new ConditionItem();
        condition.setName("职级");
        condition.setCode("hr#EmpWorkInfo#jobGrade$startGrade");
        condition.setOperators(noContainChildList);
        condition.setType(ConditionCallType.INSIDER_SERVICE);
        condition.setComponent(ValueComponent.JOB_GRADE);
        condition.setDataSourceAddress("api/hr/jobgrade/v1/treeList");
        condition.setComponentValueEnum(Lists.list());
        result.add(condition);

        condition = new ConditionItem();
        condition.setName("合同类型");
        condition.setCode("hr#LastContract#contractSettingType$dictValue#owner.empId");
        condition.setOperators(noContainChildList);
        condition.setType(ConditionCallType.INSIDER_SERVICE);
        condition.setComponent(ValueComponent.DICT_SELECTOR);
        empTypeMap = new HashMap<>(2);
        empTypeMap.put("belongModule", "Employee");
        empTypeMap.put("typeCode", "ContractType");
        condition.setDataSourceParams(empTypeMap);
        condition.setComponentValueEnum(Lists.list());
        result.add(condition);

        condition = new ConditionItem();
        condition.setName("性别");
        condition.setCode("hr#EmpPrivateInfo#sex$dictValue");
        condition.setOperators(noContainChildList);
        condition.setType(ConditionCallType.INSIDER_SERVICE);
        condition.setComponent(ValueComponent.DICT_SELECTOR);
        empTypeMap = new HashMap<>(2);
        empTypeMap.put("belongModule", "Employee");
        empTypeMap.put("typeCode", "Gender");
        condition.setDataSourceParams(empTypeMap);
        condition.setComponentValueEnum(Lists.list());
        result.add(condition);

        //CAIDAOM-2027 增加配置信息  劳动合同主体与科锐的关系、员工子类型；

        condition = new ConditionItem();
        condition.setName("劳动合同主体与科锐的关系");
        condition.setCode("hr#EmpWorkInfo#comCareer$dictValue");
        condition.setOperators(noContainChildList);
        condition.setType(ConditionCallType.OUTSIDE_SERVICE);
        condition.setComponent(ValueComponent.DICT_SELECTOR);
        Map<String, String> comCareerMap = new HashMap<>(2);
        comCareerMap.put("belongModule", "Employee");
        comCareerMap.put("typeCode", "comCareer");
        condition.setDataSourceParams(comCareerMap);
        condition.setComponentValueEnum(Lists.list());
        result.add(condition);

        condition = new ConditionItem();
        condition.setName("员工子类型");
        condition.setCode("hr#EmpWorkInfo#empsubtype$dictValue");
        condition.setOperators(noContainChildList);
        condition.setType(ConditionCallType.OUTSIDE_SERVICE);
        condition.setComponent(ValueComponent.DICT_SELECTOR);
        Map<String, String> empsubtypeMap = new HashMap<>(2);
        empsubtypeMap.put("belongModule", "Employee");
        empsubtypeMap.put("typeCode", "Subtype");
        condition.setDataSourceParams(empsubtypeMap);
        condition.setComponentValueEnum(Lists.list());
        result.add(condition);

        return result;
    }

    public List<ConditionItem> getConditionListV2() {
        List<ConditionItem> items = ConditionItems.generateConditionItemList();
        for (ConditionItem item : items) {
            if ("hr#EmpWorkInfo#confirmationStatus".equals(item.getCode())) {
                item.setComponentValueEnum(getValueEnum("confirmationStatus", new HashMap<>(), "entity.hr.EmpWorkInfo"));
            }else {
                item.setComponentValueEnum(Lists.list());
            }
        }
        return items;
    }

    private List<ComponentValue> getValueEnum(String prop, Map<String, MetadataPropertyVo> cacheMap, String identifier) {
        MetadataPropertyVo vo = cacheMap.get(identifier);
        if (null == vo) {
            vo = metadataService.getPropertyDef(identifier, prop);
            cacheMap.put(identifier, vo);
        }

        List<ComponentValue> componentValues = ObjectConverter.convertList(vo.getEnumDef(), ComponentValue.class);
        return componentValues;
    }

    /**
     * 获取启动的消息配置
     *
     * @param type
     */
    public MsgConfigVo getEnabledMsgConfigByType(NoticeType type) {
        var msgConfigList = domainService.getMsgConfigByType(type, MsgConfigStatusEnum.ENABLED);
        if (CollectionUtils.isEmpty(msgConfigList)) {
            return new MsgConfigVo();
        }
        var messageConfigDto = msgConfigList.get(0);
        var msgConfigVo = FastjsonUtil.convertObject(messageConfigDto, MsgConfigVo.class);
        if (StringUtils.isNotBlank(messageConfigDto.getTemplateBid())) {
            var templateBids = Lists.list(messageConfigDto.getTemplateBid().split(","));
            var basePage = new TemplateQueryDto();
            basePage.setPageNo(1);
            basePage.setPageSize(templateBids.size());
            basePage.setTemplateBidList(templateBids);
            var templateList = templateDomainService.getPage(basePage).getItems();
            if (!CollectionUtils.isEmpty(templateList)) {
                /*List<MsgConfigVo.TemplateInfo> templateInfoList = templateList.stream().map(e -> {
                    var templateInfo = new MsgConfigVo.TemplateInfo();
                    BeanUtils.copyProperties(e, templateInfo, "category");
                    if (e.getCategory() != null) {
                        templateInfo.setCategory(MsgTemplateEnum.getEnumByIndex(e.getCategory().getValue()));
                    }
                    return templateInfo;
                }).collect(Collectors.toList());
                msgConfigVo.setTemplateList(templateInfoList);*/
            }
        }
        return msgConfigVo;
    }

    public List<MsgConfigVo> getEnableMsgConfigList(String noticeType) {
        List<MsgConfigDo> dataList = domainService.getEnableMsgConfigList(noticeType);
        return convertVoList(dataList);
    }

    public List<MsgConfigVo> selectList(String type) {
        if (StringUtil.isNotEmpty(type)) {
            return getEnableMsgConfigList(type);
        }

        List<String> types = IntStream.rangeClosed(Integer.valueOf(NoticeType.ON_BOARDING_CANDIDATE_SUBMIT.getIndex()),
                        Integer.valueOf(NoticeType.ON_BOARDING_URGE.getIndex()))
                .mapToObj(num -> String.valueOf(num)).collect(Collectors.toList());
        types.add(NoticeType.ON_BOARDING_OPERATOR_APPROVAL.getIndex());
        types.add(NoticeType.ON_BOARDING_CONTINUE_ENTRY.getIndex());
        types.add(NoticeType.ON_BOARDING_EMAIL_ERROR.getIndex());
        types.add(NoticeType.EMPLOYEE_EMAIL_ERROR.getIndex());
        types.add(NoticeType.ON_BOARDING_NO_CONFIRM.getIndex());
        types.add(NoticeType.ON_BOARDING_CERIFICATION_CODE.getIndex());
        types.add(NoticeType.ON_BOARDING_CANCEL.getIndex());
        types.add(NoticeType.ON_BOARDING_EXPIRE_NO_ENTRY.getIndex());
        types.add(NoticeType.ON_BOARDING_REFUSE_OFFER.getIndex());
        types.add(NoticeType.ON_BOARDING_TRAINING_DATE_UPDATE.getIndex());
        //caidao-- 2247 增加 同意入职选择；
        types.add(NoticeType.ON_BOARDING_ACCEPT_OFFER.getIndex());
        types.add(NoticeType.ON_BOARDING_ACCOUNT_CREATE.getIndex());

        List<MsgConfigDo> dataList = domainService.getOnboardingSelectList(types);
        return convertVoList(dataList);
    }

    private List<MsgConfigVo> convertVoList(List<MsgConfigDo> dataList) {
        if (null == dataList || dataList.isEmpty()) {
            return Lists.list();
        }

        // List<MsgConfigVo> msgList = ObjectConverter.convertList(dataList, MsgConfigVo.class);
        return Sequences.sequence(dataList).map(MsgConfigVo::fromEntity).toList();
    }

    public PageResult<MsgConfigVo> getEnableMsgConfigPageList(MessageConfigPageQueryDto queryDto) {
        PageResult<MsgConfigDo> pageResult = domainService.getEnableMsgConfigPageList(queryDto);
        if (CollectionUtils.isEmpty(pageResult.getItems())) {
            return new PageResult<>(Lists.list(), pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
        }
        List<MsgConfigVo> dataList = ObjectConverter.convertList(pageResult.getItems(), MsgConfigVo.class);
        return new PageResult<>(dataList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    public List<MsgConfigVo> getMsgConfig(NoticeType type, List<String> empList) {
        if (null == empList || empList.isEmpty()) {
            return Lists.list();
        }

        List<EmpMsgTemplateRelDo> msgTemplateList = empMsgTemplateRelDomainService.getByEmpIds(empList);
        if (null == msgTemplateList || msgTemplateList.isEmpty()) {
            return Lists.list();
        }

        List<String> collect = msgTemplateList.stream().map(EmpMsgTemplateRelDo::getTemplate).collect(Collectors.toList());
        if (null == collect || collect.isEmpty()) {
            return Lists.list();
        }

        List<MsgConfigDo> dataList = domainService.getMsgConfigByTypeAndIds(collect, type, MsgConfigStatusEnum.ENABLED);
        if (null == dataList || dataList.isEmpty()) {
            return Lists.list();
        }

        List<MsgConfigVo> msgList = ObjectConverter.convertList(dataList, MsgConfigVo.class);
        Map<String, MsgConfigVo> msgMap = msgList.stream()
                .collect(Collectors.toMap(MsgConfigVo::getBid, condition -> condition, (k1, k2) -> k2));

        List<String> templateIdList = new ArrayList<>(msgMap.keySet().size());
        templateIdList.addAll(msgMap.keySet());
        TemplateQueryDto basePage = new TemplateQueryDto();
        basePage.setPageNo(1);
        basePage.setPageSize(templateIdList.size());
        basePage.setTemplateBidList(templateIdList);
        List<TemplateDo> templateList = templateDomainService.getPage(basePage).getItems();
        if (null == templateList || templateList.isEmpty()) {
            return msgList;
        }

        List<TemplateVo> voList = ObjectConverter.convertList(templateList, TemplateVo.class);
        voList.forEach(node -> {
            MsgConfigVo item = msgMap.get(node.getMsgConfig());
            if (null == item) {
                return;
            }

            List<TemplateVo> list = item.getTemplateList();
            if (null == list) {
                list = new ArrayList<>(10);
            }
            list.add(node);
            item.setTemplateList(list);
            msgMap.put(node.getMsgConfig(), item);
        });

        return ObjectConverter.convertList(dataList, MsgConfigVo.class);
    }

    @Override
    public MsgConfigDo dto2do(Object data) {
        MsgConfigDo doData = super.dto2do(data);
        MsgConfigDto dto = (MsgConfigDto) data;
        doData.setName(String.valueOf(dto.getI18nName().get("default")));
        doData.setI18nName(FastjsonUtil.toJson(dto.getI18nName()));
        if (StringUtils.isNotEmpty(dto.getFunc())) {
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(dto.getFunc());
            doData.setFunc(enumSimple);
        }

        if (dto.getMsgType() != null) {
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(dto.getMsgType().getIndex());
            doData.setMsgType(enumSimple);
        }

        if (StringUtils.isNotEmpty(dto.getRound())) {
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(dto.getRound());
            doData.setRound(enumSimple);
        }

        doData.setChannel(FastjsonUtil.toJson(((MsgConfigDto) data).getChannel()));

        if (dto.getNotifyObject() != null) {
            doData.setNotifyObject(FastjsonUtil.toJson(dto.getNotifyObject()));
        }
        doData.setCondition(FastjsonUtil.toJson((dto).getCondition()));

        // 发送时间转为0点开始毫秒数
        if (dto.getSendTime() != null) {
            doData.setSendTime(Math.toIntExact(dto.getSendTime() * 1000L - DateUtil.getCurrentTimestamp()));
        }

        if (dto.getRule() != null) {
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(dto.getRule().name());
            doData.setRule(enumSimple);
        }

        // 合并发送规则
        if (dto.getMergeRule() != null) {
            doData.setMergeRule(ObjectConverter.convert(dto.getMergeRule(), MergeRule.class));
            Integer mergeTime = dto.getMergeRule().getMergeTime();
            if (mergeTime != null) {
                doData.getMergeRule().setMergeTime(Math.toIntExact(mergeTime * 1000L - DateUtil.getCurrentTimestamp()));
            }
        }

        return doData;
    }

    @Override
    protected void checkObj(MsgConfigDo data) {
        super.checkObj(data);
        PreCheck.preCheckNotNull(data.getName(), LangUtil.getFormatMsg(code_70000, LangUtil.getMsg(code_70009)));
        PreCheck.preCheckNotNull(data.getMsgType(), LangUtil.getFormatMsg(code_70000, LangUtil.getMsg(code_70010)));
        // 未签署通知类型的消息，通知规则不能为空
        if (data.checkNoSignMsgType()) {
            PreCheck.preCheckNotNull(data.getRule(), LangUtil.getFormatMsg(code_70000, LangUtil.getMsg(code_70022)));
        }
        // 工作流/考勤通知不校验通知对象、发送时间
        if (NoticeType.WORKFLOW_PUSH.getIndex().equals(data.getMsgType().getValue())
                || NoticeType.ATTENDANCE_REGISTER_MSG.getIndex().equals(data.getMsgType().getValue())
                || NoticeType.ATTENDANCE_DAILY_REPORT_MSG.getIndex().equals(data.getMsgType().getValue())
                || NoticeType.ATTENDANCE_WEEKLY_REPORT_MSG.getIndex().equals(data.getMsgType().getValue())
                || NoticeType.ATTENDANCE_MONTH_REPORT_MSG.getIndex().equals(data.getMsgType().getValue())
                || NoticeType.ATTENDANCE_LEAVE_CANCEL_MSG.getIndex().equals(data.getMsgType().getValue())) {
            return;
        }
        PreCheck.preCheckNotNull(data.getNotifyObject(), LangUtil.getFormatMsg(code_70000, LangUtil.getMsg(code_70011)));
        // PreCheck.preCheckNotNull(data.getSendTime(), LangUtil.getFormatMsg(code_70000,LangUtil.getMsg(SEND_TIME)));
        // 附件不能大于 9 个
        if (data.getNotifyObject().contains(ASSIGN_EMP.toString())) {
            // 指定系统人员不为空
            PreCheck.preCheckArgument(data.getEmps() == null || data.getEmps().isEmpty(),
                    LangUtil.getFormatMsg(code_70000, LangUtil.getMsg(LangCodeConstant.ASSIGN_EMP)));
            // 指定系统人员最大不超过100
            PreCheck.preCheckArgument(data.getEmps().size() > MAX_SIZE, LangUtil.getMsg(LangCodeConstant.ASSIGN_EMP_EXCEED_100));
        }
        // 手动推送时，发送日期和发送时间不能为空
        PreCheck.preCheckArgument(NoticeType.MANUAL_PUSH.getIndex().equals(data.getMsgType().getValue())
                        && (data.getTriggerDate() == null || data.getSendTime() == null),
                LangUtil.getFormatMsg(code_70000, LangUtil.getMsg(SEND_TIME)));
        // 手动推送时，发送时间不能小于当前时间
        PreCheck.preCheckArgument(NoticeType.MANUAL_PUSH.getIndex().equals(data.getMsgType().getValue())
                && data.getTriggerDate() + data.getSendTime() <= System.currentTimeMillis(), LangUtil.getMsg(SEND_TIME_ERROR));

    }

    /**
     * 消息配置进行数据缓存
     *
     * @param msgConfigBid
     */
    private MsgConfigDo doCacheMsgConfig(String msgConfigBid) {
        if (StringUtils.isEmpty(msgConfigBid)) {
            log.info("doCacheMsgConfig error msgConfigBid empty");
            return null;
        }
        MsgConfigDo msgConfigData = domainService.getById(msgConfigBid);
        if (null == msgConfigData) {
            log.info("doCacheMsgConfig error MsgConfigDo Empty msgConfigBid={}", msgConfigBid);
            return null;
        }
        msgConfigData.getProperties().clear();
        String cacheKey = String.format(CacheCodeConstant.ENTITY_MESSAGE_MSGCONFIG_KEY, msgConfigData.getTenantId(), msgConfigData.getBid());
        cacheService.cacheValue(cacheKey, FastjsonUtil.toJson(msgConfigData));
        return msgConfigData;
    }

    /**
     * 从缓存中获取消息配置
     *
     * @param tenantId
     * @param msgConfigBid
     * @return
     */
    public MsgConfigDo getMsgConfigFromCache(String tenantId, String msgConfigBid) {
        String cacheKey = String.format(CacheCodeConstant.ENTITY_MESSAGE_MSGCONFIG_KEY, tenantId, msgConfigBid);
        if (!cacheService.containsKey(cacheKey) && StringUtils.isEmpty(cacheService.getValue(cacheKey))) {
            return doCacheMsgConfig(msgConfigBid);
        }
        String cacheValue = cacheService.getValue(cacheKey);
        return FastjsonUtil.toObject(cacheValue, MsgConfigDo.class);
    }

    /**
     * 检查消息配置是否已启用
     * 通知类型为手动推送的消息，保存后，状态立即为已开启状态，如果消息时间设置的是预发送的，可以在发送之前停用这条消息设置
     *
     * @param tenantId
     * @param msgConfigBid
     * @return
     */
    public Boolean checkMsgConfigEnable(String tenantId, String msgConfigBid) {
        log.info("start checkMsgConfigEnable,tenantId={},msgConfigBid={}", tenantId, msgConfigBid);
        // 兼容消息内容未设置msgConfigBid的场景
        if (StringUtils.isEmpty(msgConfigBid) || StringUtils.isEmpty(tenantId)) {
            return true;
        }
        // 查询消息配置
        MsgConfigDo msgConfigData = getMsgConfigFromCache(tenantId, msgConfigBid);
        if (null == msgConfigData || null == msgConfigData.getMsgType() || null == msgConfigData.getStatus()) {
            return false;
        }
        // 判断消息类型是否为：手动推送类型，非手动推送类型直接返回true
        if (!NoticeType.MANUAL_PUSH.getIndex().equals(msgConfigData.getMsgType().getValue())) {
            return true;
        }
        return MsgConfigStatusEnum.ENABLED.value.equals(msgConfigData.getStatus().getValue());
    }

    @Override
    public MsgConfigDo saveOrUpdateObj(Object data) {
        MsgConfigDo convert = dto2do(data);
        checkObj(convert);
        convert(convert, data);
        MsgConfigDo config = saveOrUpdate(convert);

        // 缓存消息配置
        doCacheMsgConfig(config.getBid());

        // 保存消息的关联模板
        MsgConfigDto dto = ObjectConverter.convert(data, MsgConfigDto.class);
        log.info("saving msg template of config '{}' >>>>>>>>>>>>>{}", config.getBid(), dto.getTemplates());
        templateDomainService.saveMsgTemplate(config, dto.getTemplates());

        // 生成新的匹配关系
        // 生成当前消息匹配条件的关联匹配数据
        if (StringUtil.isNotEmpty(config.getCondition())) {
            RelationshipDto rd = new RelationshipDto();
            rd.setTemplateTxt(config.getName()).setTemplate(config.getBid()).setConditionTree(config.getCondition())
                    .setTenantId(config.getTenantId());
            conditionTaskService.saveRelationshipData(rd);
        } else {
            // 删除匹配关系
            empMsgTemplateRelDomainService.deleteTemplateId(config.getBid());
        }

        dto.setSendTime(config.getSendTime());
        if (StringUtils.isEmpty(dto.getBid())) {
            dto.setBid(config.getBid());
        }

        // 通知种类为手动推送时，触发消息推送
        doManualPushMsg(dto);
        return config;
    }

    private void getNoticeEmpList(MsgConfigDto dto, List<EmpWorkInfoDto> notifyEmpWorkList, List<EmpPrivateInfoVo> guardianEmpList) {
        if (null == dto || CollectionUtils.isEmpty(dto.getNotifyObject())) {
            return;
        }
        // 通知对象种类
        for (String target : dto.getNotifyObject()) {
            switch (NoticeTarget.getEnumByValue(target)) {
                case ASSIGN_EMP:
                    if (CollectionUtils.isEmpty(dto.getEmps())) {
                        break;
                    }
                    List<String> selectEmpIds = dto.getEmps().stream().map(EmpSimple::getEmpId).distinct().collect(Collectors.toList());
                    String empIds = StringUtils.join(selectEmpIds, ",");
                    Result<List<EmpWorkInfoDto>> empResult = masterdataEmpRepository.loadEmpList(empIds);
                    if (null != empResult.getData() && empResult.getData().size() > 0) {
                        notifyEmpWorkList.addAll(empResult.getData());
                    }
                    break;
                case LEADER:
                    // 手动推送没有事件人
                    List<EmpWorkInfoDto> leaderWorkInfos = getLeaderWorkInfoDto();
                    if (!CollectionUtils.isEmpty(leaderWorkInfos)) {
                        notifyEmpWorkList.addAll(leaderWorkInfos);
                    }
                    break;
                case ALL_EMP:
                    Result<List<EmpWorkInfoDto>> allEmpResult = masterdataEmpRepository.loadAllEmp();
                    if (null != allEmpResult.getData() && allEmpResult.getData().size() > 0) {
                        notifyEmpWorkList.addAll(allEmpResult.getData());
                    }
                    break;
                case GUARDIAN:
                    Result<List<EmpPrivateInfoVo>> priInfoResult = masterdataEmpRepository.loadAllGuardian();
                    if (null != priInfoResult.getData()) {
                        guardianEmpList.addAll(priInfoResult.getData());
                    }
                    break;
                case HRBP:
                    List<EmpWorkInfoDto> hrbpWorkInfoList = getHrbpWorkInfoDto();
                    if (hrbpWorkInfoList != null && hrbpWorkInfoList.size() > 0) {
                        notifyEmpWorkList.addAll(hrbpWorkInfoList);
                    }
            }
        }
    }

    public void doGetAndFilterEmp(String template, BasePage page, List<String> noticeEmpIdList, List<String> matchEmpIdList) {
        // 分页查询匹配条件下的员工数据
        List<String> empIds = empMsgTemplateRelService.getEmpList(template, page);
        if (CollectionUtils.isEmpty(empIds)) {
            return;
        }
        int size = empIds.size();

        // 根据通知对象所匹配的员工数据过滤
        empIds.removeIf(o -> !noticeEmpIdList.contains(o));
        matchEmpIdList.addAll(empIds);

        if (size < page.getPageSize()) {
            return;
        }
        // 继续分页查询
        page.setPageNo(page.getPageNo() + 1);
        doGetAndFilterEmp(template, page, noticeEmpIdList, matchEmpIdList);
    }

    /**
     * 手动推送
     */
    public void doManualPushMsg(MsgConfigDto dto) {
        MsgConfigDo msgConfigData = domainService.getById(dto.getBid());
        if (null == msgConfigData) {
            log.info("doSendMsg.msgConfigData is empty bid={}", dto.getBid());
            return;
        }
        if (null == msgConfigData.getMsgType()
                || (!NoticeType.MANUAL_PUSH.getIndex().equals(msgConfigData.getMsgType().getValue()) &&
                !NoticeType.EMP_MANUAL_PUSH.getIndex().equals(msgConfigData.getMsgType().getValue()))) {
            log.info("doSendMsg.NoticeType is err bid={}", msgConfigData.getBid());
            return;
        }
        if (null == msgConfigData.getStatus() || !MsgConfigStatusEnum.ENABLED.value.equals(msgConfigData.getStatus().getValue())) {
            log.info("doSendMsg.status is err bid={}", msgConfigData.getBid());
            return;
        }
        if (CollectionUtils.isEmpty(dto.getTemplates())) {
            log.info("doSendMsg.getTemplates is empty bid={}", msgConfigData.getBid());
            return;
        }

        // 推送时间判断
        long nowDate = DateUtil.getCurrentTimestamp();
        long triggerDate = msgConfigData.getTriggerDate() == null ? nowDate : msgConfigData.getTriggerDate();
        if (triggerDate > nowDate) {
            // 发送日期大于当前日期，直接返回
            log.info("doSendMsg fail triggerDate={},nowDate={}", triggerDate, nowDate);
            return;
        }

        // 消息延迟推送时间
        long delayTime = getDelayTime(triggerDate, msgConfigData.getSendTime());

        List<EmpWorkInfoDto> notifyEmpWorkList = new ArrayList<>();
        List<EmpPrivateInfoVo> guardianEmpList = new ArrayList<>();

        // 获取通知对象信息
        getNoticeEmpList(dto, notifyEmpWorkList, guardianEmpList);

        // 通知对象的员工ID
        List<String> noticeEmpIdList = new ArrayList<>();
        List<String> noticeSystemEmpIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(notifyEmpWorkList)) {
            noticeSystemEmpIdList.addAll(notifyEmpWorkList.stream().filter(o -> StringUtils.isNotEmpty(o.getEmpId()))
                    .map(EmpWorkInfoDto::getEmpId).distinct().collect(Collectors.toList()));
            noticeEmpIdList.addAll(noticeSystemEmpIdList);
        }

        // 监护人
        List<String> noticeGuardianEmpIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(guardianEmpList)) {
            noticeGuardianEmpIdList.addAll(guardianEmpList.stream().filter(o -> StringUtils.isNotEmpty(o.getEmpId()))
                    .map(EmpPrivateInfoVo::getEmpId).distinct().collect(Collectors.toList()));
            noticeEmpIdList.addAll(noticeGuardianEmpIdList);
        }

        // 消息提醒通知模版
        List<TemplateVo> msgTptList = ObjectConverter.convertList(dto.getTemplates(), TemplateVo.class);
        if (CollectionUtils.isEmpty(noticeEmpIdList)) {
            log.info("noticeEmpIdList is empty");
            doSendExternalMsg(msgTptList, msgConfigData, dto.getChannel(), delayTime);
            return;
        }
        log.info("match notifyEmpIds:{}", FastjsonUtil.toJson(noticeEmpIdList));

        // 匹配条件
        if (dto.getCondition() != null && dto.getCondition().getType() != null) {
            BasePage page = new BasePage();
            page.setPageNo(1);
            page.setPageSize(2000);
            List<String> matchEmpIdList = new ArrayList<>();
            doGetAndFilterEmp(dto.getBid(), page, noticeEmpIdList, matchEmpIdList);
            log.info("matchEmpIdList={}", FastjsonUtil.toJson(matchEmpIdList));
            noticeEmpIdList.retainAll(matchEmpIdList);

            if (CollectionUtils.isEmpty(noticeEmpIdList)) {
                log.info("doManualPushMsg.noticeEmpIdList.doGetAndFilterEmp is empty");
                return;
            }
        }

        // 对通知对象去重
        Set<String> empIdSetList = new HashSet<>(noticeEmpIdList);
        noticeEmpIdList.clear();
        noticeEmpIdList.addAll(empIdSetList);
        log.info("remove repeated:{}", FastjsonUtil.toJson(noticeEmpIdList));

        if(NoticeType.EMP_MANUAL_PUSH.getIndex().equals(msgConfigData.getMsgType().getValue())){
            if(delayTime == 0L){
                pushContractConsumer(dto.getBid(), noticeEmpIdList);
            } else if (delayTime < 86400000){
                // 判断 86000
                pushContractConsumer(dto.getBid(), noticeEmpIdList, (int) delayTime);
            }
            return;
        }

        // 短息模版推送
        doSendSms(msgTptList, noticeEmpIdList, noticeSystemEmpIdList,
                noticeGuardianEmpIdList, delayTime, dto.getChannel(), msgConfigData);

        // 邮件模版推送
        doSendEmail(msgTptList, noticeEmpIdList, noticeSystemEmpIdList, noticeGuardianEmpIdList,
                delayTime, dto.getChannel(), msgConfigData);

        doSendExternalMsg(msgTptList, msgConfigData, dto.getChannel(), delayTime);
    }

    private void pushContractConsumer(String msgConfigId, List<String> noticeEmpIdList) {
        pushContractConsumer(msgConfigId, noticeEmpIdList, 0);
    }

    private void pushContractConsumer(String msgConfigId, List<String> noticeEmpIdList, Integer delayTime) {
        UserInfo userInfo = UserContext.preCheckUser();
        String tenantId = userInfo.getTenantId();
        Long userId = userInfo.getUserId();
        for (String empId : noticeEmpIdList) {
            sendMqMsg(msgConfigId, empId, userId, tenantId, delayTime);
        }
    }

    private void sendMqMsg(String msgConfig, String empId, Long userId, String tenantId, Integer delayTime) {
        MsgNoticeDto messageDto = new MsgNoticeDto();
        messageDto.setTenantId(tenantId);
        messageDto.setUserId(userId);
        messageDto.setSubjects(Lists.list(empId));
        messageDto.setCreateTime(System.currentTimeMillis());
        messageDto.setExt(null);
        messageDto.setMsgFrom("empManualPush");
        messageDto.setType(0);
        messageDto.setMsgConfig(msgConfig);
        msgPublish.msgNoticePublish(messageDto, delayTime);
    }

    private void doSendExternalMsg(List<TemplateVo> msgTptList, MsgConfigDo msgConfigData, List<String> channels, long delayTime) {
        List<TemplateVo> emailMsgTptList = msgTptList.stream()
                .filter(o -> null != o.getCategory() && "1".equals(o.getCategory().getValue())).collect(Collectors.toList());
        if (null == emailMsgTptList || emailMsgTptList.isEmpty()) {
            return;
        }

        String userIdStr = UserContext.getUserId();
        Long userId = null == userIdStr ? 0L : Long.valueOf(userIdStr);
        emailMsgTptList.forEach(template -> {
            if (StringUtil.isEmpty(template.getExternalMail())) {
                return;
            }

            String email = template.getExternalMail();
            email = null == email ? "" : email;
            List<String> externalMailList = Arrays.asList(email.split(","));
            email = template.getCopyMail();
            email = null == email ? "" : email;
            List<String> ccList = Arrays.asList(email.split(","));
            email = template.getBlindCopyMail();
            email = null == email ? "" : email;
            List<String> bccList = Arrays.asList(email.split(","));
            // 设置消息通知内容
            MsgDto msgDto = new MsgDto();
            doSetEmailMsgDto(msgDto, template, Joiner.on(";").join(externalMailList), StringUtils.join(ccList, ","),StringUtils.join(bccList, ","));
            msgDto.setUserId(userId);
            msgDto.setChannel(channels);
            msgDto.setMsgConfig(msgConfigData.getBid());
            msgDto.setTenantId(msgConfigData.getTenantId());

            // 设置消息推送，把消息放到延时队列中，定时消费
            product.publishDelayMsg(msgDto, Math.toIntExact(delayTime));
        });
    }

    /**
     * 邮件通知
     *
     * @param msgTptList
     * @param empIds
     * @param sysEmpIds
     * @param guardianEmpIds
     * @param delayTime
     * @param channel
     */
    private void doSendEmail(List<TemplateVo> msgTptList, List<String> empIds, List<String> sysEmpIds, List<String> guardianEmpIds
            , long delayTime, List<String> channel, MsgConfigDo msgConfigData) {
        // 邮件通知模版
        List<TemplateVo> emailMsgTptList = msgTptList.stream()
                .filter(o -> null != o.getCategory() && "1".equals(o.getCategory().getValue())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(emailMsgTptList)) {
            log.info("doSendEmail msgTptList empty");
            return;
        }

        String userIdStr = UserContext.getUserId();
        Long userId = null == userIdStr ? 0L : Long.valueOf(userIdStr);

        List<String> allGuardianEmailList = new ArrayList<>();
        Map<String, String> allEmpPriEmailMap = new HashMap<>();
        Map<String, String> allEmpCorpEmailMap = new HashMap<>();

        List<List<String>> empIdLists = ListUtil.split(empIds, 500);
        for (List<String> empIdList : empIdLists) {
            // 查询员工个人信息
            String empIdStr = Joiner.on(",").join(empIdList);
            List<EmpPrivateInfoVo> privateInfoList = masterdataEmpRepository.loadPrivateInfoList(empIdStr).getData();
            if (!CollectionUtils.isEmpty(privateInfoList)) {
                // 监护人邮箱
                List<String> guardianEmails = privateInfoList.stream()
                        .filter(o -> guardianEmpIds.contains(o.getEmpId()) && StringUtils.isNotEmpty(o.getGuardianEmail()))
                        .map(EmpPrivateInfoVo::getGuardianEmail).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(guardianEmails)) {
                    allGuardianEmailList.addAll(guardianEmails);
                }

                // 个人邮箱
                Map<String, String> empPriEmailMap = privateInfoList.stream()
                        .filter(o -> sysEmpIds.contains(o.getEmpId()) && StringUtils.isNotEmpty(o.getEmail()))
                        .collect(Collectors.toMap(EmpPrivateInfoVo::getEmpId, EmpPrivateInfoVo::getEmail, (o1, o2) -> o1));
                allEmpPriEmailMap.putAll(empPriEmailMap);
            }

            // 查询员工任职信息
            List<EmpWorkInfoDto> workInfoList = masterdataEmpRepository.loadEmpList(empIdStr).getData();
            if (!CollectionUtils.isEmpty(workInfoList)) {
                // 企业邮箱
                Map<String, String> empCorpEmailMap = workInfoList.stream()
                        .filter(o -> sysEmpIds.contains(o.getEmpId()) && StringUtils.isNotEmpty(o.getCompanyEmail()))
                        .collect(Collectors.toMap(EmpWorkInfoDto::getEmpId, EmpWorkInfoDto::getCompanyEmail, (o1, o2) -> o1));
                allEmpCorpEmailMap.putAll(empCorpEmailMap);
            }
        }

        for (TemplateVo template : emailMsgTptList) {
            // 邮件
            List<String> toEmails = new ArrayList<>();

            // 监护人邮箱
            if (!CollectionUtils.isEmpty(allGuardianEmailList)) {
                toEmails.addAll(allGuardianEmailList);
            }

            // 员工个人邮箱
            if (!CollectionUtils.isEmpty(sysEmpIds)) {
                for (String empId : sysEmpIds) {
                    // 获取任职、个人信息
                    String noticeEmail = getNoticeEmail(template, allEmpCorpEmailMap.get(empId), allEmpPriEmailMap.get(empId));
                    if (StringUtils.isNotEmpty(noticeEmail)) {
                        toEmails.add(noticeEmail);
                    }
                }
            }

            // 外部人员邮箱
            if (StringUtil.isNotEmpty(template.getExternalMail())) {
                toEmails.addAll(Arrays.asList(template.getExternalMail().split(",")));
            }

            if (CollectionUtils.isEmpty(toEmails)) {
                log.info("doSendEmail toEmails empty");
                continue;
            }

            // 邮箱去重
            Set<String> emailSet = new HashSet<>(toEmails);
            toEmails.clear();
            toEmails.addAll(emailSet);

            // 抄送对象
            List<String> ccList = handleCCEmail(empIds, template, toEmails);
            List<String> bccList = handleBCCEmail(empIds, template, toEmails);

            List<List<String>> toEmailLists = ListUtil.split(toEmails, 200);
            MsgDto msgDto;
            for (List<String> toEmailList : toEmailLists) {
                // 设置消息通知内容
                msgDto = new MsgDto();
                doSetEmailMsgDto(msgDto, template, Joiner.on(";").join(toEmailList), StringUtils.join(ccList, ","),StringUtils.join(bccList, ","));
                msgDto.setUserId(userId);
                msgDto.setChannel(channel);
                msgDto.setMsgConfig(msgConfigData.getBid());
                msgDto.setTenantId(msgConfigData.getTenantId());

                // 设置消息推送，把消息放到延时队列中，定时消费
                product.publishDelayMsg(msgDto, Math.toIntExact(delayTime));
            }
        }
    }

    @NotNull
    private List<String> handleCCEmail(List<String> empIds, TemplateVo template, List<String> toEmails) {
        List<String> ccList = new ArrayList<>();
        List<EmpWorkInfoDto> hrbpWorkInfos = null;
        boolean includeHrbp = true;
        Set<String> empIdSet = new HashSet<>(empIds);
        for (String empId : empIds) {
            List<String> empCcList = getCcEmail(template, hrbpWorkInfos, empIdSet, empId, includeHrbp);
            ccList.addAll(empCcList);
        }
        if (!CollectionUtils.isEmpty(ccList)) {
            Set<String> ccSet = new HashSet<>(ccList);
            ccList.clear();
            ccList.addAll(ccSet);
            ccList.removeIf(toEmails::contains);
        }
        return ccList;
    }


    @NotNull
    private List<String> handleBCCEmail(List<String> empIds, TemplateVo template, List<String> toEmails) {
        List<String> bccList = new ArrayList<>();
        List<EmpWorkInfoDto> hrbpWorkInfos = null;
        boolean includeHrbp = true;
        Set<String> empIdSet = new HashSet<>(empIds);
        for (String empId : empIds) {
            List<String> empCcList = getBCcEmail(template, hrbpWorkInfos, empIdSet, empId, includeHrbp);
            bccList.addAll(empCcList);
        }
        if (!CollectionUtils.isEmpty(bccList)) {
            Set<String> ccSet = new HashSet<>(bccList);
            bccList.clear();
            bccList.addAll(ccSet);
            bccList.removeIf(toEmails::contains);
        }
        return bccList;
    }

    private List<String> getEmpPhoneList(List<String> empIds, List<String> sysEmpIds, List<String> guardianEmpIds) {
        List<String> phoneList = new ArrayList<>();

        List<List<String>> empIdLists = ListUtil.split(empIds, 500);
        for (List<String> empIdList : empIdLists) {
            // 查询员工个人信息
            List<EmpPrivateInfoVo> privateInfoList = masterdataEmpRepository.loadPrivateInfoList(
                    Joiner.on(",").join(empIdList)).getData();
            if (CollectionUtils.isEmpty(privateInfoList)) {
                continue;
            }

            // 员工个人手机号
            if (!CollectionUtils.isEmpty(sysEmpIds)) {
                List<String> personalPhones = privateInfoList.stream()
                        .filter(o -> sysEmpIds.contains(o.getEmpId()) && null != o.getPhone())
                        .map(phone -> phone.getPhone())
                        .map(phoneValue -> phoneValue.getValue().trim()).collect(Collectors.toList());
                phoneList.addAll(personalPhones);
            }

            // 监护人手机号
            if (!CollectionUtils.isEmpty(guardianEmpIds)) {
                List<String> guardianPhones = privateInfoList.stream()
                        .filter(o -> guardianEmpIds.contains(o.getEmpId()) && null != o.getGuardianPhone())
                        .map(phone -> phone.getPhone())
                        .map(phoneValue -> phoneValue.getValue().trim()).collect(Collectors.toList());
                phoneList.addAll(guardianPhones);
            }
        }

        // 手机号去重
        if (!CollectionUtils.isEmpty(phoneList)) {
            Set<String> phoneSet = new HashSet<>(phoneList);
            phoneList.clear();
            phoneList.addAll(phoneSet);
        }
        return phoneList;
    }

    /**
     * 短信通知
     *
     * @param msgTptList
     * @param empIds
     * @param sysEmpIds
     * @param guardianEmpIds
     * @param delayTime
     * @param channel
     */
    private void doSendSms(List<TemplateVo> msgTptList, List<String> empIds, List<String> sysEmpIds, List<String> guardianEmpIds,
                           long delayTime, List<String> channel, MsgConfigDo msgConfigData) {
        // 短信通知模版
        List<TemplateVo> smsMsgTptList = msgTptList.stream()
                .filter(o -> null != o.getCategory() && "0".equals(o.getCategory().getValue())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(smsMsgTptList)) {
            log.info("doSendSms msgTptList empty");
            return;
        }

        List<String> phoneList = getEmpPhoneList(empIds, sysEmpIds, guardianEmpIds);

        if (CollectionUtils.isEmpty(phoneList)) {
            log.info("doSendSms phoneList empty");
            return;
        }
        String userIdStr = UserContext.getUserId();
        Long userId = null == userIdStr ? 0L : Long.valueOf(userIdStr);
        MsgDto msgDto;
        for (TemplateVo template : smsMsgTptList) {
            msgDto = new MsgDto();
            msgDto.setUserId(userId);
            msgDto.setChannel(channel);
            msgDto.setMsgConfig(msgConfigData.getBid());
            msgDto.setTenantId(msgConfigData.getTenantId());

            List<List<String>> phoneLists = ListUtil.split(phoneList, 500);
            for (List<String> list : phoneLists) {
                doSetMessageDto(msgDto, template, list);
                // 设置消息推送，把消息放到延时队列中，定时消费
                product.publishDelayMsg(msgDto, Math.toIntExact(delayTime));
            }
        }
    }

    private void doAnalysisTemplateInfo(List<TemplateVo> templateDos, String empId) {
        for (TemplateVo templateDo : templateDos) {
            templateAnalysisService.analysisTemplateInfo(templateDo, empId, null, 0);
            log.info("analysis template:{}", FastjsonUtil.toJson(templateDo));
        }
    }

    public String getNoticeEmail(TemplateVo template, String companyEmail, String personalEmail) {
        // 获取邮件通知规则
        if (null == template || (StringUtils.isEmpty(companyEmail) && StringUtils.isEmpty(personalEmail))) {
            return "";
        }
        if (null != template.getNoticeRule() &&
                NoticeRuleEnum.PERSONAL_EMAIL.getIndex().equals(template.getNoticeRule())) {
            // 个人邮箱: 优先发个人邮箱，无个人邮箱发企业邮箱
            return StringUtils.isNotEmpty(personalEmail) ? personalEmail : companyEmail;
        } else {
            // 企业邮箱：优先发企业邮箱，无企业邮箱发个人邮箱
            return StringUtils.isNotEmpty(companyEmail) ? companyEmail : personalEmail;
        }
    }

    public String getNoticeEmail(TemplateVo template, EmpWorkInfoDto empWorkInfo, EmpPrivateInfoVo empPrivateInfo) {
        // 获取邮件通知规则
        if (null == template || (null == empWorkInfo && null == empPrivateInfo)) {
            return "";
        }
        String companyEmail = null != empWorkInfo ? empWorkInfo.getCompanyEmail() : "";
        String personalEmail = null != empPrivateInfo ? empPrivateInfo.getEmail() : "";
        if (null != template.getNoticeRule() &&
                NoticeRuleEnum.PERSONAL_EMAIL.getIndex().equals(template.getNoticeRule())) {
            // 个人邮箱: 优先发个人邮箱，无个人邮箱发企业邮箱
            return StringUtils.isNotEmpty(personalEmail) ? personalEmail : companyEmail;
        } else {
            // 企业邮箱：优先发企业邮箱，无企业邮箱发个人邮箱
            return StringUtils.isNotEmpty(companyEmail) ? companyEmail : personalEmail;
        }
    }

    public String getNoticeEmail(TemplateVo template, EmpWorkInfoDto empWorkInfo, Set<String> excludeEmpIds) {
        // 获取邮件通知规则
        if (null == template || null == empWorkInfo) {
            return "";
        }

        if (!CollectionUtils.isEmpty(excludeEmpIds) && excludeEmpIds.contains(empWorkInfo.getEmpId())) {
            return "";
        }
        String companyEmail = empWorkInfo.getCompanyEmail();
        String personalEmail = "";
        // 查询个人信息
        EmpPrivateInfoVo empPrivateInfo = getEmpPrivateInfo(empWorkInfo.getEmpId());
        if (null != empPrivateInfo) {
            personalEmail = empPrivateInfo.getEmail();
        }
        if (null != template.getNoticeRule() &&
                NoticeRuleEnum.PERSONAL_EMAIL.getIndex().equals(template.getNoticeRule())) {
            // 个人邮箱: 优先发个人邮箱，无个人邮箱发企业邮箱
            return StringUtils.isNotEmpty(personalEmail) ? personalEmail : companyEmail;
        } else {
            // 企业邮箱：优先发企业邮箱，无企业邮箱发个人邮箱
            return StringUtils.isNotEmpty(companyEmail) ? companyEmail : personalEmail;
        }
    }

    public List<String> getNoticeEmailListByNoticeRule(TemplateVo template,
                                                       List<EmpWorkInfoDto> empWorkInfoDtoList, Set<String> excludeEmpIds) {
        // 获取邮件通知规则
        if (null == template || CollectionUtils.isEmpty(empWorkInfoDtoList)) {
            return null;
        }

        List<String> empList = empWorkInfoDtoList.stream().map(EmpWorkInfoDto::getEmpId).distinct().collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(excludeEmpIds)) {
            empList.removeIf(excludeEmpIds::contains);
        }

        if (CollectionUtils.isEmpty(empList)) {
            return null;
        }

        // todo 查询个人信息 性能待优化
        String empIdStr = Joiner.on(",").join(empList);
        List<EmpPrivateInfoVo> privateInfoList = masterdataEmpRepository.loadPrivateInfoList(empIdStr).getData();
        Map<String, EmpPrivateInfoVo> empPrivateInfoVoMap = privateInfoList.stream().
                collect(Collectors.toMap(EmpPrivateInfoVo::getEmpId, Function.identity(), (o1, o2) -> o1));

        List<String> emailList = null;
        if (null != template.getNoticeRule() &&
                NoticeRuleEnum.PERSONAL_EMAIL.getIndex().equals(template.getNoticeRule())) {
            // 个人邮箱: 优先发个人邮箱，无个人邮箱发企业邮箱
            emailList = empWorkInfoDtoList.stream().map(o -> {
                if (empPrivateInfoVoMap.containsKey(o.getEmpId()) &&
                        StringUtils.isNotEmpty(empPrivateInfoVoMap.get(o.getEmpId()).getEmail())) {
                    return empPrivateInfoVoMap.get(o.getEmpId()).getEmail();
                }
                return o.getCompanyEmail();
            }).collect(Collectors.toList());
        } else {
            // 企业邮箱：优先发企业邮箱，无企业邮箱发个人邮箱
            emailList = empWorkInfoDtoList.stream().map(o -> {
                if (StringUtils.isNotEmpty(o.getCompanyEmail())) {
                    return o.getCompanyEmail();
                } else if (empPrivateInfoVoMap.containsKey(o.getEmpId()) &&
                        StringUtils.isNotEmpty(empPrivateInfoVoMap.get(o.getEmpId()).getEmail())) {
                    return empPrivateInfoVoMap.get(o.getEmpId()).getEmail();
                }
                return "";
            }).collect(Collectors.toList());
        }

        return emailList.stream().filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
    }

    /**
     * 查询个人信息
     *
     * @param empId
     * @return
     */
    public EmpPrivateInfoVo getEmpPrivateInfo(String empId) {
        Result<EmpPrivateInfoVo> result = masterdataFeignClient.getPrivateInfo(empId);
        EmpPrivateInfoVo empPrivateInfo = null;
        if (null == result || !result.isSuccess() || null == (empPrivateInfo = result.getData())) {
            return empPrivateInfo;
        }

        return empPrivateInfo;
    }

    private List<String> getCcEmail(TemplateVo template, List<EmpWorkInfoDto> hrbpWorkInfos,
                                    Set<String> empIdSet, String empId, boolean includeHrbp) {
        // 抄送对象
        return handleTargetEmail(template, hrbpWorkInfos, empIdSet, empId, includeHrbp, template.getCopyObject(),template.getCopyMail());
    }

    private List<String> getBCcEmail(TemplateVo template, List<EmpWorkInfoDto> hrbpWorkInfos,
            Set<String> empIdSet, String empId, boolean includeHrbp) {
        // 抄送对象
        return handleTargetEmail(template, hrbpWorkInfos, empIdSet, empId, includeHrbp, template.getBlindCopyObject(),template.getBlindCopyMail());
    }

    private List<String> handleTargetEmail(TemplateVo template, List<EmpWorkInfoDto> hrbpWorkInfos, Set<String> empIdSet, String empId, boolean includeHrbp, List<String> copyObject,String email) {
        List<String> ccList = new ArrayList<>();
        if (StringUtil.isNotEmpty(copyObject)) {
            List<String> roleIdList = Lists.list();
            for (String str : copyObject) {
                if (StringUtils.isBlank(str)) {
                    continue;
                }
                switch (str) {
                    case "hrbp":
                        if (!includeHrbp) {
                            break;
                        }
                        if (null == hrbpWorkInfos) {
                            hrbpWorkInfos = getHrbpWorkInfoDto();
                        }
                        List<String> noticeEmailList = getNoticeEmailListByNoticeRule(template, hrbpWorkInfos, empIdSet);
                        if (CollectionUtils.isEmpty(noticeEmailList)) {
                            break;
                        }
                        ccList.addAll(noticeEmailList);
                        includeHrbp = false;
                        break;
                    case "leader":
                        EmpWorkInfoDto leaderWorkInfo = getLeaderWorkInfoDtoByEmpId(empId);
                        String leaderEmail = getNoticeEmail(template, leaderWorkInfo, empIdSet);
                        doAddCcObjToList(ccList, leaderEmail, empId);
                        break;
                    case "orgLeader":
                        EmpWorkInfoDto orgLeaderWorkInfo = getOrgLeaderWorkInfoDto(empId);
                        String orgLeaderEmail = getNoticeEmail(template, orgLeaderWorkInfo, empIdSet);
                        doAddCcObjToList(ccList, orgLeaderEmail, empId);
                        break;
                    default:
                        if (str.contains("Role$")) {
                            roleIdList.add(str.replace("Role$", ""));
                        }
                        break;
                }
            }
            if (CollectionUtils.isEmpty(roleIdList)) {
                var roleUserQuery = new RoleUserQueryDto();
                roleUserQuery.setRoleIds(roleIdList);
                var queryPage = new QueryPageBean();
                queryPage.setPageNo(1);
                queryPage.setPageSize(500);
                roleUserQuery.setQueryPageBean(queryPage);
                queryEmailOnAuth(ccList, roleUserQuery);
            }
        }
        // 抄送邮箱
        if (StringUtil.isNotEmpty(email)) {
            ccList.addAll(Arrays.asList(email.split(",")));
        }
        return ccList;
    }

    /**
     * 查询权限角色下的邮箱
     *
     * @param ccList
     * @param roleUserQuery
     */
    private void queryEmailOnAuth(List<String> ccList, RoleUserQueryDto roleUserQuery) {
        var result = authServiceFeignClient.getUserInfoByRoles(roleUserQuery);
        if (!result.isSuccess()) {
            log.warn("request auth fail, ccList={} roleUserQuery={}", FastjsonUtil.toJson(ccList), FastjsonUtil.toJson(roleUserQuery));
            return;
        }
        if (!CollectionUtils.isEmpty(result.getData().getItems())) {
            var emailList = result.getData().getItems().stream().map(e -> e.getEmail())
                    .filter(e -> StringUtils.isNotBlank(e)).collect(Collectors.toList());
            var isAdd = CollectionUtils.isEmpty(emailList) ? false : ccList.addAll(emailList);
            emailList = null;
            var queryPageBean = roleUserQuery.getQueryPageBean();
            queryPageBean.setPageNo(queryPageBean.getPageNo() + 1);
            queryEmailOnAuth(ccList, roleUserQuery);
        }
    }

    private void doAddCcObjToList(List<String> ccList, String email, String empId) {
        if (StringUtils.isEmpty(email) || "null".equals(email)) {
            log.warn("MsgConfigService.doAddCcObjToList empId = {} email={} err", empId, email);
            return;
        }
        ccList.add(email);
    }

    private void doSetMessageDto(MsgDto msgDto, TemplateVo templateDo, List<String> phones) {
        if (CollectionUtils.isEmpty(phones)) {
            return;
        }

        // 手机号去重
        Set<String> phoneSet = new HashSet<>(phones);
        phones.clear();
        phones.addAll(phoneSet);

        SmsMessageDto message = new SmsMessageDto();
        message.setContent(templateDo.getContent());
        List<SmsMessageDto.MobileDto> mobiles =
                phones.stream().map(phone -> {
                    SmsMessageDto.MobileDto mobile = new SmsMessageDto.MobileDto();
                    mobile.setMobile(phone);
                    return mobile;
                }).collect(Collectors.toList());
        message.setMobile(mobiles);
        log.info("doSetMessageDto:{}", FastjsonUtil.toJson(message));
        msgDto.setMessage(message);
    }

    private void doSetEmailMsgDto(MsgDto msgDto, TemplateVo templateDo, String to, String cc, String bcc) {
        EmailMsgDto emailMsgDto = new EmailMsgDto();
        emailMsgDto.setContent(templateDo.getContent());
        emailMsgDto.setSubject(templateDo.getTitle());
        emailMsgDto.setCc(cc);
        emailMsgDto.setBcc(bcc);
        emailMsgDto.setTo(to);
        if (null != templateDo.getAttachFile() && !CollectionUtils.isEmpty(templateDo.getAttachFile().getUrls())) {
            Attachment attachment = templateDo.getAttachFile();
            emailMsgDto.setAffixName(StringUtils.join(attachment.getNames(), ","));
            emailMsgDto.setAffix(StringUtils.join(attachment.getUrls(), ","));
        }
        log.info("doSetEmailMsgDto:{}", FastjsonUtil.toJson(emailMsgDto));
        msgDto.setEmailMsg(emailMsgDto);
    }

    @Deprecated
    private EmpWorkInfoDto getHrbpWorkInfoDto(String empId) {
        Result<EmpWorkInfoDto> hrbpResult = masterdataFeignClient.hrbpByEmpId(empId, System.currentTimeMillis());
        if (null != hrbpResult && null != hrbpResult.getData()) {
            return hrbpResult.getData();
        }
        return null;
    }

    private EmpWorkInfoDto getOrgLeaderWorkInfoDto(String empId) {
        Result<EmpWorkInfoDto> orgLeaderResult = masterdataEmpRepository.loadOrgLeader(empId, System.currentTimeMillis());
        if (null != orgLeaderResult && null != orgLeaderResult.getData()) {
            return orgLeaderResult.getData();
        }
        return null;
    }

    private List<EmpWorkInfoDto> getLeaderWorkInfoDto() {
        Result<List<EmpWorkInfoDto>> leaderResult = masterdataEmpRepository.loadAllLeader(System.currentTimeMillis());
        if (null != leaderResult && null != leaderResult.getData()) {
            return leaderResult.getData();
        }
        return null;
    }

    private EmpWorkInfoDto getLeaderWorkInfoDtoByEmpId(String empId) {
        Result<EmpWorkInfoDto> leaderResult = masterdataEmpRepository.loadLeader(empId, System.currentTimeMillis());
        if (null != leaderResult && null != leaderResult.getData()) {
            return leaderResult.getData();
        }
        return null;
    }

    private List<EmpWorkInfoDto> getHrbpWorkInfoDto() {
        Result<List<EmpWorkInfoDto>> hrbpResult = masterdataEmpRepository.loadAllHrbp(System.currentTimeMillis());
        if (null != hrbpResult && null != hrbpResult.getData()) {
            return hrbpResult.getData();
        }
        return null;
    }

    private long getDelayTime(long publisDate, Integer sendTime) {
        // 判断推送时间是否小于当前时间，如小于当前时间则立即推送消息，反之：判断推送日期是否为当天，如果是当天，则将消息推送到延迟队列
        sendTime = null != sendTime ? sendTime : 0;
        long realSendTimeMillis = publisDate + sendTime;
        long nowTimeMillis = System.currentTimeMillis();
        if (realSendTimeMillis <= nowTimeMillis) {
            return 0;
        }
        return realSendTimeMillis - nowTimeMillis;
    }

    public void refresh(String bid) {
        if (StringUtil.isEmpty(bid)) {
            conditionTaskService.templateConditionJobHandler();
            return;
        }
        MsgConfigQueryDto query = new MsgConfigQueryDto();
        conditionTaskService.doMsgTemplate(UserContext.getTenantId(), null, query);
    }

    public MsgConfigDetailVo getDetail(String bid) {
        MsgConfigDo data = domainService.getById(bid);
        List<TemplateDo> templateList = templateDomainService.getByConfig(data);

        MsgConfigDetailVo vo = ObjectConvertUtil.convert(data, MsgConfigDetailVo.class,(t1,v1)->{
            if (t1.getI18nName()!=null){
                v1.setI18nName(FastjsonUtil.toObject(t1.getI18nName(),Map.class));
            }
            Map<String,Object> i18nName=new HashMap<>();
            i18nName.put("default",t1.getName());
            v1.setI18nName(i18nName);
        });

        vo.setCondition(FastjsonUtil.toObject(data.getCondition(), ConditionTree.class));
        vo.setTemplates(TemplateVo.toVoList(templateList));
        // 通知对象多选返回
        if (data.getNotifyObject() != null) {
            vo.setNotifyObject(FastjsonUtil.toObject(data.getNotifyObject(), List.class));
        }
        vo.setMsgType(NoticeType.getEnumByIndex(data.getMsgType().getValue()));
        if (data.getRule() != null) {
            vo.setRule(NoticeRule.getByName(data.getRule().getValue()));
        }
        vo.setRound(data.getRound().getValue());
        vo.setFunc(data.getFunc().getValue());
        vo.setChannel(FastjsonUtil.toObject(data.getChannel(), List.class));
        if (data.getSendTime() != null) {
            vo.setSendTime((int) ((DateUtil.getCurrentTimestamp() + data.getSendTime()) / 1000));
        }
        if (data.getMergeRule() != null) {
            vo.setMergeRule(ObjectConverter.convert(data.getMergeRule(), MergeRuleVo.class));
            Integer mergeTime = data.getMergeRule().getMergeTime();
            if (mergeTime != null) {
                vo.getMergeRule().setMergeTime((int) ((DateUtil.getCurrentTimestamp() + mergeTime) / 1000));
            }
        }
        return vo;
    }

    public void copy(String bid) {
        MsgConfigDto msgConfigDto = doInitMsgConfigDto(bid);
        msgConfigDto.setBid(null);
        saveOrUpdateObj(msgConfigDto);
    }

    private MsgConfigDto doInitMsgConfigDto(String bid) {
        MsgConfigDetailVo detail = getDetail(bid);

        MsgConfigDto msgConfigDto = ObjectConverter.convert(detail, MsgConfigDto.class);
        msgConfigDto.setTemplates(ObjectConverter.convertList(detail.getTemplates(), TemplateDto.class));

        return msgConfigDto;
    }

    public void enable(String bid) {
        domainService.enable(bid);
        doCacheMsgConfig(bid);
        // 通知种类为手动推送时，触发消息推送
        doStartManualPushMsg(bid);
    }

    public void doStartManualPushMsg(String bid) {
        doManualPushMsg(doInitMsgConfigDto(bid));
    }

    public void disable(String bid) {
        domainService.disable(bid);
        doCacheMsgConfig(bid);
    }

    public List<KeyValue> getCopyTarget() {
        List<KeyValue> collect = Arrays.stream(EmailCopyTarget.values()).filter(o -> EmailCopyTarget.ORG_LEADER != o).map(type -> {
            KeyValue kv = new KeyValue();
            kv.setText(type.getName());
            kv.setValue(type);
            return kv;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            collect = Lists.list();
        }
        Result<List<KeyValue>> allRoleResult = authServiceFeignClient.getAllRole();
        if (allRoleResult.isSuccess() && !CollectionUtils.isEmpty(allRoleResult.getData())) {
            allRoleResult.getData().forEach(e -> {
                e.setValue(String.format("Role$%s", e.getValue()));
            });
            collect.addAll(allRoleResult.getData());
        }
        //查询审批人
        List<KeyValue> dataList = hrFeignClient.getApproverSettings().getData().stream().map(it -> new KeyValue(it.get("name").toString(), it.get("bid")))
                .collect(Collectors.toList());
        List<KeyValue> newList = dataList.stream().map(keyValue -> {
            keyValue.setValue(String.format(CacheCodeConstant.NOTIFY_OBJECT_WORKFLOW_APPROVER_PREFIX, keyValue.getValue()));
            return keyValue;
        }).collect(Collectors.toList());
        collect.addAll(newList);
        return collect;
    }

    public List<MsgConfigListVo> toVoList(List<MsgConfigDo> list) {
        return toVoList(list, new HashMap<>());
    }

    public List<MsgConfigListVo> toVoList(List<MsgConfigDo> list, Map<String, String> notifyObjectMap) {
        return list.stream().map(d -> {
            MsgConfigListVo vo = ObjectConvertUtil.convert(d, MsgConfigListVo.class,(t1,v1)->{
                if (t1.getI18nName()!=null){
                    v1.setI18nName(FastjsonUtil.toObject(t1.getI18nName(),Map.class));
                }else {
                    Map<String,Object> map=new HashMap<>();
                    map.put("default",t1.getName());
                    v1.setI18nName(map);
                }
            });
            if (null != d.getMsgType() && !StringUtils.isEmpty(d.getMsgType().getValue())) {
                NoticeType noticeType = NoticeType.getEnumByIndex(d.getMsgType().getValue());
                vo.setMsgType(null != noticeType ? noticeType.getName() : "");
                vo.setMsgTypeValue(noticeType != null ? noticeType.getIndex() : "");
            }
            vo.setFunc(d.getFunc().getText());

            if (d.getNotifyObject() != null) {
                List<String> notify = FastjsonUtil.toObject(d.getNotifyObject(), List.class);
                vo.setNotifyObject(StringUtils.join(notify.stream()
                        .map(objStr -> NoticeTarget.getNameByEnumNameOrMap(objStr, notifyObjectMap))
                        .collect(Collectors.toList()), ","));
            }

            List<String> channel = FastjsonUtil.toObject(d.getChannel(), List.class);
            vo.setChannel(StringUtils.join(channel.stream()
                    .map(MsgCategory::getAliasByCode)
                    .collect(Collectors.toList()), ","));


            NotificationCycleTypeEnum type = NotificationCycleTypeEnum.getByValue(d.getRound().getValue());
            if (type == null) {
                log.warn("round of msg config is empty");
                return vo;
            }
            vo.setDay(type.format(d.getDay(), UNIT));
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 验证上传的员工信息；
     *
     * @param dataList
     * @return
     */
    public MsgImportCheckVo importCheck(List<MsgImportDto> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return new MsgImportCheckVo();
        }

        dataList = dataList.stream()
                .filter(item -> null != item && (StringUtil.isNotEmpty(item.getName())
                        || StringUtil.isNotEmpty(item.getWorkno()))).collect(Collectors.toList());
        //失败案例；
        List<MsgEmpCheckDto> failTextList = new ArrayList<>(dataList.size());

        //成功案例；
        List<MsgImportDto> successDataList = dataList.stream()
                .filter(o -> StringUtils.isNotEmpty(o.getWorkno()))
                .collect(Collectors.toList());

        checkWorkNo(dataList, failTextList);

        //员工校验；
        List<String> worknoList = successDataList.stream()
                .map(MsgImportDto::getWorkno)
                .collect(Collectors.toList());

        Result<List<MsgImportDto>> empList =
                null == worknoList || worknoList.isEmpty() ? null : hrFeignClient.getEmpListByWorkno(worknoList);
        List<MsgImportDto> empIds = null;

        if (null == empList || !empList.isSuccess() || null == (empIds = empList.getData()) || empIds.isEmpty()) {
            List<MsgEmpCheckDto> empEmptyList = ObjectConverter.convertList(successDataList, MsgEmpCheckDto.class);
            failTextList.addAll(empEmptyList);
            return new MsgImportCheckVo().setFailTextList(failTextList);
        }

        List<String> empIdList = new ArrayList<>();
        List<MsgEmpCheckDto> successTextList = new ArrayList<>(successDataList.size());

        Map<MsgImportDto, MsgImportDto> queryEmpMap = empIds.stream()
                .collect(toMap(d -> d, d -> d, (a, b) -> a));

        successDataList.forEach(data -> {
            MsgEmpCheckDto dto = ObjectConverter.convert(data, MsgEmpCheckDto.class);
            MsgImportDto hrData;
            if ((hrData = queryEmpMap.get(data)) != null) {
                empIdList.add(hrData.getEmpId());
                dto.setEmpId(hrData.getEmpId());
                successTextList.add(dto);
            } else {
                failTextList.add(dto);
            }
        });

        List<MsgEmpCheckDto> failList = failTextList.stream().distinct().collect(Collectors.toList());
        List<String> empLists = empIdList.stream().distinct().collect(Collectors.toList());
        List<MsgEmpCheckDto> successList = successTextList.stream().distinct().collect(Collectors.toList());

        return new MsgImportCheckVo()
                .setFailTextList(failList)
                .setEmpList(empLists)
                .setSucessTextList(successList);
    }

    private void checkWorkNo(List<MsgImportDto> dataList, List<MsgEmpCheckDto> failTextList) {
        //workNo校验；
        List<String> failWorkNo = dataList.stream()
                .filter(msgImportDto -> StringUtil.isEmpty(msgImportDto.getWorkno()))
                .map(MsgImportDto::getName)
                .collect(Collectors.toList());
        if (!failWorkNo.isEmpty()) {
            List<MsgEmpCheckDto> failDto = failWorkNo.stream().map(name -> new MsgEmpCheckDto().setName(name)).collect(Collectors.toList());
            failTextList.addAll(failDto);
        }
    }

}
