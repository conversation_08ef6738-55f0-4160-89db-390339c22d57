package com.caidaocloud.message.service.domain.msg.repository;

import com.caidaocloud.message.service.domain.base.repository.BaseRepository;
import com.caidaocloud.message.service.domain.msg.entity.AssignEmpDo;
import com.caidaocloud.message.service.domain.msg.entity.MsgConfigDo;

import java.util.List;

/**
 * AssignEmpRepository
 *
 * <AUTHOR>
 * @date 2022/6/6 下午5:49
 */
public interface AssignEmpRepository extends BaseRepository<AssignEmpDo> {

    void deleteByConfigId(String identifier, String config);

    void batchInsert(String identifier, List<AssignEmpDo> dataList);
}
