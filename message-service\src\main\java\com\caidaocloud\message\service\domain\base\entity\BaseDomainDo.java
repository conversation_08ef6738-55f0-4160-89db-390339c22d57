package com.caidaocloud.message.service.domain.base.entity;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;

import java.util.List;

public interface BaseDomainDo<T> {
    BaseRepository<T> getRepository();

    T queryDetailByTenantId();

    T getByBid(String bid);

    PageResult<T> getPage(BasePage query);

    void deleteByBid(String bid);

    default String getDoIdentifier(){
        return null;
    }

    T getDetail(T t);

    List<T> selectByIds(List<String> ids);

    int deleteBatchIds(List<String> ids);

    List<T> selectList();
}
