package com.caidaocloud.message.service.domain.msg.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.message.service.domain.base.entity.BaseDomainDo;
import com.caidaocloud.message.service.domain.base.service.BaseDomainServiceImpl;
import com.caidaocloud.message.service.domain.msg.entity.EmpMsgTemplateRelDo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class EmpMsgTemplateRelDomainService extends BaseDomainServiceImpl<EmpMsgTemplateRelDo, BasePage> {
    @Resource
    private EmpMsgTemplateRelDo empMsgTemplateRelDo;

    @Override
    public BaseDomainDo<EmpMsgTemplateRelDo> getDoService() {
        return empMsgTemplateRelDo;
    }

    public List<EmpMsgTemplateRelDo> getByEmpIds(List<String> empList) {
        return empMsgTemplateRelDo.getByEmpIds(empList);
    }

    public void deleteTemplateId(String template) {
        empMsgTemplateRelDo.deleteTemplateId(template);
    }

    public List<String> getEmpList(String msgConfig, BasePage page) {
        return empMsgTemplateRelDo.selectByTemplate(msgConfig, page);
    }

    public List<String> getEmpListByEmpIds(String msgConfig, BasePage page, List<String> empIdList) {
        return empMsgTemplateRelDo.getEmpListByEmpIds(msgConfig, page, empIdList);
    }
}
