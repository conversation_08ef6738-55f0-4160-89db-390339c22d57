package com.caidaocloud.message.service.record.feign;


import com.caidaocloud.message.service.record.bean.UserAccountInfo;
import com.caidaocloud.message.service.record.config.EnableLogFeignConfig;
import com.caidaocloud.message.service.record.feign.callback.UserFeignFallback;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 获取员工信息
 * created by: FoAng
 * create time: 28/5/2024 4:48 下午
 */
@FeignClient(
        value = "caidaocloud-user-service",
        fallback = UserFeignFallback.class,
        configuration = EnableLogFeignConfig.class,
        contextId = "record-user-service"
)
public interface UserFeign {

    @GetMapping("/api/user/v1/getUserAndAccountInfo")
    Result<UserAccountInfo> getUserAndAccountInfo(@RequestParam("userId") Long userId);

}
