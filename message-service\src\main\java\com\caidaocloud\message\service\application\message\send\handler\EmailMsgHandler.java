package com.caidaocloud.message.service.application.message.send.handler;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.message.service.application.common.enums.NoticeRuleEnum;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.message.service.application.message.notifier.service.NotifyTargetFactory;
import com.caidaocloud.message.service.application.message.send.dto.MessageContext;
import com.caidaocloud.message.service.application.msg.dto.EmailMsgDto;
import com.caidaocloud.message.service.interfaces.vo.template.TemplateVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 根据模版生产邮件消息
 */
@Slf4j
@Service
public class EmailMsgHandler implements IMsgHandler {

    @Override
    public String getHandlerType() {
        return "1";
    }

    private static final ThreadLocal<MessageContext> CONTEXT_THREAD_LOCAL = new ThreadLocal<>();

    @Override
    public void doMsgHandler(MessageContext context) {
        try {
            CONTEXT_THREAD_LOCAL.set(context);

            // 抄送邮箱
            List<String> ccList = new ArrayList<>(),
                    bccList = new ArrayList<>(),
                    // 收件人邮箱
                    toEmails = new ArrayList<>();

            // 监护人邮箱
            guardianFilter(context.getOtherObjList(), toEmails);

            // 其他通知对象收件人邮箱
            empListFilter(context.getEmpList(), toEmails, context.getReceiverMap());

            TemplateVo template = context.getTemplate();

            // 抄送邮箱
            appendReceiver(template.getCopyMail(), ccList);

            // 密送邮箱
            appendReceiver(template.getBlindCopyMail(), bccList);

            // 外部人员邮箱
            appendReceiver(template.getExternalMail(), toEmails);

            // 处理抄送对象的邮箱
            List<NotifierDto> notifierDtos = loadCcListByTemplate(template, context.getNotify());
            notifierDtos.forEach(item -> {
                emailFilter(ccList, item);
            });
            // 处理抄送对象的邮箱
            List<NotifierDto> bccNotifierDtos = loadBccListByTemplate(template, context.getNotify());
            bccNotifierDtos.forEach(item -> {
                emailFilter(bccList, item);
            });

            // 抄送去重
            distinctList(ccList);
            // 密送去重
            distinctList(bccList);

            // 收件人邮箱去重
            distinctList(toEmails);

            // 抄送和收件人不能同时存在
            ccList.removeIf(toEmails::contains);
            bccList.removeIf(toEmails::contains);

            EmailMsgDto emailMsg = new EmailMsgDto();
            emailMsg.setContent(template.getContent());
            emailMsg.setSubject(template.getTitle());
            // 设置抄送邮箱
            emailMsg.setCc(convertEmailList(ccList));
            emailMsg.setBcc(convertEmailList(bccList));
            emailMsg.setTo(convertEmailList(toEmails));

            if (null != template.getAttachFile() && !CollectionUtils.isEmpty(template.getAttachFile().getUrls())) {
                Attachment attachment = template.getAttachFile();
                emailMsg.setAffixName(attachment.getNames().stream().collect(Collectors.joining(",")));
                emailMsg.setAffix(attachment.getUrls().stream().collect(Collectors.joining(",")));
            }

            log.info("send EmailMsg RequestData = {}", FastjsonUtil.toJson(emailMsg));
            context.getMsgDto().setEmailMsg(emailMsg);
        }finally {
            CONTEXT_THREAD_LOCAL.remove();
        }
    }

    protected String convertEmailList(List<String> emailList){
        if(null == emailList || emailList.isEmpty()){
            return null;
        }

        return emailList.stream().collect(Collectors.joining(";"));
    }

    protected void appendReceiver(String email, List<String> receiver){
        if(StringUtil.isEmpty(email)){
            return;
        }

        receiver.addAll(Arrays.asList(email.split(",")));
    }

    protected void empListFilter(Set<String> empSet, List<String> toEmails, Map<String, NotifierDto> receiverMap){
        empSet.forEach(sysEmpId -> {
            NotifierDto notifierDto = receiverMap.get(sysEmpId);
            if(null == notifierDto || (StringUtil.isEmpty(notifierDto.getEmail()) && StringUtil.isEmpty(notifierDto.getCompanyEmail()))){
                log.warn("sysEmpId={} is not exist Notifier={}", sysEmpId, FastjsonUtil.toJson(notifierDto));
                return;
            }

            emailFilter(toEmails, notifierDto);
        });
    }

    protected void guardianFilter(List<NotifierDto> guardianList, List<String> toEmails){
        if(null == guardianList || guardianList.isEmpty()){
            return;
        }

        guardianList.forEach(notifier -> {
            if(StringUtil.isEmpty(notifier.getEmail())){
                return;
            }

            emailFilter(toEmails, notifier);
        });
    }

    protected void emailFilter(List<String> emailList, NotifierDto item){
        String email = item.getCompanyEmail();
        // 邮件通知规则
        String noticeRule = CONTEXT_THREAD_LOCAL.get().getTemplate().getNoticeRule();

        // 公司邮箱
        if(NoticeRuleEnum.CORP_EMAIL.getIndex().equals(noticeRule)){
            email = StringUtil.isEmpty(email) ? item.getEmail() : email;
        } else if(NoticeRuleEnum.PERSONAL_EMAIL.getIndex().equals(noticeRule)){
            email = StringUtil.isEmpty(item.getEmail()) ? email : item.getEmail();
        }else {
            log.warn("notice rule is empty");
        }

        if(StringUtil.isNotEmpty(email)){
            emailList.add(email);
        }
    }

    protected List<NotifierDto> loadCcListByTemplate(TemplateVo template, NotifyObjectDto notify){
        var copyObjectList = template.getCopyObject();
        if(null == copyObjectList || copyObjectList.isEmpty()){
            return Lists.newArrayList();
        }

        List<String> notifyObject = copyObjectList.stream().collect(Collectors.toList());
        List<NotifierDto> distinctNotifier = NotifyTargetFactory.getDistinctNotifier(notifyObject, notify);
        return distinctNotifier;
    }

    protected List<NotifierDto> loadBccListByTemplate(TemplateVo template, NotifyObjectDto notify){
        var copyObjectList = template.getBlindCopyObject();
        if(null == copyObjectList || copyObjectList.isEmpty()){
            return Lists.newArrayList();
        }

        List<String> notifyObject = copyObjectList.stream().collect(Collectors.toList());
        List<NotifierDto> distinctNotifier = NotifyTargetFactory.getDistinctNotifier(notifyObject, notify);
        return distinctNotifier;
    }
}
