package com.caidaocloud.message.service.domain.email.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
public class EmailTemplateBatchDto {
    // from邮件
    private List<String> sendEmail = new ArrayList<String>();
    // from pwd
    private List<String> sendPwd = new ArrayList<String>();
    private List<String> sendName = new ArrayList<String>();
    private List<MultipartFile> files = new ArrayList<MultipartFile>();

    private List<Integer> fileCount = new ArrayList<Integer>();
    // 租户
    private List<String> tenantId = new ArrayList<String>();
    // 收件人
    private List<String> to = new ArrayList<String>();
    // 邮件主题
    private List<String> subject = new ArrayList<String>();
    // 邮件内容
    private List<String> content = new ArrayList<String>();
    /**
     * 多个附件用逗号分割
     * 附件地址
     */
    private List<String> affix = new ArrayList<String>();

    /**
     * 指定附件对应的名称，多个用逗号分割同affix一一对应，如果为空或对应下标上的值为空，则取文件名作为名称
     * 附件名称
     */
    private List<String> affixName = new ArrayList<String>();

    // 抄送人
    private List<String> cc = new ArrayList<String>();

    // 批量邮件的批次id
    private List<String> logEmailId = new ArrayList<String>();

    public void add(EmailTemplateDto dto){
        this.sendEmail.add(dto.getSendEmail());
        this.content.add(dto.getContent());
        this.subject.add(dto.getSubject());
        this.logEmailId.add(dto.getLogEmailId());
        if(null==dto.getFiles() || dto.getFiles().size() == 0){
            this.fileCount.add(0);
        }else {
            this.files.addAll(dto.getFiles());
            this.fileCount.add(dto.getFiles().size());
        }
        this.tenantId.add(dto.getTenantId());
        this.cc.add(dto.getCc());
        this.sendPwd.add(dto.getSendPwd());
        this.to.add(dto.getTo());
        this.affix.add(dto.getAffix());
        this.affixName.add(dto.getAffixName());
        this.sendName.add(dto.getSendName());
    }
    public List<EmailTemplateDto> toList(){
        List<EmailTemplateDto> list = new ArrayList<>();
        int index = 0;
        for(int i=0;i<this.sendEmail.size();++i){
            EmailTemplateDto dto = new EmailTemplateDto();
            List<MultipartFile> tempFile = new ArrayList<>();
            int count = this.fileCount.get(i);
            if(count>0){
                int dest = index+count;
                for(int j = index;j<dest;++j){
                    if(files.size()>j){
                        tempFile.add(this.files.get(j));
                        index++;
                    }
                }
                dto.setFiles(tempFile);
            }
            dto.setSendEmail(sendEmail.get(i));
            dto.setSendEmail(sendEmail.get(i));
            dto.setContent(content.get(i));
            dto.setSubject(subject.get(i));
            dto.setLogEmailId(logEmailId.get(i));
            dto.setTenantId(tenantId.get(i));
            dto.setCc(cc.get(i));
            dto.setSendPwd(sendPwd.get(i));
            dto.setTo(to.get(i));
            dto.setAffix(affix.get(i));
            dto.setAffixName(affixName.get(i));
            dto.setSendName(sendName.get(i));
            list.add(dto);
        }
        return list;
    }
}
