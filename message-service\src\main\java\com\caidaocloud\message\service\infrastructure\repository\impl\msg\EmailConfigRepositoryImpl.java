package com.caidaocloud.message.service.infrastructure.repository.impl.msg;

import com.caidaocloud.message.service.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.message.service.domain.msg.entity.EmailConfigDo;
import com.caidaocloud.message.service.domain.msg.repository.EmailConfigRepository;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 * @date 2022/6/6 下午6:04
 */
@Repository
public class EmailConfigRepositoryImpl extends BaseRepositoryImpl<EmailConfigDo> implements EmailConfigRepository {
}
