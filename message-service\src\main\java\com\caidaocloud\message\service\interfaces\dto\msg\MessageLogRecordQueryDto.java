package com.caidaocloud.message.service.interfaces.dto.msg;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * 消息记录查询
 * <AUTHOR>
 */
@Data
@ApiModel("消息记录查询dto")
public class MessageLogRecordQueryDto extends BasePage {

    private Long id;

    @ApiModelProperty("事件人名称")
    private String name;


}
