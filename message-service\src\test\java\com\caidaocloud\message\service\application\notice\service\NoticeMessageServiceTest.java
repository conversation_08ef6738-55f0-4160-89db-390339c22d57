package com.caidaocloud.message.service.application.notice.service;

import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.msg.event.TaskOperateNoticeSubscribe;
import junit.framework.TestCase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * created by: FoAng
 * create time: 2/1/2025 3:56 下午
 */
@SpringBootTest(classes = MessageApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class NoticeMessageServiceTest {

    @Autowired
    private TaskOperateNoticeSubscribe taskOperateNoticeSubscribe;

    @Test
    public void testMarkRead() {
        final String message = "{\"businessKey\": \"750_111\", \"choice\":\"REFUSE\",\"defId\":196,\"endEvent\":false,\"nodeId\":\"Activity_0ugs5n7\",\"procInstId\":\"e1bf379c-c8dc-11ef-acc1-0242ac11001e\",\"taskId\":\"3664ad8d-c8dd-11ef-acc1-0242ac11001e\",\"tenantId\":\"11\"}";
        taskOperateNoticeSubscribe.process(message);
    }
}