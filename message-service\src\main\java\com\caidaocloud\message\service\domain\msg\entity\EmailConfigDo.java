package com.caidaocloud.message.service.domain.msg.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.service.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;
import com.caidaocloud.message.service.domain.msg.repository.EmailConfigRepository;
import lombok.Data;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * EmailConfigDo
 *
 * <AUTHOR>
 * @date 2022/6/6 下午5:37
 */
@Data
@Service
public class EmailConfigDo extends BaseDomainDoImpl {

    private final static String IDENTIFIER = "entity.message.EmailConfig";

    /**
     * 邮箱模式
     * 0 smtp 常规模式
     * 1 adapter api 适配模式
     */
    private EnumSimple emailModel;

    /**
     * SMTP服务器
     */
    private String emailSmtp;

    /**
     * 端口
     */
    private Integer emailPort;

    /**
     * 用户名
     */
    private String emailFrom;

    /**
     * 发件人昵称
     */
    private String emailNick;

    /**
     * 密码
     */
    private String emailPwd;

    /**
     * 是否开启SSL
     */
    private Boolean enableSsl = false;


    @Resource
    private EmailConfigRepository repository;

    @Override
    public BaseRepository getRepository() {
        return repository;
    }

    @Override
    public String getDoIdentifier() {
        return IDENTIFIER;
    }

    @Override
    protected DataSimple build() {
        EmailConfigDo defaultData = new EmailConfigDo();
        defaultData.setIdentifier(getDoIdentifier());
        return defaultData;
    }
}
