package com.caidaocloud.message.service.application.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 通知规则
 */
public enum NoticeRuleEnum {
    CORP_EMAIL("0", "企业邮箱", "Corporate email", "优先发企业邮箱，无企业邮箱发个人邮箱"),
    PERSONAL_EMAIL("1", "个人邮箱", "Personal email", "优先发个人邮箱，无个人邮箱发企业邮箱");

    private String index;
    private String name;
    private String enName;
    private String desc;

    NoticeRuleEnum(String index, String name, String enName, String desc) {
        this.index = index;
        this.name = name;
        this.enName = enName;
        this.desc = desc;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static NoticeRuleEnum getEnumByIndex(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        NoticeRuleEnum[] values = NoticeRuleEnum.values();
        for (NoticeRuleEnum noticeTarget : values) {
            if (noticeTarget.index.equals(value)) {
                return noticeTarget;
            }
        }
        return null;
    }
}
