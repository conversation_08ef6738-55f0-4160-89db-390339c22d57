package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.service.application.message.notifier.dto.RoleUserInfoDto;
import com.caidaocloud.message.service.application.message.notifier.dto.RoleUserQueryDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(
    value = "caidaocloud-auth-service",
    fallback = AuthServiceFeignClientFallback.class,
    configuration = FeignConfiguration.class,
    contextId = "AuthServiceFeignClient"
)
public interface AuthServiceFeignClient {
    @GetMapping("/api/auth/v1/role/enabled/all")
    Result<List<KeyValue>> getAllRole();

    @PostMapping("/api/auth/v1/subject/roles/query-page")
    Result<PageResult<RoleUserInfoDto>> getUserInfoByRoles(@RequestBody RoleUserQueryDto roleUserQuery);
}
