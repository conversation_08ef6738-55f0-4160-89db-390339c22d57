package com.caidaocloud.message.service.application.msg.dto;

import com.caidaocloud.message.sdk.enums.ContentTypeEnum;
import com.caidaocloud.message.sdk.enums.NoticeActionEnum;
import com.caidaocloud.message.sdk.enums.NoticeBusinessEnum;
import com.caidaocloud.message.sdk.enums.NoticeMsgTypeEnum;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

/**
 * created by: FoAng
 * create time: 16/8/2024 10:34 上午
 */
@Data
@Accessors(chain = true)
public class NoticeMessageDto implements Serializable {

    @ApiModelProperty("发送消息接收对象")
    private Set<String> empIds;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("摘要汇总")
    private String summary;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("文本类型：plain text, rich")
    private ContentTypeEnum contentType;

    @ApiModelProperty("类型")
    private NoticeMsgTypeEnum type;

    @ApiModelProperty("业务类型")
    private NoticeBusinessEnum businessType;

    @ApiModelProperty("业务ID")
    private String businessId;

    @ApiModelProperty("操作类型")
    private NoticeActionEnum action;

    @ApiModelProperty("操作内容")
    private String actionContent;

    @ApiModelProperty("扩展字段")
    private Map<String, Object> ext;

    private SendStatusEnum status = SendStatusEnum.SUCESS;
}
