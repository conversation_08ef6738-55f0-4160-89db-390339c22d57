package com.caidaocloud.message.sdk.feign;

import com.caidaocloud.message.sdk.dto.EmpMsgConfigQueryDto;
import com.caidaocloud.message.sdk.dto.MsgConfigDto;
import com.caidaocloud.message.sdk.dto.MsgConfigItemVo;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MessageFeignClientFallBack implements MessageFeignClient {

    @Override
    public Result<List<MsgConfigDto>> getEnableMsgConfigList(String type) {
        return Result.fail();
    }

    @Override
    public Result<String> getEnableMsgConfigListSequence(String type) {
        return Result.fail();
    }

    @Override
    public Result<MsgConfigDto> getMsgConfigOne(String bid) {
        return Result.fail();
    }

    @Override
    public Result<List<MsgConfigItemVo>> listByIds(String bids) {
        return Result.fail();
    }

    @Override
    public Result<List<MsgConfigDto>> getMsgConfig(EmpMsgConfigQueryDto queryDto) {
        return Result.fail();
    }
}
