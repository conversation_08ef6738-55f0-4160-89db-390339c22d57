<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <modules>
        <module>message-sdk</module>
        <module>message-service</module>
        <module>record-core</module>
    </modules>

    <parent>
        <groupId>com.caidaocloud</groupId>
        <version>1.0.2-SNAPSHOT</version>
        <artifactId>caidaocloud-parent</artifactId>
    </parent>

    <groupId>com.caidaocloud</groupId>
    <artifactId>caidao-message-service</artifactId>
    <version>1.1.5-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <java.version>1.8</java.version>
        <elasticsearch.version>7.3.1</elasticsearch.version>
        <message.sdk.verison>1.1.5-SNAPSHOT</message.sdk.verison>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.caidaocloud</groupId>
                <artifactId>message-sdk</artifactId>
                <version>${message.sdk.verison}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>caidaocloud-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>galaxy-service-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>

        <!--<dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>metadata-sdk</artifactId>
        </dependency>
        -->
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>hr-core</artifactId>
            <version>2.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>metadata-sdk</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>paas-sdk</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>galaxy-service-mq</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-spring-boot-starter</artifactId>
            <version>4.4.0</version>
        </dependency>

        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>galaxy-service-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>galaxy-service-distributedlock</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>galaxy-service-condition</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
            <version>${kotlin.stdlib.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk7</artifactId>
            <version>${kotlin.stdlib.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
            <version>${kotlin.stdlib.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>${kotlin.stdlib.version}</version>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>galaxy-service-msg</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-common</artifactId>
            <version>${netty.version}</version>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>