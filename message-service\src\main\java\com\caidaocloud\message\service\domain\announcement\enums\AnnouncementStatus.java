package com.caidaocloud.message.service.domain.announcement.enums;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;

public enum AnnouncementStatus {
    UNPUBLISHED(0, "未发布"),
    PUBLISHED(1, "已发布"),
    EXPIRED(2, "已失效");

    private final int value;
    private final String display;

    AnnouncementStatus(int value, String display) {
        this.value = value;
        this.display = display;
    }

    public int getValue() {
        return value;
    }

    public String getDisplay() {
        return display;
    }

    public static AnnouncementStatus fromValue(int value) {
        for (AnnouncementStatus status : AnnouncementStatus.values()) {
            if (status.value == value) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid Status value: " + value);
    }

    public EnumSimple toEnumSimple(){
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(String.valueOf(value));
        return enumSimple;
    }
}