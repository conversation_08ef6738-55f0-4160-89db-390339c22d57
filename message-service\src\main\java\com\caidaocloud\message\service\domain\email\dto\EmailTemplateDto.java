package com.caidaocloud.message.service.domain.email.dto;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Slf4j
@Data
public class EmailTemplateDto {
    // from邮件
    private String sendEmail;
    // from Name
    private String sendName;
    // from pwd
    private String sendPwd;
    // 租户
    private String tenantId;
    // 收件人
    private String to;
    // 邮件主题
    private String subject;
    // 邮件内容
    private String content;
    /**
     * 多个附件用逗号分割
     * 附件地址
     */
    private String affix;

    /**
     * 指定附件对应的名称，多个用逗号分割同affix一一对应，如果为空或对应下标上的值为空，则取文件名作为名称
     * 附件名称
     */
    private String affixName;

    private List<MultipartFile> files;

    // 抄送人
    private String cc;

    // 批量邮件的批次id
    private String logEmailId;
}
