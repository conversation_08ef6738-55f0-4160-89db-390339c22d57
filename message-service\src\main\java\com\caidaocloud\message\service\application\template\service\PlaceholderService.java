package com.caidaocloud.message.service.application.template.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.message.service.application.common.service.BaseServiceImpl;
import com.caidaocloud.message.service.application.feign.IWfApproverFeign;
import com.caidaocloud.message.service.application.feign.dto.WorkflowApproverSetting;
import com.caidaocloud.message.service.domain.base.service.BaseDomainService;
import com.caidaocloud.message.service.domain.template.entity.PlaceholderDo;
import com.caidaocloud.message.service.domain.template.service.PlaceholderDomainService;
import com.caidaocloud.message.service.interfaces.vo.template.PlaceholderItemVo;
import com.caidaocloud.message.service.interfaces.vo.template.PlaceholderVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class PlaceholderService extends BaseServiceImpl<PlaceholderDo, BasePage> {
    @Resource
    private PlaceholderDomainService domainService;

    @Autowired
    private IWfApproverFeign iWfApproverFeign;

    @Override
    protected BaseDomainService getDomainService() {
        return domainService;
    }

    public List<PlaceholderItemVo> selectListData() {
        List<PlaceholderDo> placeholderDos = domainService.selectList();
        if(null == placeholderDos || placeholderDos.isEmpty()){
            return Lists.newArrayList();
        }

        Map<String, List<PlaceholderDo>> map = new HashMap<>();
        String key = null;
        List<PlaceholderDo> list = null;
        for (PlaceholderDo node : placeholderDos) {
            key = node.getCategory();
            if(map.containsKey(key)){
                list = map.get(key);
                list.add(node);
                map.put(key, list);
                continue;
            }

            list = new ArrayList<>();
            list.add(node);
            map.put(key, list);
        }

        List<PlaceholderItemVo> itemVos = new ArrayList<>();
        map.forEach((k, v) -> {
            PlaceholderItemVo itemVo = new PlaceholderItemVo();
            itemVo.setCategory(k);
            itemVo.setItem(convertVo(v));
            itemVos.add(itemVo);
        });
        itemVos.add(getApproverParameter());
        return itemVos;
    }

    private PlaceholderItemVo getApproverParameter() {
        PlaceholderItemVo placeholderItemVo = new PlaceholderItemVo();
        placeholderItemVo.setCategory("审批人管理");
        ArrayList<PlaceholderVo> list = new ArrayList<>();
        //1，查询审批人管理
        Result<List<WorkflowApproverSetting>> listResult = iWfApproverFeign.listSettings();
        log.info("getApproverParameter listResult={}", FastjsonUtil.toJson(listResult));
        if (listResult.isSuccess()) {
            List<WorkflowApproverSetting> data = listResult.getData();
            for (WorkflowApproverSetting setting : data) {
                PlaceholderVo placeholderVo = new PlaceholderVo();
                placeholderVo.setName(setting.getName());
                placeholderVo.setText(String.format("#审批人.%s#", setting.getName()));
                placeholderVo.setProp("$approver_" + setting.getBid());
                placeholderVo.setModel(placeholderVo.getProp());
                list.add(placeholderVo);
            }
        }
        placeholderItemVo.setItem(list);
        return placeholderItemVo;
    }

    private List<PlaceholderVo> convertVo(List<PlaceholderDo> list){
        if(null == list){
            return null;
        }

        List<PlaceholderVo> pList = new ArrayList<>(list.size());
        list.forEach(placeholderDo -> {
            PlaceholderVo convert = ObjectConverter.convert(placeholderDo, PlaceholderVo.class);
            convert.setVarType(null == placeholderDo.getVarType() ? "0" : placeholderDo.getVarType().getValue());
            pList.add(convert);
        });

        return pList;
    }

}
