package com.caidaocloud.message.service.application.message.notifier.service;

import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.message.service.application.message.notifier.input.NotifierDataFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 查询 指定系统人员
 */
@Service
public class NotifyAssignEmpService implements INotifyTargetService{

    @Override
    public String getNotifyTarget() {
        return NoticeTarget.ASSIGN_EMP.toString();
    }

    @Override
    public void getNotifier(List<NotifierDto> notifier, NotifyObjectDto obj) {
        getNotifier(notifier, obj, obj.getType());
    }

    public void getNotifier(List<NotifierDto> notifier, NotifyObjectDto obj, int type) {
        List<EmpDataVo> empDataList = null;
        if(1 == type){
            // 查询候选人
            empDataList = NotifierDataFactory
                    .getNotifierService().getCandidateDataByEmpIds(obj.getEmpIds(), obj.getDataTime());
        } else {
            empDataList = NotifierDataFactory
                    .getNotifierService().getEmpDataByEmpIds(obj.getEmpIds(), obj.getDataTime());
        }

        if(null == empDataList || empDataList.isEmpty()){
            return;
        }
        notifier.addAll(convertEmpDataList(empDataList));
    }
}
