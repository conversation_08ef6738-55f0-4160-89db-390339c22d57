package com.caidaocloud.message.service.interfaces.vo.base;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
public class PageListVo<T, D> {
    private final static PageListVo emptyVo = new PageListVo(Lists.newArrayList(), Lists.newArrayList());
    private List<T> columns;
    private List<D> items;

    /**
     * 非线程安全
     * 不能共享使用
     */
    public static PageListVo bulidEmpty(){
        return emptyVo;
    }

    /**
     * 构建一个新 PageListVo
     */
    public static PageListVo bulid(){
        PageListVo newPageListVo = new PageListVo();
        return newPageListVo;
    }
}
