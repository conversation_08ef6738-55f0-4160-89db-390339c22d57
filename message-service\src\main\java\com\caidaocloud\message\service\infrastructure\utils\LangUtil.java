package com.caidaocloud.message.service.infrastructure.utils;

/**
 * LangUtil
 *
 * <AUTHOR>
 * @date 2022/6/7 下午5:52
 */

import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.WebUtil;

/**
 * 多语言工具类
 */
public final class LangUtil {
    /**
     * 获取多语言提示
     */
    public static String getMsg(int msgCode){
        return MessageHandler.getExceptionMessage(msgCode, WebUtil.getRequest());
    }

    /**
     * 带占位符的多语言
     */
    public static String getFormatMsg(int msgCode, Object... args){
        String format = getMsg(msgCode);
        return String.format(format, args);
    }

}
