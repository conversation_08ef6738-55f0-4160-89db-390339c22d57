package com.caidaocloud.message.service.domain.msg.entity;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.service.application.common.enums.MsgConfigStatusEnum;
import com.caidaocloud.message.service.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;
import com.caidaocloud.message.service.domain.msg.repository.MsgConfigRepository;
import com.caidaocloud.message.service.infrastructure.utils.LangUtil;
import lombok.Data;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.caidaocloud.message.sdk.enums.NoticeType.RENEWAL_NOT_SIGN;
import static com.caidaocloud.message.service.application.common.constant.LangCodeConstant.CONFIG_CANNOT_DELETE;

/**
 * MsgConfigDo
 *
 * <AUTHOR>
 * @date 2022/6/6 下午5:36
 */
@Data
@Service
public class MsgConfigDo extends BaseDomainDoImpl {
    /**
     * 消息通知名称
     */
    private String name;
    /**
     * 多语言字段
     */
    private String i18nName;

    /**
     * 通知类型：
     * 0 续签未签署提醒
     * 1 电子合同签署提醒
     * 2 电子合同作废提醒
     * 3 手动推送
     */
    private EnumSimple msgType;

    /**
     * 通知对象
     * hrbp HRBP
     * assignEmp 指定系统人员
     * leader 上级领导
     * orgLeader 部门负责人
     * eventEmp 事件人员
     * allEmp 全部人员
     * guardian 监护人
     */
    private String notifyObject;

    private String condition;

    /**
     * 通知方式
     * 0 单次通知
     * 1 循环通知
     */
    private EnumSimple func;

    /**
     * 通知轮次
     * 0 提前
     * 1 当天
     * 2 延后
     */
    private EnumSimple round;

    /**
     * 通知天数
     */
    private Integer day;

    /**
     * 发送时间，0点开始毫秒数
     */
    private Integer sendTime;

    /**
     * 通知周期
     */
    private Integer loop;

    /**
     * 发送途径
     */
    private String channel;

    /**
     * 状态：
     * 0 未启用
     * 1 已启用
     * 2 已停用
     */
    private EnumSimple status;

    /**
     * 绑定的模版
     */
    private String templateBid;

    /**
     * 指定系统人员
     */
    private List<EmpSimple> emps;

    /**
     * 手动推送通知日期
     */
    private Long triggerDate;


    /**
     * 通知规则
     */
    private EnumSimple rule;


    /**
     * 循环天数
     */
    private Integer loopDay;

    /**
     * 合并规则
     */
    private MergeRule mergeRule;
    // 事件人过滤，false 不过滤，true 过滤
    private Boolean subjectFilter;

    private final static String IDENTIFIER = "entity.message.MsgConfig";

    @Resource
    private MsgConfigRepository msgConfigRepository;

    public int updateById(MsgConfigDo data) {
        MsgConfigDo dbData = selectOne(data.getBid());
        // 开启状态的消息不能编辑
//        PreCheck.preCheckArgument(MsgConfigStatusEnum.ENABLED.value.equals(dbData.getStatus().getValue()), LangUtil.getMsg(CONFIG_CANNOT_EDIT));
        return msgConfigRepository.updateById(data);
    }

    @Override
    public BaseRepository<MsgConfigDo> getRepository() {
        return msgConfigRepository;
    }

    @Override
    public String getDoIdentifier() {
        return IDENTIFIER;
    }

    @Override
    protected DataSimple build() {
        return new MsgConfigDo();
    }

    public MsgConfigDo selectOne(String bid) {
        return getRepository().selectById(bid, IDENTIFIER);
    }

    public MsgConfigDo save(MsgConfigDo data) {
        // 消息默认状态为 未启用
        EnumSimple configStatus = new EnumSimple();
        configStatus.setValue(MsgConfigStatusEnum.NOT_ENABLED.value);
        // 手动推送默认设置为开启状态
        if (NoticeType.MANUAL_PUSH.getIndex().equals(data.getMsgType().getValue())) {
            configStatus.setValue(MsgConfigStatusEnum.ENABLED.value);
        }
        data.setStatus(configStatus);
        return msgConfigRepository.insert(data);
    }

    public void changeStatus(String bid, MsgConfigStatusEnum statusEnum) {
        MsgConfigDo data = selectOne(bid);
        EnumSimple status = data.getStatus();
        status.setValue(statusEnum.value);
        data.setStatus(status);
        msgConfigRepository.updateById(data); }

    @Override
    public void deleteByBid(String bid) {
        MsgConfigDo data = selectOne(bid);
        // 开启状态的消息不能删除
        PreCheck.preCheckArgument(MsgConfigStatusEnum.ENABLED.value.equals(data.getStatus().getValue()), LangUtil.getMsg(CONFIG_CANNOT_DELETE));
        super.deleteByBid(bid);
    }

    /**
     * 是否是未签署通知类型
     * @return
     */
    public boolean checkNoSignMsgType() {
        return msgType != null && RENEWAL_NOT_SIGN.getIndex().equals(msgType.getValue());
    }
}
