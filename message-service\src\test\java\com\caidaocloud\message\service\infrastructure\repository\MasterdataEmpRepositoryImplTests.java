package com.caidaocloud.message.service.infrastructure.repository;

import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.domain.base.repository.INotifyEmpRepository;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;


/**
 *
 * <AUTHOR>
 * @date 2023/7/28
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
@Slf4j
public class MasterdataEmpRepositoryImplTests {

	@Autowired
	private INotifyEmpRepository masterdataEmpRepository;

	@Before
	public void bf(){
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("11");
		SecurityUserUtil.setSecurityUserInfo(userInfo);
	}

	@Test
	public void test(){
		log.info("{}", masterdataEmpRepository.loadPrivateInfo("1707060247992325"));
		log.info("{}", masterdataEmpRepository.loadLeader("1707060247992325",System.currentTimeMillis()
		));
	}
}