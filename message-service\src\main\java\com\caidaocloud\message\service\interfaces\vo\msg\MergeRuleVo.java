package com.caidaocloud.message.service.interfaces.vo.msg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/3/7
 */
@Data
@ApiModel("合并发送配置vo")
public class MergeRuleVo {
	@ApiModelProperty("合并发送日，1-31")
	private Integer mergeDay;
	@ApiModelProperty("合并发送时间")
	private Integer mergeTime;
	@ApiModelProperty("合并周期")
	private Integer mergePeriod;
}