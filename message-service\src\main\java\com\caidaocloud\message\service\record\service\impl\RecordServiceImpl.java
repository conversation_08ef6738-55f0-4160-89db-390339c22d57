package com.caidaocloud.message.service.record.service.impl;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.message.service.application.feign.HrFeignClient;
import com.caidaocloud.message.service.infrastructure.utils.DateUtil;
import com.caidaocloud.message.service.infrastructure.utils.ExcelUtils;
import com.caidaocloud.message.service.record.bean.LogRecord;
import com.caidaocloud.message.service.record.bean.UserAccountInfo;
import com.caidaocloud.message.service.record.dto.LogRecordQueryDto;
import com.caidaocloud.message.service.record.feign.ResourceFeign;
import com.caidaocloud.message.service.record.feign.UserFeign;
import com.caidaocloud.message.service.record.properties.LogProviderProperties;
import com.caidaocloud.message.service.record.provider.RecordRepositoryFactory;
import com.caidaocloud.message.service.record.service.RecordService;
import com.caidaocloud.message.service.record.util.UserContextUtil;
import com.caidaocloud.message.service.record.vo.LogRecordVo;
import com.caidaocloud.record.core.beans.AuthResourceUrl;
import com.caidaocloud.record.core.beans.LogRecordData;
import com.caidaocloud.record.core.enums.ResourceCategoryEnum;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * created by: FoAng
 * create time: 28/5/2024 4:07 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class RecordServiceImpl implements RecordService {

    private UserFeign userFeign;

    private ResourceFeign resourceFeign;

    private RecordRepositoryFactory repositoryFactory;

    private CacheService cacheService;

    private LogProviderProperties logProviderProperties;

    private HrFeignClient hrFeignClient;

    private final SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1L, 1L);

    @Override
    public void saveRecordData(LogRecordData data) {
        try {
            final String tenantId = data.getTenantId();
            if (StringUtil.isEmpty(UserContextUtil.getTenantId())) {
                SecurityUserInfo userInfo =  new SecurityUserInfo();
                userInfo.setTenantId(tenantId);
                userInfo.setUserId(data.getOperator());
                userInfo.setIsAdmin(false);
                userInfo.setEmpId(data.getEmpId());
                SecurityUserUtil.setSecurityUserInfo(userInfo);
            }
            LogRecord logRecord = BeanUtil.convert(data, LogRecord.class);
            logRecord.setId(snowflakeUtil.createId());
            logRecord.setCreateTime(new Date());
            logRecord.setIsDeleted(0);
            UserAccountInfo accountInfo = null;
            AuthResourceUrl resourceUrlVo;
            if (data.getOperator() != null) {
                accountInfo = fetchUserInfo(data.getTenantId(), data.getOperator());
                if (accountInfo != null && accountInfo.getEmpId() != null) {
                    EmpWorkInfoVo infoVo = hrFeignClient.getEmpWorkInfo(String.valueOf(accountInfo.getEmpId()), System.currentTimeMillis()).getData();
                    accountInfo.setWorkNo(Optional.ofNullable(infoVo).map(EmpWorkInfoVo::getWorkno).orElse(""));
                }
            }
            if (StringUtil.isNotEmpty(data.getUri()) && (StringUtil.isEmpty(data.getMenu()) || StringUtil.isEmpty(data.getCategory()))) {
                resourceUrlVo = fetchResourceInfo(data.getUri());
                if (resourceUrlVo != null) {
                    logRecord.setMenu(Optional.ofNullable(logRecord.getMenu()).orElseGet(() -> {
                        if (resourceUrlVo.getCategory() != ResourceCategoryEnum.MENU) {
                            return String.join("-", resourceUrlVo.getParentName(), resourceUrlVo.getName());
                        } else {
                            return resourceUrlVo.getParentName();
                        }
                    }));
                    logRecord.setCategory(Optional.ofNullable(logRecord.getCategory())
                            .orElseGet(() -> ResourceCategoryEnum.parseResourceCategory(resourceUrlVo.getCategory(),
                                    resourceUrlVo.getResourceAction())));
                }
            }
            if (invalidRecordData(logRecord)) {
                log.info("[record] invalid record data: {}",  FastjsonUtil.toJson(logRecord));
            } else {
                repositoryFactory.getRepository(logProviderProperties.getProvider()).saveRecord(logRecord, accountInfo);
            }
        } catch (Exception e) {
            log.error("[record] save record error: {}", e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

    @Override
    public List<LogRecordVo> listRecord(LogRecordQueryDto dto) {
        return repositoryFactory.getRepository(logProviderProperties.getProvider()).listRecord(dto);
    }

    @Override
    public PageResult<LogRecordVo> pageRecord(LogRecordQueryDto dto) {
        return repositoryFactory.getRepository(logProviderProperties.getProvider()).pageRecord(dto);
    }

    private boolean invalidRecordData(LogRecord recordData) {
        return StringUtil.isEmpty(recordData.getMenu()) || StringUtil.isEmpty(recordData.getCategory());
    }

    private UserAccountInfo fetchUserInfo(String tenantId, Long userId) {
        String CACHE_KEY_TENANT_USER = "cache_user_info_%s_%s";
        return Optional.ofNullable(cacheService.getValue(String.format(CACHE_KEY_TENANT_USER, tenantId, userId)))
                .map(it -> FastjsonUtil.toObject(it, UserAccountInfo.class)).orElseGet(() -> {
                    Result<UserAccountInfo> result = userFeign.getUserAndAccountInfo(userId);
                    if (result != null && result.isSuccess() && result.getData() != null) {
                        cacheService.cacheValue(String.format(CACHE_KEY_TENANT_USER, tenantId, userId),
                                FastjsonUtil.toJson(result.getData()), 3600);
                        // todo 更新es缓存用户信息？
                    }
                    return result != null && result.isSuccess() ? result.getData() : null;
                });
    }

    private AuthResourceUrl fetchResourceInfo(String url) {
        String CACHE_KEY_RESOURCE_INFO = "cache_resource_info_%s";
        return Optional.ofNullable(cacheService.getValue(String.format(CACHE_KEY_RESOURCE_INFO, url)))
                .map(it -> FastjsonUtil.toObject(it, AuthResourceUrl.class)).orElseGet(() -> {
                    Result<AuthResourceUrl> result = resourceFeign.getResourceData(url);
                    if (result != null && result.isSuccess() && result.getData() != null) {
                        cacheService.cacheValue(String.format(CACHE_KEY_RESOURCE_INFO, url),
                                FastjsonUtil.toJson(result.getData()), 3600);
                    }
                    return result != null && result.isSuccess() ? result.getData() : null;
                });
    }

    @Override
    public void export(LogRecordQueryDto dto, HttpServletResponse response) {
        dto.setPageNo(1);
        dto.setPageSize(5000);
        PageResult<LogRecordVo> page = pageRecord(dto);
        try {
            List<Map> list = new ArrayList<>();
            for (LogRecordVo item : page.getItems()) {
                Map map = FastjsonUtil.convertObject(item, Map.class);
                map.put("createTime", DateUtil.getFormatDate((long) map.get("createTime"), DateUtil.YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                list.add(map);
            }
            ExcelUtils.downloadDataMapExcel(getColList(), list, "操作记录", response);

        } catch (Exception e) {
            log.error("download NewlySignedList excel error.{}", e.getMessage(), e);
        }

    }

    private List<ExcelExportEntity> getColList() {
        List<ExcelExportEntity> colList = new ArrayList<>();
        colList.add(new ExcelExportEntity("操作人姓名", "operatorName"));
        colList.add(new ExcelExportEntity("操作人账号", "operatorAccount"));
        colList.add(new ExcelExportEntity("操作人工号", "operatorWorkNo"));
        colList.add(new ExcelExportEntity("操作菜单", "menu"));
        colList.add(new ExcelExportEntity("操作端口", "operatorPlatform"));
        colList.add(new ExcelExportEntity("操作类型", "category"));
        colList.add(new ExcelExportEntity("操作内容", "action"));
        colList.add(new ExcelExportEntity("操作时间", "createTime"));
        return colList;
    }
}
