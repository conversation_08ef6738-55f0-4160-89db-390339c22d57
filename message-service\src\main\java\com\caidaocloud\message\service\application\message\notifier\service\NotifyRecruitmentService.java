package com.caidaocloud.message.service.application.message.notifier.service;

import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDataDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.message.service.application.message.notifier.input.NotifierDataFactory;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * created by: FoAng
 * create time: 16/3/2023 10:42 上午
 */
@Service
public class NotifyRecruitmentService implements INotifyTargetService {

    @Override
    public String getNotifyTarget() {
        return NoticeTarget.RECRUITMENT.toString();
    }

    @Override
    public void getNotifier(List<NotifierDto> notifier, NotifyObjectDto obj) {
        List<EmpDataVo> recruitmentList = null;
        if(1 == obj.getType()){
            recruitmentList = NotifierDataFactory.getNotifierService()
                    .getCandidateRecruitment(NotifierDataDto.bulid(obj));
        }
        if (CollectionUtils.isEmpty(recruitmentList)) return;
        notifier.addAll(convertEmpDataList(recruitmentList));
    }
}
