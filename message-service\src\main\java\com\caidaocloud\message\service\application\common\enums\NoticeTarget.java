package com.caidaocloud.message.service.application.common.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * 通知目标、通知对象
 */
public enum NoticeTarget {
    HRBP("hrbp", "HRBP", "HRBP"),
    ASSIGN_EMP("assignEmp", "指定系统人员", "Designated system personnel"),
    LEADER("leader", "直接上级", "Direct superior"),
    ORG_LEADER("orgLeader", "部门负责人", "Department leader"),
    EVENT_EMP("eventEmp", "事件人员", "Incident personnel"),
    ALL_EMP("allEmp", "全部人员", "All personnel"),
    GUARDIAN("guardian","监护人", "guardian"),
    RECRUITERS("recruiters","Recruiters", "recruiters"),
    RECRUITMENT("recruitment", "招聘顾问", "recruitment"),
    ASSIGN_OTHER("assignOther","指定外部人员", "assignOther"),
    Initiator("Initiator", "入职发起人", "Entry Initiator"),
    COMMON_INITIATOR("COMMON_INITIATOR","发起人","Initiator");

    private String index;
    private String name;
    private String enName;

    NoticeTarget(String index, String name, String enName){
        this.index = index;
        this.name = name;
        this.enName = enName;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public static NoticeTarget getEnumByValue(String value) {
        if (value == null) {
            return null;
        }
        for (NoticeTarget v : NoticeTarget.values()) {
            String enumName = v.toString();
            if (enumName.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static NoticeTarget getEnumByIndex(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        NoticeTarget[] values = NoticeTarget.values();
        for (NoticeTarget noticeTarget : values) {
            if (noticeTarget.index.equals(value)) {
                return noticeTarget;
            }
        }
        return null;
    }

    public static String getNameByIndex(String index) {
        NoticeTarget e;
        if ((e = getEnumByIndex(index)) == null) {
            return "";
        }
        return e.getName();
    }

    public static String getNameByEnumName(String enumName) {
        for (NoticeTarget noticeTarget : NoticeTarget.values()) {
            if (noticeTarget.toString().equals(enumName)) {
                return noticeTarget.getName();
            }
        }
        return null;
    }

    public static String getNameByEnumNameOrMap(String enumName, Map<String, String> roleMap) {
        if(roleMap.containsKey(enumName)){
            return roleMap.get(enumName);
        }
        for (NoticeTarget noticeTarget : NoticeTarget.values()) {
            if (noticeTarget.toString().equals(enumName)) {
                return noticeTarget.getName();
            }
        }
        return null;
    }

    public static String getIndexByValue(String value) {
        NoticeTarget e;
        if ((e = getEnumByValue(value)) == null) {
            return "";
        }
        return e.getIndex();
    }

    public static void main(String[] args) {
        System.out.println(getEnumByValue("ASSIGN_EMP"));
    }
}
