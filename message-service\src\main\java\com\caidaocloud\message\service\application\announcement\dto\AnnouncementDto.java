package com.caidaocloud.message.service.application.announcement.dto;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.service.domain.announcement.enums.ReceiveType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@ApiModel(description = "公告Dto")
public class AnnouncementDto {

	@ApiModelProperty(value = "公告ID")
	private String bid;

	@ApiModelProperty(value = "公告名称")
	@NotBlank(message = "公告名称不能为空")
	private String name;

	@ApiModelProperty(value = "公告类型")
	@NotBlank(message = "公告类型不能为空")
	private String type;

	@ApiModelProperty(value = "发布时间（Unix时间戳，毫秒）")
	private Long releaseTime;

	@ApiModelProperty(value = "生效时间（Unix时间戳，毫秒）")
	private Long effectiveTime;

	@ApiModelProperty(value = "到期时间（Unix时间戳，毫秒）")
	private Long expiryTime;

	@ApiModelProperty(value = "接收人员类型")
	@NotNull(message = "接收人员类型不能为空")
	private ReceiveType receiveType;

	@ApiModelProperty(value = "接收者值列表")
	private List<String> receiveDeptList;

	@ApiModelProperty(value = "接收者值列表")
	private List<EmpSimple> receiveEmpList;

	@ApiModelProperty(value = "是否置顶")
	private Boolean isTop = false;

	@ApiModelProperty(value = "公告适用域名列表")
	private List<String> domain;

	@ApiModelProperty(value = "公告内容")
	private String content;

	@ApiModelProperty(value = "附件信息")
	private Attachment attachment;

}
