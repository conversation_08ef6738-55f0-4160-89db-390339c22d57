package com.caidaocloud.message.service.application.announcement.dto;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.service.domain.announcement.enums.ReceiveType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@ApiModel(description = "公告数据传输对象")
public class AnnouncementDto {

	@ApiModelProperty(value = "公告ID")
	private String bid;

	@ApiModelProperty(value = "公告名称")
	private String name;

	@ApiModelProperty(value = "公告类型")
	private String type;

	@ApiModelProperty(value = "发布时间（Unix时间戳，毫秒）")
	private Long releaseTime;

	@ApiModelProperty(value = "生效时间（Unix时间戳，毫秒）")
	private Long effectiveTime;

	@ApiModelProperty(value = "到期时间（Unix时间戳，毫秒）")
	private Long expiryTime;

	@ApiModelProperty(value = "接收人员类型")
	private ReceiveType receiveType;

	@ApiModelProperty(value = "接收者值列表")
	private List<String> receiveValue;

	@ApiModelProperty(value = "是否置顶")
	private Boolean isTop = false;

	@ApiModelProperty(value = "公告适用域名列表")
	private List<String> domain;

	@ApiModelProperty(value = "公告内容")
	private String content;

	@ApiModelProperty(value = "附件信息")
	private Attachment attachment;

}
