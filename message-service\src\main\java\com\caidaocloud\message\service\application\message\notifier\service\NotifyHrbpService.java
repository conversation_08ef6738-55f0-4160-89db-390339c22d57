package com.caidaocloud.message.service.application.message.notifier.service;

import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDataDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.message.service.application.message.notifier.input.NotifierDataFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 查询 HRBP
 */
@Service
public class NotifyHrbpService implements INotifyTargetService{

    @Override
    public String getNotifyTarget() {
        return NoticeTarget.HRBP.toString();
    }

    @Override
    public void getNotifier(List<NotifierDto> notifier, NotifyObjectDto obj) {
        List<EmpDataVo> hrbpByEmpIds = NotifierDataFactory.getNotifierService()
                .getHrbpDataByEmpIds(NotifierDataDto.bulid(obj));
        if(null == hrbpByEmpIds || hrbpByEmpIds.isEmpty()){
            return;
        }

        notifier.addAll(convertEmpDataList(hrbpByEmpIds));
    }
}
