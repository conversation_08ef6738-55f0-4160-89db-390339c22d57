package com.caidaocloud.message.service.infrastructure.utils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

public class DateUtil {
    public static final String DEFAULT_DATE_PATTERN = "yyyy-MM-dd";
    public static final String YEAR_MONTH_DAY_HOUR_MINUTE_SECOND = "yyyy-MM-dd HH:mm:ss";

    public static int getYear(Long dateTimestamp) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(dateTimestamp);
        return calendar.get(Calendar.YEAR);
    }

    /**
     * 获取相差天数 (只考虑天数)
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static int getIntervalDays(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return 0;
        }

        startDate = DateUtil.getDateZeroPoint(startDate);
        endDate = DateUtil.getDateZeroPoint(endDate);

        long times = endDate.getTime() - startDate.getTime();
        return (int) (times / (24 * 60 * 60 * 1000));
    }

    /**
     * 获取时间的 零点
     *
     * @param date
     * @return
     */
    public static Date getDateZeroPoint(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取当天日期零点的毫秒时间戳
     *
     * @return
     */
    public static long getCurrentDateMillis() throws ParseException {
        DateFormat df = new SimpleDateFormat(DEFAULT_DATE_PATTERN);
        return df.parse(df.format(new Date())).getTime();
    }

    public static LocalDateTime getDateTimeOfTimestamp(long timestamp) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    public static String getDateTimeAsString(LocalDateTime localDateTime, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return localDateTime.format(formatter);
    }

    public static String getFormatDate(long timestamp, String format) {
        LocalDateTime localDateTime = getDateTimeOfTimestamp(timestamp);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return localDateTime.format(formatter);
    }

    public static long getTimestampOfDateTime(LocalDateTime localDateTime) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return instant.toEpochMilli();
    }

    public static LocalDateTime parseStringToDateTime(String time, String format) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(format);
        return LocalDateTime.parse(time, df);
    }
}
