package com.caidaocloud.record.core.configure;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * created by: FoAng
 * create time: 22/5/2024 5:32 下午
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "log.record")
public class LogRecordProperties {

    /**
     * 是否开启
     */
    private boolean enable;

    /**
     * 日志记录方式：gateway、aop、mixed
     */
    private String mode;

    /**
     * 匹配全部url, mixed模式下，需手动进行区分，matchAll建议设置为false
     */
    private boolean matchAll;

    /**
     * 日志消费方式：rabbitmq、feign
     */
    private String dispatch;
}
