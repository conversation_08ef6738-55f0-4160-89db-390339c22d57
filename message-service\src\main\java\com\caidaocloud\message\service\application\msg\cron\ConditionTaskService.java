package com.caidaocloud.message.service.application.msg.cron;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.condition.service.IRuleCondition;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.message.service.application.common.constant.LangCodeConstant;
import com.caidaocloud.message.service.application.msg.dto.RelationshipDto;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.domain.msg.entity.EmpMsgTemplateRelDo;
import com.caidaocloud.message.service.domain.msg.entity.MsgConfigDo;
import com.caidaocloud.message.service.infrastructure.utils.LangUtil;
import com.caidaocloud.message.service.interfaces.dto.msg.MsgConfigQueryDto;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.vo.ImportExcelProcessVo;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

@Slf4j
@Service
public class ConditionTaskService {
    private final static String TASK_LOCK_KEY = "msg_template_%s",
            TEMPLATE_REL_REFRESH_PROGRESS = "msg_template_rel_refresh_progress_%s_%s";

    @Value("${caidaocloud.tenant:}")
    private List<String> tenantList;
    @Resource
    private CacheService cacheService;
    @Resource
    private Locker locker;
    @Resource
    private MsgConfigDo msgConfigDo;
    @Resource
    private IRuleCondition ruleCondition;
    @Resource
    private EmpMsgTemplateRelDo empMsgTemplateRelDo;

    @XxlJob("templateConditionJobHandler")
    public ReturnT<String> templateConditionJobHandler() {
        XxlJobHelper.log("XxlJob templateConditionJobHandler start");
        log.info("cronTask[Template Condition]------------------------start execution,time {}", System.currentTimeMillis());
        if(null == tenantList || tenantList.isEmpty()){
            log.info("tenantList is empty, execution end");
            return ReturnT.SUCCESS;
        }
        SecurityUserInfo userInfo;
        MsgConfigQueryDto pageQuery = new MsgConfigQueryDto();
        pageQuery.setStatus("1");
        UserInfo user;
        for (String tenantId : tenantList) {
            userInfo = new SecurityUserInfo();
            userInfo.setTenantId(tenantId);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            user = new UserInfo();
            user.setTenantId(tenantId);
            // 系统跑批userid默认为0
            user.doSetUserId(0L);
            UserContext.setCurrentUser(user);
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(200);
            doMsgTemplate(tenantId, null, pageQuery);
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
        log.info("cronTask[Template Condition]------------------------end execution,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob templateConditionJobHandler end");
        return ReturnT.SUCCESS;
    }

    public void doMsgTemplate(String tenantId, String progress, MsgConfigQueryDto pageQuery){
        Lock lock = locker.getLock(String.format(TASK_LOCK_KEY, tenantId));
        boolean locked;
        try {
            locked = lock.tryLock(10, TimeUnit.SECONDS);
        } catch (Exception e){
            throw new ServerException(LangUtil.getMsg(LangCodeConstant.OTHER_TASKS_CURRENTLY_BEING_PROCESSED));
        }

        if(!locked){
            // 获取锁失败
            log.info("tryLock fail,tenantId={}", tenantId);
            throw new ServerException(LangUtil.getMsg(LangCodeConstant.OTHER_TASKS_CURRENTLY_BEING_PROCESSED));
        }

        boolean cacheProgress = StringUtil.isEmpty(progress);
        ImportExcelProcessVo processObj = null;
        String progressKey = null;
        try {
            PageResult<MsgConfigDo> page = msgConfigDo.getPage(pageQuery);
            if(!cacheProgress){
                progressKey = String.format(TEMPLATE_REL_REFRESH_PROGRESS, tenantId, progress);
                String progressData = cacheService.getValue(progressKey);
                // 更新进度条
                processObj = FastjsonUtil.toObject(progressData, ImportExcelProcessVo.class);
                if(null == processObj){
                    processObj = new ImportExcelProcessVo(progress, 300, 1, 0, 1, 0);
                }
            }

            List<MsgConfigDo> list = null;
            if(null == page || null == (list = page.getItems()) || list.isEmpty()){
                completedRefresh(cacheProgress, progressKey, processObj);
                return;
            }

            int size = list.size();
            if(!cacheProgress && size < processObj.getTotal()){
                processObj.setTotal(size);
            }

            RelationshipDto rd = new RelationshipDto();
            rd.setEmpId(pageQuery.getEmpId());
            for (int i = 0; i < size; i++) {
                MsgConfigDo data = list.get(i);
                if(null == data || StringUtil.isEmpty(data.getCondition())){
                    continue;
                }

                rd.setTenantId(tenantId).setConditionTree(data.getCondition())
                        .setTemplateTxt(data.getName()).setTemplate(data.getBid());
                saveRelationshipData(rd);

                if(cacheProgress){
                    continue;
                }

                // 更新进度条
                processObj.setCompleted(processObj.getCompleted() + 1);
                processObj.setSuccessCount(processObj.getCompleted());
                cacheService.cacheValue(progressKey, FastjsonUtil.toJson(processObj), 1800);
            }

            if(list.size() < 200){
                completedRefresh(cacheProgress, progressKey, processObj);
                return;
            }

            pageQuery.setPageNo(page.getPageNo() + 1);
            doMsgTemplate(tenantId, progress, pageQuery);
        } catch (Exception e){
            log.error("tenantId = {},userInfo={}, execution err,{}", tenantId,
                    FastjsonUtil.toJson(SecurityUserUtil.getSecurityUserInfo()),
                    e.getMessage(), e);
        } finally {
            lock.unlock();
        }
    }

    public void genEmpTemplateRelationship(String bid, String progress){
        try {
            MsgConfigDo data = this.msgConfigDo.selectOne(bid);
            genEmpTemplateRelationship(data);
        } catch (Exception e){
            log.error("genEmpTemplateRelationship err,{}", e.getMessage(), e);
        }

        if(StringUtil.isEmpty(progress)){
            return;
        }

        String tenantId = UserContext.getTenantId();
        String progressKey = String.format(TEMPLATE_REL_REFRESH_PROGRESS, tenantId, progress);
        String progressData = cacheService.getValue(progressKey);
        if(StringUtil.isEmpty(progressData)){
            return;
        }

        // 更新进度条
        ImportExcelProcessVo processObj = FastjsonUtil.toObject(progressData, ImportExcelProcessVo.class);
        processObj.setCompleted(processObj.getTotal());
        processObj.setSuccessCount(processObj.getCompleted());
        cacheService.cacheValue(progressKey, FastjsonUtil.toJson(processObj), 1800);
    }

    public void genEmpTemplateRelationship(MsgConfigDo data){
        String tenantId = UserContext.getTenantId();
        Lock lock = locker.getLock(String.format(TASK_LOCK_KEY, tenantId));
        boolean locked;
        try {
            locked = lock.tryLock(10, TimeUnit.SECONDS);
        } catch (Exception e){
            throw new ServerException(LangUtil.getMsg(LangCodeConstant.OTHER_TASKS_CURRENTLY_BEING_PROCESSED));
        }

        if(!locked){
            // 获取锁失败
            log.info("genEmpTemplateRelationship tryLock fail,tenantId={}", tenantId);
            return;
        }

        RelationshipDto rd = new RelationshipDto();
        rd.setTemplate(data.getBid()).setTemplateTxt(data.getName()).setTenantId(data.getTenantId()).setConditionTree(data.getCondition());
        try {
            saveRelationshipData(rd);
        } catch (Exception e){
            log.info("Generate employee template relationship err,{}", e.getMessage(), e);
        } finally {
            lock.unlock();
        }
    }

    public void saveRelationshipData(RelationshipDto rd){
        List<String> empList = ruleCondition.getEmpIdList(rd.getTenantId(), rd.getConditionTree(), rd.getEmpId());
        log.info("-----EmpMsgTemplateRelDo-----data={}", FastjsonUtil.toJson(empList));

        if(StringUtil.isEmpty(rd.getEmpId())){
            empMsgTemplateRelDo.deleteTemplateId(rd.getTemplate());
        } else{
            empMsgTemplateRelDo.deleteByEmpIdAndTemplateId(rd.getTemplate(), Lists.newArrayList(rd.getEmpId()));
        }

        List<EmpMsgTemplateRelDo> insertList = new ArrayList<>(empList.size());
        long l = System.currentTimeMillis();
        for (String empId : empList) {
            EmpMsgTemplateRelDo data = new EmpMsgTemplateRelDo();
            data.setEmpId(empId);
            data.setTemplate(rd.getTemplate());
            data.setTemplateTxt(rd.getTemplateTxt());
            data.setIdentifier(empMsgTemplateRelDo.getDoIdentifier());
            data.setCreateBy(UserContext.getUserId());
            data.setUpdateTime(l);
            data.setUpdateBy(data.getCreateBy());
            data.setCreateTime(l);
            data.setDataStartTime(0);
            data.setDataEndTime(DateUtil.MAX_TIMESTAMP);
            insertList.add(data);
            if(insertList.size() >= 500){
                empMsgTemplateRelDo.batchInsert(insertList);
                insertList.clear();
            }
        }

        if(!insertList.isEmpty()){
            empMsgTemplateRelDo.batchInsert(insertList);
        }
    }

    private void completedRefresh(boolean cacheProgress, String progressKey, ImportExcelProcessVo processObj){
        if(!cacheProgress){
            processObj.setCompleted(processObj.getTotal());
            processObj.setSuccessCount(processObj.getCompleted());
            cacheService.cacheValue(progressKey, FastjsonUtil.toJson(processObj), 1800);
        }
    }
}
