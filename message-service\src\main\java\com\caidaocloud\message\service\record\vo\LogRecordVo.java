package com.caidaocloud.message.service.record.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * created by: FoAng
 * create time: 31/5/2024 4:12 下午
 */
@Data
public class LogRecordVo implements Serializable {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("操作人")
    private Long operator;

    @ApiModelProperty("操作人账号")
    private String operatorAccount;

    @ApiModelProperty("操作人手机号")
    private String operatorMobile;

    @ApiModelProperty("操作人邮箱")
    private String operatorEmail;

    @ApiModelProperty("操作人姓名")
    private String operatorName;

    @ApiModelProperty("操作人工号")
    private String operatorWorkNo;

    @ApiModelProperty("员工ID")
    private Long empId;

    @ApiModelProperty("操作来源")
    private String operatorSource;

    @ApiModelProperty("操作平台")
    private String operatorPlatform;

    @ApiModelProperty("服务名称")
    private String serviceName;

    @ApiModelProperty("请求uri")
    private String uri;

    @ApiModelProperty("请求菜单")
    private String menu;

    @ApiModelProperty("操作类型")
    private String category;

    @ApiModelProperty("操作摘要")
    private String action;

    @ApiModelProperty("操作详细内容")
    private String detail;

    @ApiModelProperty("操作状态")
    private Boolean status;

    @ApiModelProperty("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
