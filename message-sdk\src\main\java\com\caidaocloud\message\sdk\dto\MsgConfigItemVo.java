package com.caidaocloud.message.sdk.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.sdk.enums.NoticeRule;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.enums.NotificationCycleTypeEnum;
import com.caidaocloud.message.sdk.enums.NotificationMethodEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * created by: FoAng
 * create time: 26/4/2023 2:27 下午
 */
@Data
public class MsgConfigItemVo {

    @ApiModelProperty("业务id")
    private String bid;

    @ApiModelProperty("通知名称名称")
    private String name;

    @ApiModelProperty("通知类型")
    private NoticeType msgType;

    @ApiModelProperty("通知对象")
    private String notifyObject;

    @ApiModelProperty("通知方式")
    private NotificationMethodEnum func;

    @ApiModelProperty("通知天数")
    private Integer day;

    @ApiModelProperty("通知天数")
    private String dayTxt;

    @ApiModelProperty("通知周期")
    private Integer loop;

    @ApiModelProperty("循环通知，循环结束天数")
    private Integer loopDay;

    @ApiModelProperty("发送途径")
    private String channel;

    @ApiModelProperty("通知轮次")
    private NotificationCycleTypeEnum round;

    @ApiModelProperty("消息发送规则")
    private NoticeRule rule;

    @ApiModelProperty("状态")
    private EnumSimple status;



}
