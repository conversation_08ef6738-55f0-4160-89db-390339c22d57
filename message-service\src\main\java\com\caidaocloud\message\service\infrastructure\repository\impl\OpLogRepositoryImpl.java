package com.caidaocloud.message.service.infrastructure.repository.impl;

import com.caidaocloud.message.service.infrastructure.repository.po.LogPo;
import org.springframework.stereotype.Repository;

@Repository
public class OpLogRepositoryImpl {
    private static String COLLECTION_PREFIX = "oplog_%s";

    public void insert(LogPo po) {
        String collection = String.format(COLLECTION_PREFIX, po.getTenantId());
        //mongodbDao.save(po, collection);
    }
}
