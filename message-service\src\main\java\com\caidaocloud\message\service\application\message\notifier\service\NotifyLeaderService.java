package com.caidaocloud.message.service.application.message.notifier.service;

import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDataDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.message.service.application.message.notifier.input.NotifierDataFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 查询 直接上级
 */
@Service
public class NotifyLeaderService implements INotifyTargetService{

    @Override
    public String getNotifyTarget() {
        return NoticeTarget.LEADER.toString();
    }

    @Override
    public void getNotifier(List<NotifierDto> notifier, NotifyObjectDto obj) {
        List<EmpDataVo> leaders = NotifierDataFactory.getNotifierService()
                .getLeadersByEmpIds(NotifierDataDto.bulid(obj));
        if(null == leaders || leaders.isEmpty()){
            return;
        }

        notifier.addAll(convertEmpDataList(leaders));
    }
}
