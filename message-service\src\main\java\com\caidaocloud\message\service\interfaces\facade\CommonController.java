package com.caidaocloud.message.service.interfaces.facade;

import com.caidaocloud.message.service.interfaces.dto.base.SelectOptionDto;
import com.caidaocloud.message.service.application.common.service.SelectOptionService;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/msg/common/v1")
@Api(value = "/api/msg/common/v1", description = "通用下拉框枚举", tags = "v0.0")
public class CommonController {
    @Resource
    private SelectOptionService selectOptionService;
    @Resource
    private MqMessageProducer producer;

    @ApiOperation("获取下拉列表")
    @GetMapping("/select")
    public Result select(@RequestParam("model") String model, @RequestParam("prop") String prop) {
        SelectOptionDto dto = new SelectOptionDto(model, prop);
        return Result.ok(selectOptionService.getOptionEnumDef(dto));
    }

    @GetMapping("/sendMsg")
    public void testMsg(){
        String body = "{\"channel\":[\"1\"],\"emailMsg\":{\"content\":\"<p>Dear<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#流程.审批人#\\\">#流程.审批人#</span><span style=\\\"font-family: 宋体;\\\">，</span> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </p><p> &nbsp; &nbsp; &nbsp; &nbsp; <span style=\\\"font-family: 宋体;\\\">有一条人员调转流程任务等待您处理。</span></p><p> &nbsp; &nbsp; &nbsp; &nbsp; <span style=\\\"font-family: 宋体;\\\">本流程到达您那需要尽快处理，否则会影响该流程生效日期。</span> </p><p><br></p><p><span style=\\\"font-family: 宋体;\\\"> &nbsp; &nbsp; &nbsp; 异动员工工号：</span><span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#流程.申请人工号#\\\">#流程.申请人工号#</span> &nbsp; &nbsp; </p><p> &nbsp; &nbsp; &nbsp; &nbsp; <span style=\\\"font-family: 宋体;\\\">异动员工姓名：</span><span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#流程.申请人#\\\">#流程.申请人#</span> &nbsp;</p><p> &nbsp; &nbsp; &nbsp; &nbsp; <span style=\\\"font-family: 宋体;\\\">拟调整生效日期：</span><span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"2023-03-07\\\">2023-03-07</span> &nbsp; &nbsp; &nbsp;</p><p><br></p><p> &nbsp; &nbsp; &nbsp; &nbsp; <span style=\\\"font-family: 宋体;\\\">请</span><a href=\\\"https://hcm-test.ciwork.cn/module/workflow/backlog\\\" target=\\\"_blank\\\"><span style=\\\"font-family: 宋体;\\\">点击这里</span></a><span style=\\\"font-family: 宋体;\\\">进入工作流系统处理。</span></p>\",\"subject\":\"操作提醒：员工异动信息填写-#流程.申请人工号#，#流程.申请人#\",\"to\":\"<EMAIL>\"},\"tenantId\":\"8\"}";
        log.info("[publishToMessageService] prepare send message to rabbitmq, body=[{}]", body);

        var rabbitBaseMessage = new RabbitBaseMessage();
        rabbitBaseMessage.setExchange("message.fac.direct.exchange");
        rabbitBaseMessage.setRoutingKey("routingKey.message.plain.msg");
        rabbitBaseMessage.setBody(body);
        log.info("[publishToMessageService] success send message, body=[{}]", body);
        producer.publish(rabbitBaseMessage);
    }
}
