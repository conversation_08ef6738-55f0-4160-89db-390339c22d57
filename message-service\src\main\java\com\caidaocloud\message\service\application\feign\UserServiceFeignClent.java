package com.caidaocloud.message.service.application.feign;


import com.caidaocloud.message.service.application.feign.dto.UserBaseInfoQueryParam;
import com.caidaocloud.message.service.application.feign.dto.UserDetailInfoVo;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
        value = "caidaocloud-user-service",
        fallback = UserServiceFeignClentFallback.class,
        configuration = FeignConfiguration.class,
        contextId = "userServiceFeign"
)
public interface UserServiceFeignClent {


    @PostMapping("/api/user/base/v2/queryListByMobilesAndEmails")
    public Result<List<UserDetailInfoVo>> queryListByMobilesAndEmails(@RequestBody UserBaseInfoQueryParam queryParam);


    @GetMapping("/api/user/base/v2/getUserById")
    @ApiOperation("根据用户ID查询用户信息")
    public Result<UserDetailInfoVo> getUserById(@RequestParam("userId") Long userId);



    @GetMapping("/api/user/base/v2/getUserByIds")
    @ApiOperation("根据用户ID查询用户信息")
    Result<List<UserDetailInfoVo>> getUserByIds(@RequestBody List<Long> userIds);


    @GetMapping("/api/user/base/v2/info")
    @ApiOperation("根据empId查询用户信息")
    public Result<UserDetailInfoVo> getUserByEmpId(@RequestParam("empId") Long empId);


}
