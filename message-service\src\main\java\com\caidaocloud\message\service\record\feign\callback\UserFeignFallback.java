package com.caidaocloud.message.service.record.feign.callback;

import com.caidaocloud.message.service.record.bean.UserAccountInfo;
import com.caidaocloud.message.service.record.feign.UserFeign;
import com.caidaocloud.web.Result;

/**
 * created by: FoAng
 * create time: 28/5/2024 5:15 下午
 */
public class UserFeignFallback implements UserFeign {

    @Override
    public Result<UserAccountInfo> getUserAndAccountInfo(Long userId) {
        return Result.fail("获取员工信息失败");
    }
}
