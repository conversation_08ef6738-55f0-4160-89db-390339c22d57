package com.caidaocloud.message.service.interfaces.dto.template;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class TemplateQueryDto extends BasePage {

    @ApiModelProperty("模版名称")
    private String name;

    @ApiModelProperty("模版分类")
    private EnumSimple category;

    @ApiModelProperty("模版bid")
    private List<String> templateBidList;

}
