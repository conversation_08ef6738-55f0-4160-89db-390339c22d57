package com.caidaocloud.message.service.record.mybatis;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.caidaocloud.message.service.record.properties.LogProviderProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@MapperScan("com.caidaocloud.record.**.mapper")
public class MybatisPlusConfiguration {

	@Resource
	private LogProviderProperties recordProperties;

	@Bean
	public DynamicTableNameHandler dynamicTableNameHandler() {
		return new DynamicTableNameHandler();
	}

	@Bean
	public MybatisPlusInterceptor mybatisPlusInterceptor(DynamicTableNameHandler tableNameHandler) {
		MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
		DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
		dynamicTableNameInnerInterceptor.setTableNameHandler(tableNameHandler);
		interceptor.addInnerInterceptor(dynamicTableNameInnerInterceptor);
		interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.getDbType(recordProperties.getDbType())));
		return interceptor;
	}
}

