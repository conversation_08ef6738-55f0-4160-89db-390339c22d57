package com.caidaocloud.message.service.application.msg.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.message.service.application.common.constant.CacheCodeConstant;
import com.caidaocloud.message.service.application.common.service.BaseServiceImpl;
import com.caidaocloud.message.service.application.email.dto.HtmlMailDto;
import com.caidaocloud.message.service.application.email.dto.MailMsgSender;
import com.caidaocloud.message.service.application.email.service.EmailNotifyFactory;
import com.caidaocloud.message.service.domain.base.service.BaseDomainService;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.domain.msg.entity.EmailConfigDo;
import com.caidaocloud.message.service.domain.msg.service.EmailConfigDomainService;
import com.caidaocloud.message.service.infrastructure.utils.HolderUtil;
import com.caidaocloud.message.service.interfaces.dto.msg.EmailConfigDto;
import com.caidaocloud.util.ObjectConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/6 下午6:07
 */
@Slf4j
@Service
public class EmailConfigService extends BaseServiceImpl<EmailConfigDo, BasePage> {
    @Resource
    private EmailConfigDomainService domainService;

    @Override
    protected BaseDomainService getDomainService() {
        return domainService;
    }

    public EmailConfigDo saveEmailConfig(EmailConfigDto config){
        List<EmailConfigDo> configs = domainService.selectList();
        EmailConfigDo convert = ObjectConverter.convert(config, EmailConfigDo.class);
        if(null == configs || configs.isEmpty()){
            domainService.save(convert);
            return convert;
        }

        if(configs.size() > 1){
            List<String> collect = configs.stream().map(EmailConfigDo::getBid).collect(Collectors.toList());
            domainService.deleteBatchIds(collect);
        } else {
            convert.setBid(configs.get(0).getBid());
        }

        convert.setTenantId(UserContext.getTenantId());
        domainService.saveOrUpdate(convert);
        return convert;
    }

    public boolean sendTestEmail(EmailConfigDto config){
        MailMsgSender msg = new MailMsgSender();
        msg.setTenantId(UserContext.getTenantId());
        msg.setContent("This is test email #1 sent from CAIDAO.");
        msg.setSubject("Test email");
        msg.setTo(config.getTestMail());

        HtmlMailDto mailUtil = new HtmlMailDto();
        mailUtil.setUser(Optional.ofNullable(config.getEmailUser()).orElse(config.getEmailFrom()));
        mailUtil.setPassword(config.getEmailPwd());
        mailUtil.setSmtp(config.getEmailSmtp());
        mailUtil.setFrom(config.getEmailFrom());
        mailUtil.setNick(config.getEmailNick());
        Optional.ofNullable(config.getEmailPort()).filter(it -> it != -1)
                .ifPresent(it -> {
                    mailUtil.setPort(it.toString());
                });
        try {
            HolderUtil.set(CacheCodeConstant.STMP_EMAIL_CONFIG, mailUtil);
            EmailNotifyFactory.getEmailNotify("smtp").send(msg);
            return true;
        } catch (Exception e){
            log.error("sendTestEmail err,{}", e.getMessage(), e);
        } finally {
            HolderUtil.clear();
        }
        return false;
    }
}
