package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.message.service.application.app.dto.AppMsgSender;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AppAdapterFeignClientFallBack implements AppAdapterFeignClient{
    @Override
    public Result sendMesage(AppMsgSender messageDto) {
        return Result.fail();
    }

    @Override
    public Result batchSendMesage(List<AppMsgSender> msgList) {
        return Result.fail();
    }
}
