import com.caidaocloud.record.core.beans.LogRecordData;
import lombok.Data;

import java.util.Optional;

/**
 * created by: FoAng
 * create time: 23/5/2024 3:09 下午
 */
public class ClassTest {

    public static void main(String[] args) {
        Person person = null;
        System.out.println(Optional.ofNullable(person).map(it -> person.name).orElse("hahaha"));
    }

    @Data
    static class Person {
        String name;
    }
}
