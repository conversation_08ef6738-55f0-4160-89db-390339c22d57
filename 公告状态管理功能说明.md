# 公告状态管理功能说明

## 功能概述
为公告系统新增了完整的状态管理功能，包括发布、失效、撤回等操作，以及自动化的定时任务处理。

## 实现的功能

### 1. 发布功能
- **触发方式**: 手动点击发布按钮
- **业务逻辑**:
  - 修改公告状态为"已发布"
  - 记录发布时间（publishTime字段）
  - 若当前时间 ≥ 生效时间，自动复制公告到已生效公告表（AnnouncementActive）
- **API接口**: `POST /api/announcement/publish`

### 2. 失效功能
- **触发方式**: 手动点击失效按钮
- **业务逻辑**:
  - 修改公告状态为"失效"
  - 同步删除已生效公告表中的对应数据
- **API接口**: `POST /api/announcement/expire`

### 3. 撤回功能
- **触发方式**: 手动点击撤回按钮（仅限已发布但未生效的公告）
- **业务逻辑**:
  - 检查公告是否已生效（当前时间 < 生效时间）
  - 修改公告状态为"未发布"
  - 清除发布时间
- **限制条件**: 只有已发布且未生效的公告才能撤回
- **API接口**: `POST /api/announcement/withdraw`

### 4. 定时任务功能
使用xxl-job实现自动化处理：

#### 4.1 综合状态处理任务
- **任务名**: `announcementStatusJobHandler`
- **功能**: 同时处理生效和失效逻辑
- **执行逻辑**:
  - 查询已发布且到达生效时间的公告，复制到生效表
  - 查询已发布且到达失效时间的公告，修改状态并删除生效数据

#### 4.2 公告生效任务
- **任务名**: `announcementEffectiveJobHandler`
- **功能**: 专门处理公告生效
- **执行逻辑**: 将已发布且生效的公告复制到已生效公告表

#### 4.3 公告失效任务
- **任务名**: `announcementExpiredJobHandler`
- **功能**: 专门处理公告失效
- **执行逻辑**: 将已发布且失效的公告修改为失效状态，并删除生效数据

### 5. 编辑和删除限制
- **限制规则**: 已发布的公告不能编辑或删除
- **实现方式**: 在更新和删除操作前检查公告状态
- **错误提示**: 返回友好的错误信息

## 文件结构

### 1. 实体层扩展
- **Announcement.java**: 添加了`publishTime`字段记录发布时间
- **AnnouncementActive.java**: 添加了`originalAnnouncementId`字段关联原公告

### 2. Repository层
- **AnnouncementActiveRepository.java**: 已生效公告Repository接口
- **AnnouncementActiveRepositoryImpl.java**: 已生效公告Repository实现
- **AnnouncementRepository.java**: 扩展了状态查询方法
- **AnnouncementRepositoryImpl.java**: 实现了状态查询方法

### 3. 服务层
- **AnnouncementStatusService.java**: 公告状态管理核心服务
- **AnnouncementApplicationService.java**: 扩展了状态变更方法和权限检查

### 4. 定时任务层
- **AnnouncementScheduleService.java**: xxl-job定时任务服务

### 5. 控制器层
- **AnnouncementController.java**: 扩展了状态变更API接口

## 状态流转图

```
未发布(UNPUBLISHED) 
    ↓ [发布]
已发布(PUBLISHED) 
    ↓ [自动/定时任务]
已生效(复制到AnnouncementActive表)
    ↓ [自动/定时任务]
已失效(EXPIRED)
```

### 状态变更规则
1. **未发布 → 已发布**: 通过发布操作
2. **已发布 → 未发布**: 通过撤回操作（仅限未生效时）
3. **已发布 → 已失效**: 通过失效操作或定时任务
4. **已生效数据管理**: 通过AnnouncementActive表独立管理

## API接口说明

### 状态变更接口

#### 1. 发布公告
```http
POST /api/announcement/publish?announcementId={id}
```
**响应**: `{"success": true, "data": true}`

#### 2. 失效公告
```http
POST /api/announcement/expire?announcementId={id}
```
**响应**: `{"success": true, "data": true}`

#### 3. 撤回公告
```http
POST /api/announcement/withdraw?announcementId={id}
```
**响应**: `{"success": true, "data": true}`

### 错误处理
所有接口都包含完整的异常处理：
- 参数验证错误
- 业务逻辑错误（如状态不允许变更）
- 系统异常错误

## 定时任务配置

### xxl-job任务配置
需要在xxl-job管理平台配置以下任务：

1. **公告状态综合处理**
   - 任务名: `announcementStatusJobHandler`
   - 建议执行频率: 每小时执行一次
   - Cron表达式: `0 0 * * * ?`

2. **公告生效处理**
   - 任务名: `announcementEffectiveJobHandler`
   - 建议执行频率: 每30分钟执行一次
   - Cron表达式: `0 */30 * * * ?`

3. **公告失效处理**
   - 任务名: `announcementExpiredJobHandler`
   - 建议执行频率: 每小时执行一次
   - Cron表达式: `0 0 * * * ?`

### 租户配置
在application.yml中配置租户列表：
```yaml
xxl:
  job:
    tenant:
      list: tenant1,tenant2,tenant3
```

## 数据库变更

### 1. Announcement表
- 新增字段: `publishTime` (BIGINT) - 发布时间

### 2. AnnouncementActive表
- 新增字段: `originalAnnouncementId` (VARCHAR) - 原公告ID

## 业务规则

### 1. 发布规则
- 只有未发布状态的公告才能发布
- 发布时自动记录发布时间
- 如果当前时间已达到生效时间，立即复制到生效表

### 2. 撤回规则
- 只有已发布状态的公告才能撤回
- 只有未生效的公告才能撤回（当前时间 < 生效时间）
- 撤回后状态变为未发布，清除发布时间

### 3. 失效规则
- 可以手动失效任何已发布的公告
- 定时任务自动失效过期的公告
- 失效时同步删除生效表中的数据

### 4. 编辑删除规则
- 已发布的公告不能编辑
- 已发布的公告不能删除
- 只有未发布状态的公告才能编辑和删除

## 日志记录
所有状态变更操作都有详细的日志记录：
- 操作类型和参数
- 执行结果
- 异常信息
- 处理数量统计

## 性能考虑
1. **批量处理**: 定时任务支持批量处理多个公告
2. **租户隔离**: 按租户分别处理，避免跨租户影响
3. **事务管理**: 所有状态变更操作都在事务中执行
4. **异常处理**: 单个公告处理失败不影响其他公告

## 扩展建议
1. 可以添加状态变更历史记录表
2. 可以添加公告审批流程
3. 可以添加公告推送通知功能
4. 可以添加公告阅读统计功能
