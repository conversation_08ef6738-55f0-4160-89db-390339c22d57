package com.caidaocloud.message.service.record.consumer;

import com.caidaocloud.message.service.record.service.RecordService;
import com.caidaocloud.mq.rabbitmq.consumer.AbsMQConsumer;
import com.caidaocloud.record.core.beans.LogRecordData;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 日志消息处理
 * created by: FoAng
 * create time: 22/5/2024 4:58 下午
 */
@Slf4j
@Component
public class LogRecordConsumer extends AbsMQConsumer {

    @Resource
    private RecordService recordService;

    @Override
    public void process(String message) {
        LogRecordData recordData = FastjsonUtil.toObject(message, LogRecordData.class);
        recordService.saveRecordData(recordData);
    }
}
