package com.caidaocloud.message.service.application.feign;

import java.util.List;

import com.caidaocloud.hr.service.vo.EmpPrivateInfoVo;
import com.caidaocloud.hr.service.dto.EmpWorkInfoDto;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "caidaocloud-hr-service",
        fallback = HrServiceFeignClientFallBack.class,
        configuration = FeignConfiguration.class,
contextId = "hrServiceFeign")
public interface HrServiceFeignClient {

    @GetMapping("/api/hr/emp/work/v1/hrbp")
    public Result<EmpWorkInfoDto> getOrganizeHrbp(@RequestParam("organize") String organize, @RequestParam("dataTime") Long dataTime);

    @GetMapping("/api/hr/emp/work/v1/allHrbp")
    public Result<List<EmpWorkInfoDto>>  getAllOrganizeHrbp(@RequestParam("dataTime") Long dataTime);

    @PostMapping("/api/hr/emp/work/v1/getAllEmpList")
    public Result<List<EmpWorkInfoDto>> getAllEmpList();

    @GetMapping("/api/hr/emp/work/v1/allLeader")
    public Result<List<EmpWorkInfoDto>> getAllOrganizeLeader(@RequestParam("dataTime") Long dataTime);

    @GetMapping("/api/hr/emp/privateinfo/v1/getAllGuardianList")
    public Result<List<EmpPrivateInfoVo>> allGuardian();

    @GetMapping("/api/hr/emp/work/v1/detail")
    Result<EmpWorkInfoDto> getEmpWorkInfo(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime);

    @GetMapping("/api/hr/emp/work/v1/empWorkInfoByEmpIds")
    Result<List<EmpWorkInfoVo>> getEmpWorkInfoByEmpIds(@RequestParam("empIds") String empIds);

    @GetMapping("/api/hr/emp/privateinfo/v1/privateInfoByEmpIds")
    public Result<List<EmpPrivateInfoVo>> getEmpPrivateInfoByEmpIds(@RequestParam("empIds") String empIds);

    @GetMapping("/api/hr/emp/privateinfo/v1/detail")
    public Result<EmpPrivateInfoVo> getDetail(@RequestParam("empId") String empId);

    @GetMapping("/api/hr/emp/work/v1/hrbpByEmpId")
    public Result<EmpWorkInfoDto> getHrbpByEmpId(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime);

    @GetMapping("/api/hr/emp/work/v1/leaderByEmpId")
    public Result<EmpWorkInfoDto> getLeaderByEmpId(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime);

    @GetMapping("/api/hr/emp/work/v1/orgLeaderByEmpId")
    public Result<EmpWorkInfoDto> getOrgLeaderByEmpId(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime);
}
