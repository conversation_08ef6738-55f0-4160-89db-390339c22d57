package com.caidaocloud.message.service.application.email.service;

import com.caidaocloud.message.service.application.email.dto.MailMsgSender;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * EmailNotify
 *
 * <AUTHOR>
 * @date 2022/6/6 下午3:14
 */
public interface EmailNotify {

    ConcurrentHashMap<String, EmailNotify> emailManager = new ConcurrentHashMap();

    @PostConstruct
    default void register() {
        String type = getEmailType();
        emailManager.put(type, this);
    }

    String getEmailType();

    default void send(MailMsgSender sender){
        sendMsg(sender);
    }

    boolean sendMsg(MailMsgSender sender);

    default void batchSend(List<MailMsgSender> senderList){
        batchSendMsg(senderList);
    }

    boolean batchSendMsg(List<MailMsgSender> senderList);

}
