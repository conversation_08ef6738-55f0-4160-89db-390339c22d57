package com.caidaocloud.message.sdk.utils;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UserCheck {
    public static UserInfo preCheckUser() {
        UserInfo userInfo = null;
        try {
            userInfo = SpringUtil.getBean(ISessionService.class).getUserInfo();
        } catch (Exception e){
            log.warn("UserContext,get user by session err.");
        }

        if(null == userInfo){
            SecurityUserInfo ui = SecurityUserUtil.getSecurityUserInfo();
            if(null != ui){
                userInfo = new UserInfo();
                userInfo.setTenantId(ui.getTenantId());
                userInfo.setUserid(null != ui.getUserId() ? ui.getUserId().intValue() : null);
                userInfo.doSetUserId(ui.getUserId());
                userInfo.setStaffId(ui.getEmpId());
            }
        }

        PreCheck.preCheckArgument(checkUser(userInfo), "登录失效");
        return userInfo;
    }

    public static boolean checkUser(UserInfo userInfo) {
        return null == userInfo || null == userInfo.getUserId();
    }
}
