package com.caidaocloud.record.core.service.impl;


import com.caidaocloud.record.core.beans.Operator;
import com.caidaocloud.record.core.service.IOperatorGetService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;


public class DefaultOperatorGetServiceImpl implements IOperatorGetService {

    @Override
    public Operator getUser() {
        Operator operator = new Operator();
        SecurityUserInfo securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
        if (securityUserInfo != null && securityUserInfo.getUserId() != null) {
            operator.setTenantId(securityUserInfo.getTenantId());
            operator.setOperatorId(securityUserInfo.getUserId());
            operator.setEmpId(securityUserInfo.getEmpId());
        } else {
            operator.setOperatorId(0L);
            operator.setTenantId("0");
            operator.setEmpId(0L);
        }
        return operator;
    }
}
