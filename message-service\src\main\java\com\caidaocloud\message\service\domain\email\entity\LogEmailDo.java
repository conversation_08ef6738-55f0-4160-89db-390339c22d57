package com.caidaocloud.message.service.domain.email.entity;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.service.domain.base.entity.DataEntity;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.domain.email.dto.LogEmailDto;
import com.caidaocloud.message.service.domain.email.repository.LogEmailRepository;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Data
@Service
@Slf4j
public class LogEmailDo extends DataEntity {

    // 部门id
    private String organization;
    //操作时间
    private Long operTime;
    //错误信息
    private String errorMsg;
    //模板信息
    private String emailTemplate;

    private final static String LOGEMAIL_IDENTIFIER = "entity.message.LogEmail";

    @Resource
    private LogEmailRepository logEmailRepository;

    public String save(LogEmailDto logEmailDto) {
        LogEmailDo data = ObjectConverter.convert(logEmailDto, LogEmailDo.class);
        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        String tenantId = null == userInfo || null == userInfo.getTenantId() ? null : userInfo.getTenantId();
        data.setTenantId(tenantId);
        data.setCreateTime(System.currentTimeMillis());
        data.setUpdateBy(userId);
        data.setUpdateTime(data.getCreateTime());
        data.setIdentifier(LOGEMAIL_IDENTIFIER);
        data.setDeleted(Boolean.FALSE);
        return logEmailRepository.insert(data).getBid();
    }


    public void update(LogEmailDto logEmailDto) {
        logEmailRepository.updateById(ObjectConverter.convert(logEmailDto, LogEmailDo.class));
    }
}
