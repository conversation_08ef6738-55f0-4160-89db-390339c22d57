package com.caidaocloud.message.service.application.feign;

import java.util.List;

import com.caidaocloud.hr.service.dto.EmpWorkInfoDto;
import com.caidaocloud.hr.service.vo.EmpPrivateInfoVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.web.Result;

import org.springframework.stereotype.Component;

@Component
public class HrServiceFeignClientFallBack implements HrServiceFeignClient {

    @Override
    public Result<EmpWorkInfoDto> getOrganizeHrbp(String organize, Long dataTime) {
        return null;
    }

    @Override
    public Result<List<EmpWorkInfoDto>> getAllOrganizeHrbp(Long dataTime) {
        return null;
    }

    @Override
    public Result<List<EmpWorkInfoDto>> getAllEmpList() {
        return null;
    }

    @Override
    public Result<List<EmpWorkInfoDto>> getAllOrganizeLeader(Long dataTime) {
        return null;
    }

    @Override
    public Result<List<EmpPrivateInfoVo>> allGuardian() {
        return null;
    }

    @Override
    public Result<EmpWorkInfoDto> getEmpWorkInfo(String empId, Long dataTime) {
        return null;
    }

    @Override
    public Result<List<EmpWorkInfoVo>> getEmpWorkInfoByEmpIds(String empIds) {
        return null;
    }

    @Override
    public Result<List<EmpPrivateInfoVo>> getEmpPrivateInfoByEmpIds(String empIds) {
        return null;
    }

    @Override
    public Result<EmpPrivateInfoVo> getDetail(String empId) {
        return null;
    }

    @Override
    public Result<EmpWorkInfoDto> getHrbpByEmpId(String empId, Long dataTime) {
        return null;
    }

    @Override
    public Result<EmpWorkInfoDto> getLeaderByEmpId(String empId, Long dataTime) {
        return null;
    }

    @Override
    public Result<EmpWorkInfoDto> getOrgLeaderByEmpId(String empId, Long dataTime) {
        return null;
    }
}
