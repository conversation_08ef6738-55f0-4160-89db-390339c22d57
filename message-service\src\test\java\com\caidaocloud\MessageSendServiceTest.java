package com.caidaocloud;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.message.send.MessageSendService;
import com.caidaocloud.message.service.application.message.send.sms.MessageNotifyApiImpl;
import com.caidaocloud.message.service.application.msg.dto.SmsSendMessageDto;
import com.caidaocloud.message.service.application.msg.event.ContractEventEntity;
import com.caidaocloud.message.service.application.sms.dto.SmsMessageDto;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Maps;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.HashMap;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class MessageSendServiceTest {

    @Resource
    private MessageSendService messageSendService;

    @Autowired
    public MessageNotifyApiImpl messageNotifyApi;

    @Test
    public void testSendSms(){
        ContractEventEntity eventEntity = new ContractEventEntity();
        eventEntity.setTenantId("11");
        eventEntity.setMsgConfig("1968930153289728");
        eventEntity.setUserId(0L);
        eventEntity.setSubjects(Lists.newArrayList("1484135313709057"));
        eventEntity.setMdc("A1484135313709057");
        eventEntity.setCreateTime(System.currentTimeMillis());

        String messageJson = "{\"createTime\":1665215516634,\"msgConfig\":\"1509548662750391\",\"msgFrom\":\"OnBoarding\",\"subjects\":[\"1515921180203063\"],\"tenantId\":\"33\",\"type\":1,\"userId\":1433852875044869}";
        messageJson = "{\"createTime\":1665544069109,\"msgConfig\":\"1622022495188994\",\"msgFrom\":\"OnBoarding\",\"subjects\":[\"1542038473201665\"],\"tenantId\":\"33\",\"type\":0,\"userId\":1509409451989023}";
        messageJson = "{\"createTime\":1680170066914,\"msgConfig\":\"1968930153289728\",\"msgFrom\":\"Transfer\",\"subjects\":[\"1717681320269825\"],\"tenantId\":\"11\",\"type\":0,\"userId\":0}";
        System.out.println(messageJson);
        System.out.println();
        ContractEventEntity entity = FastjsonUtil.toObject(messageJson, ContractEventEntity.class);
        messageSendService.doConsumeMessage(entity);
        System.out.println("-------ok---------");
        //messageSendService.doConsumeMessage(eventEntity);
    }

    @Test
    public void testSendSms2(){
//        ContractEventEntity eventEntity = new ContractEventEntity();
//        eventEntity.setTenantId("8");
//        eventEntity.setMsgConfig("1603751101782017");
//        eventEntity.setUserId(0L);
//        eventEntity.setSubjects(Lists.newArrayList("1484135313709057"));
//        eventEntity.setMdc("A1484135313709057");
//        eventEntity.setCreateTime(System.currentTimeMillis());

        String messageJson = "{\"createTime\":1665544069109,\"msgConfig\":\"1603751102552121\",\"msgFrom\":\"OnBoarding\",\"subjects\":[\"1542038473201665\"],\"tenantId\":\"8\",\"type\":0,\"userId\":1509409451989023}";
        System.out.println(messageJson);
        System.out.println();
        ContractEventEntity entity = FastjsonUtil.toObject(messageJson, ContractEventEntity.class);
//        messageSendService.doConsumeMessage(entity);
        System.out.println("-------ok---------");
        //messageSendService.doConsumeMessage(eventEntity);
    }


    @Before
    public void initUser(){
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        userInfo.setUserId(0L);
        userInfo.setEmpId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }

    @Test
    public void sendSmsByRocheApi() {
        SmsMessageDto smsSendMessageDto = new SmsMessageDto();
        smsSendMessageDto.setContent("111111");
        smsSendMessageDto.setMsgCode("SMS_250525055");
        SmsMessageDto.MobileDto mobileDto = new SmsMessageDto.MobileDto();
        mobileDto.setMobile("15238601862");
        smsSendMessageDto.setParams(Lists.newArrayList(new HashMap<>()));
        smsSendMessageDto.setMobile(Lists.newArrayList(mobileDto));
//        messageNotifyApi.sendMessage(smsSendMessageDto);
    }
}
