package com.caidaocloud.message.service.domain.base.repository;

import java.util.List;

import com.alibaba.nacos.api.naming.pojo.ListView;
import com.caidaocloud.hr.service.dto.EmpWorkInfoDto;
import com.caidaocloud.hr.service.vo.EmpPrivateInfoVo;
import com.caidaocloud.web.Result;

/**
 * 通知对象
 * 包含员工的手机号、公司邮箱、个人邮箱
 * <AUTHOR>
 * @date 2023/7/27
 */
public interface INotifyEmpRepository {

	Result<List<EmpWorkInfoDto>> loadAllEmp();

	Result<List<EmpPrivateInfoVo>> loadAllGuardian();

	Result<List<EmpWorkInfoDto>> loadAllHrbp(long currentTimeMillis);

	Result<List<EmpWorkInfoDto>> loadAllLeader(long currentTimeMillis);

	Result<List<EmpWorkInfoDto>> loadEmpList(String empIds);

	Result<EmpWorkInfoDto> loadLeader(String empId, long datetime);

	Result<List<EmpPrivateInfoVo>> loadPrivateInfoList(String empIdStr);

	Result<EmpWorkInfoDto> loadHrbp(String empId, long datetime);

	Result<EmpWorkInfoDto> loadOrgLeader(String empId, long datetime);

	Result<EmpPrivateInfoVo> loadPrivateInfo(String empId);
}
