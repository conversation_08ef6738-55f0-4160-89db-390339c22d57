package com.caidaocloud.message.service.interfaces.dto.msg;

import com.caidaocloud.condition.tree.ConditionTree;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.message.sdk.dto.MergeRuleDto;
import com.caidaocloud.message.sdk.enums.NoticeRule;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.service.interfaces.dto.template.TemplateDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Data
@ApiModel("消息设置dto")
public class MsgConfigDto {
    private String bid;

    @ApiModelProperty("消息通知名称")
    private String name;

    @ApiModelProperty("消息通知名称多语言")
    private Map<String, Object> i18nName;

    @ApiModelProperty("通知类型\n" +
            "0 续签未签署提醒\n" +
            "1 电子合同签署提醒\n" +
            "2 电子合同作废提醒\n" +
            "3 手动推送")
    private NoticeType msgType;

    // @ApiModelProperty("通知对象\n" +
    //         "0 HRBP\n" +
    //         "1 指定系统人员\n" +
    //         "2 上级领导\n" +
    //         "3 部门负责人\n" +
    //         "4 事件人员\n" +
    //         "5 全部人员\n" +
    //         "6 监护人")
    // private EnumSimple notifyObject;

    @ApiModelProperty("通知对象\n" +
            "hrbp HRBP\n" +
            "assignEmp 指定系统人员\n" +
            "leader 上级领导\n" +
            "orgLeader 部门负责人\n" +
            "eventEmp 事件人员\n" +
            "allEmp 全部人员\n" +
            "guardian 监护人")
    private List<String> notifyObject;

    private ConditionTree condition;

    @ApiModelProperty("通知方式\n" +
            "0 单次通知\n" +
            "1 循环通知")
    private String func;

    @ApiModelProperty("通知轮次\n" +
            "0 提前\n" +
            "1 当天\n" +
            "2 延后")
    private String round;

    @ApiModelProperty("通知天数")
    private Integer day;

    @ApiModelProperty("通知日期")
    private Long triggerDate;

    @ApiModelProperty("发送时间,秒级")
    private Integer sendTime;

    @ApiModelProperty("通知周期")
    private Integer loop;

    @ApiModelProperty("发送途径")
    private List<String> channel;

    @ApiModelProperty("通知模板")
    private List<TemplateDto> templates;

    @ApiModelProperty("指定系统人员")
    private List<EmpSimple> emps;

    @ApiModelProperty("通知规则")
    private NoticeRule rule;

    /**
     * 循环通知，循环结束天数
     */
    @ApiModelProperty("循环天数")
    private Integer loopDay;

    @ApiModelProperty("合并规则")
    private MergeRuleDto mergeRule;

    @ApiModelProperty("事件人过滤")
    private Boolean subjectFilter = false;

    public String getTemplateName() {
        return Optional.ofNullable(name).orElseGet(() -> (String) this.i18nName.getOrDefault("default",
                ""));
    }
}
