package com.caidaocloud.message.service.infrastructure.repository.impl.msg;

import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.message.service.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.message.service.domain.email.entity.LogEmailDo;
import com.caidaocloud.message.service.domain.email.repository.LogEmailRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

@Slf4j
@Repository
public class LogEmailRepositoryImpl extends BaseRepositoryImpl<LogEmailDo> implements LogEmailRepository {

    @Override
    public LogEmailDo insert(LogEmailDo logEmailDo){
        String bid = DataInsert.identifier(logEmailDo.getIdentifier()).insert(logEmailDo);
        logEmailDo.setBid(bid);
        return logEmailDo;
    }
}
