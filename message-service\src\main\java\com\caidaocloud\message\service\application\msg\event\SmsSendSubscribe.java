package com.caidaocloud.message.service.application.msg.event;

import com.caidaocloud.message.service.application.message.send.sms.SmsMessageNotifyFactory;
import com.caidaocloud.message.service.application.msg.dto.SmsSendMessageDto;
import com.caidaocloud.message.service.application.sms.dto.SmsMessageDto;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SmsSendSubscribe {

    @RabbitHandler
    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = "caidaocloud.user.message.sms.send.queue", durable = "true"),
                    exchange = @Exchange(value = "user.message.fac.direct.exchange"),
                    key = {"routingKey.user.message.sms.send"}
            )
    )
    public void process(String message) {
        log.info("Subscribe SmsSendSubscribe start time={} message={}", System.currentTimeMillis(), message);
        try {
            SmsSendMessageDto messageDto = FastjsonUtil.toObject(message, SmsSendMessageDto.class);

            List<String> phoneList = messageDto.getPhoneNumbers();
            if (CollectionUtils.isEmpty(phoneList)) {
                log.info("SmsSendSubscribe phoneList empty");
                return;
            }

            // 手机号去重
            Set<String> phoneSet = new HashSet<>(phoneList);
            phoneList.clear();
            phoneList.addAll(phoneSet);

            // 构造消息结构体
            SmsMessageDto smsMessageDto = new SmsMessageDto();
            smsMessageDto.setContent(messageDto.getContent());
            List<SmsMessageDto.MobileDto> mobiles = phoneList.stream().map(phone -> {
                SmsMessageDto.MobileDto mobile = new SmsMessageDto.MobileDto();
                mobile.setMobile(phone);
                return mobile;
            }).collect(Collectors.toList());
            smsMessageDto.setMobile(mobiles);

            // 消息发送
            SmsMessageNotifyFactory.getSmsService().sendSingleMessage(smsMessageDto);
        } catch (Exception ex) {
            log.error("Subscribe SmsSendSubscribe err,{}", ex.getMessage(), ex);
        }
    }
}
