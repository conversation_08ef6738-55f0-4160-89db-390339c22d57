package com.caidaocloud.message.service.application.message.notifier.service;

import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import com.caidaocloud.message.service.application.feign.HrFeignClient;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.message.service.application.message.notifier.dto.RoleUserInfoDto;
import com.caidaocloud.message.service.application.message.notifier.input.NotifierDataFactory;
import lombok.var;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 查询 系统角色下对应的用户
 * 查询 角色对应的用户帐号上绑定的手机号、邮箱
 */
@Service
public class NotifyUserRoleService implements INotifyTargetService {

    @Resource
    private HrFeignClient hrFeignClient;

    @Override
    public String getNotifyTarget() {
        return "AuthRole";
    }

    @Override
    public void getNotifier(List<NotifierDto> notifier, NotifyObjectDto obj) {
        List<RoleUserInfoDto> userInfoByRoles = NotifierDataFactory.getNotifierService().getUserInfoByRoles(obj.getEmpIds());
        if (null == userInfoByRoles || userInfoByRoles.isEmpty()) {
            return;
        }
        var empIdList = userInfoByRoles.stream()
                .filter(e -> e.getEmpId() != null)
                .map(e -> String.valueOf(e.getEmpId())).collect(Collectors.toList());
        var result = hrFeignClient.getEmpListByEmpIds(empIdList);
        Map<String, String> companyEmailMap = null;
        if (result.isSuccess() && !CollectionUtils.isEmpty(result.getData())) {
            companyEmailMap = result.getData().stream()
                    .filter(e -> e.getCompanyEmail() != null)
                    .collect(Collectors.toMap(e -> e.getEmpId(), e -> e.getCompanyEmail(), (v1, v2) -> v1));
        }
        var finalCompanyEmailMap = companyEmailMap;
        List<NotifierDto> collect = userInfoByRoles.stream().map(user -> {
            var empId = user.getEmpId() != null ? String.valueOf(user.getEmpId()) : "";
            NotifierDto notifierDto = new NotifierDto();
            PhoneSimple phoneSimple = new PhoneSimple();
            phoneSimple.setCode(PhoneSimple.DEF_CODE);
            phoneSimple.setUn(PhoneSimple.DEF_UN);
            phoneSimple.setValue(user.getMobile());
            notifierDto.setPhone(phoneSimple);
            notifierDto.setEmail(user.getEmail());
            notifierDto.setEmpId(empId);
            if (finalCompanyEmailMap != null && finalCompanyEmailMap.containsKey(empId)) {
                notifierDto.setCompanyEmail(finalCompanyEmailMap.get(empId));
            }
            return notifierDto;
        }).collect(Collectors.toList());
        notifier.addAll(collect);
    }
}
