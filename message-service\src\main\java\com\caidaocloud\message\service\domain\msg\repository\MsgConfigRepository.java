package com.caidaocloud.message.service.domain.msg.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.service.application.common.enums.MsgConfigStatusEnum;
import com.caidaocloud.message.service.application.msg.dto.MessageConfigPageQueryDto;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;
import com.caidaocloud.message.service.domain.msg.entity.MsgConfigDo;

import java.util.List;

/**
 * MsgConfigRepository
 *
 * <AUTHOR>
 * @date 2022/6/6 下午5:49
 */
public interface MsgConfigRepository extends BaseRepository<MsgConfigDo> {

    /**
     * 根据类型查看消息设置
     *
     * @param noticeType
     * @param status
     * @return
     */
    List<MsgConfigDo> selectByMsgTypeAndStatus(NoticeType noticeType, MsgConfigStatusEnum status);

    List<MsgConfigDo> getMsgConfigByTypeAndIds(List<String> ids, NoticeType noticeType, MsgConfigStatusEnum status);

    List<MsgConfigDo> getEnableMsgConfigList(String noticeType);

    PageResult<MsgConfigDo> selectEnableMsgConfigPageList(MessageConfigPageQueryDto queryDto);

    List<MsgConfigDo> getOnboardingSelectList(List<String> types);
}
