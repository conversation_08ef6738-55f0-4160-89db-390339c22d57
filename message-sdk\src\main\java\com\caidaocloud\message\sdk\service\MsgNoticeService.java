package com.caidaocloud.message.sdk.service;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.sdk.dto.EmpMsgConfigQueryDto;
import com.caidaocloud.message.sdk.dto.MsgConfigDto;
import com.caidaocloud.message.sdk.dto.MsgNoticeDto;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.event.publish.MsgPublish;
import com.caidaocloud.message.sdk.feign.MessageFeignClient;
import com.caidaocloud.message.sdk.utils.UserCheck;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class MsgNoticeService {
    @Resource
    private MessageFeignClient messageFeignClient;
    @Resource
    private MsgPublish msgPublish;

    /**
     * 发送消息通知入参数校验
     */
    private boolean checkArgument(NoticeType nType, List<String> subjects) {
        return null == nType || null == subjects || subjects.isEmpty();
    }

    /**
     * 发送消息通知入参数校验
     */
    private boolean checkArgument(String msgConfigId, List<String> subjects) {
        return StringUtil.isEmpty(msgConfigId) || null == subjects || subjects.isEmpty();
    }

    /**
     * 根据消息类型-获取启用的消息配置列表
     *
     * @param nType
     * @return
     */
    public List<MsgConfigDto> getMsgConfigList(NoticeType nType) {
        Result<List<MsgConfigDto>> result = messageFeignClient.getEnableMsgConfigList(nType.getIndex());
        List<MsgConfigDto> msgConfigList = null;
        if (null == result || !result.isSuccess() ||
                null == (msgConfigList = result.getData()) || msgConfigList.isEmpty()) {
            log.warn("current step not exist msg config. nType={}", nType);
            return msgConfigList;
        }

        return msgConfigList;
    }

    public List<MsgConfigDto> getMsgConfigs(NoticeType nType) {
        Result<String> result = messageFeignClient.getEnableMsgConfigListSequence(nType.getIndex());
        List<MsgConfigDto> msgConfigList = null;
        if (null == result || !result.isSuccess() ||  null == result.getData()) {
            log.warn("current step not exist msg config. nType={}", nType);
            return msgConfigList;
        }
        msgConfigList = FastjsonUtil.toArrayList(result.getData(), MsgConfigDto.class);
        return msgConfigList;
    }

    private MsgConfigDto getMsgConfigOne(String msgConfigId) {
        Result<MsgConfigDto> result = messageFeignClient.getMsgConfigOne(msgConfigId);
        MsgConfigDto msgConfig = null;
        if (null == result || !result.isSuccess() ||
                null == (msgConfig = result.getData()) || StringUtil.isEmpty(msgConfig.getBid())) {
            log.warn("current step not exist msg config. msgConfigId={}", msgConfigId);
            return msgConfig;
        }

        return msgConfig;
    }

    /**
     * 根据消息配置类型发送候选人消息通知
     * 发送消息通知事件
     */
    public void sendMsgNoticeEvent(NoticeType nType, List<String> subjects, Map<String, String> ext) {
        sendMsgNoticeEvent(nType, subjects, ext,"OnBoarding",1);
    }

    /**
     * 根据消息配置类型发送候选人消息通知
     * 发送消息通知事件
     * msgForm 需要自己指定
     * 事件人为 type = 0 员工
     */
    public void sendMsgEmpNoticeEvent(NoticeType nType, List<String> subjects, Map<String, String> ext, String msgForm) {
        sendMsgNoticeEvent(nType, subjects, ext, msgForm,0);
    }

    /**
     * 根据消息配置类型发送候选人消息通知
     * 发送消息通知事件
     * msgForm 需要自己指定
     * 事件人为 type = 1 候选人
     */
    public void sendMsgPreEmpNoticeEvent(NoticeType nType, List<String> subjects, Map<String, String> ext, String msgForm) {
        sendMsgNoticeEvent(nType, subjects, ext, msgForm,1);
    }

    /**
     * 根据消息配置类型发送消息通知
     * 发送消息通知事件
     */
    public void sendMsgNoticeEvent(NoticeType nType, List<String> subjects, Map<String, String> ext, String form, int type) {
        // 入参校验
        if (checkArgument(nType, subjects)) {
            // 参数校验不合法，不继续处理消息
            log.warn("current step parameters check fail. nType={}, subject={}",
                    nType, FastjsonUtil.toJson(subjects));
            return;
        }

        // 配置校验
        List<MsgConfigDto> msgConfigList = getMsgConfigList(nType);
        if (null == msgConfigList || msgConfigList.isEmpty()) {
            return;
        }

        // 发送MQ消息
        sendMqMsg(msgConfigList, subjects, ext, form, type);
    }

    /**
     * 根据消息配置ID发送候选人消息通知
     * 发送消息通知事件
     */
    public void sendMsgNoticeEvent(String msgConfigId, List<String> subjects, Map<String, String> ext) {
        sendMsgNoticeEvent(msgConfigId, subjects, ext,"OnBoarding",1);
    }

    /**
     * 根据消息配置ID发送消息通知
     * 发送消息通知事件
     */
    public void sendMsgNoticeEvent(String msgConfigId, List<String> subjects, Map<String, String> ext, String form, int type) {
        // 入参校验
        if (checkArgument(msgConfigId, subjects)) {
            log.warn("current step parameters check fail. msgConfigId={}, subject={}",
                    msgConfigId, FastjsonUtil.toJson(subjects));
            return;
        }

        // 配置校验
        MsgConfigDto msgConfig = getMsgConfigOne(msgConfigId);
        if (null == msgConfig) {
            return;
        }

        // 发送MQ消息
        sendMqMsg(Lists.one(msgConfig), subjects, ext, form, type);
    }

    private void sendMqMsg(List<MsgConfigDto> msgConfigList, List<String> subjects, Map<String, String> ext, String form, int type) {
        UserInfo userInfo = UserCheck.preCheckUser();

        MsgNoticeDto messageDto = new MsgNoticeDto();
        messageDto.setTenantId(userInfo.getTenantId());
        messageDto.setUserId(userInfo.getUserId());
        messageDto.setSubjects(subjects);
        messageDto.setCreateTime(System.currentTimeMillis());
        messageDto.setExt(ext);
        messageDto.setMsgFrom(form);
        messageDto.setType(type);

        msgConfigList.forEach(mc -> {
            messageDto.setMsgConfig(mc.getBid());
            msgPublish.msgNoticePublish(messageDto, 0);
        });
    }

    public List<MsgConfigDto> getMsgConfigList(NoticeType nType, List<String> empIds) {
        EmpMsgConfigQueryDto configQuery = new EmpMsgConfigQueryDto();
        configQuery.setEmpList(empIds);
        configQuery.setType(nType);
        Result<List<MsgConfigDto>> result = messageFeignClient.getMsgConfig(configQuery);
        List<MsgConfigDto> msgConfigList = null;
        if (null == result || !result.isSuccess() ||
                null == (msgConfigList = result.getData()) || msgConfigList.isEmpty()) {
            log.warn("getMsgConfigList current step not exist msg config. nType={}", nType);
            return msgConfigList;
        }

        return msgConfigList;
    }
}
