package com.caidaocloud.message.service.application.app.service;

import com.caidaocloud.message.service.application.app.dto.AppMsgSender;
import com.caidaocloud.message.service.application.feign.AppAdapterFeignClient;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AdapterAppNotifyService implements AppNotifyService{
    @Resource
    private AppAdapterFeignClient appAdapterFeignClient;

    @Override
    public boolean sendMsg(AppMsgSender sender) {
        Result result = appAdapterFeignClient.sendMesage(sender);
        return null != result && result.isSuccess();
    }

    @Override
    public boolean batchSendMsg(List<AppMsgSender> senderList) {
        Result result = appAdapterFeignClient.batchSendMesage(senderList);
        return null != result && result.isSuccess();
    }

    @Override
    public String getMsgType() {
        return "adapterapp";
    }
}
