package com.caidaocloud.message.service.application.message.notifier.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.message.sdk.dto.PreEmpEntryProcessVo;
import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.service.application.feign.AuthServiceFeignClient;
import com.caidaocloud.message.service.application.feign.OnBoardingFeignClient;
import com.caidaocloud.message.service.application.message.notifier.dto.*;
import com.caidaocloud.message.service.application.message.notifier.input.NotifierDataFactory;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 查询 入职发起人
 * 查询 入职发起人用户对应的用户帐号上绑定的员工身上的手机号、邮箱
 */
@Slf4j
@Service
public class NotifyInitiatorService implements INotifyTargetService {
    @Override
    public String getNotifyTarget() {
        return NoticeTarget.Initiator.toString();
    }

    @Override
    public void getNotifier(List<NotifierDto> notifier, NotifyObjectDto obj) {
        List<EmpDataVo> initiatorByEmpIds = NotifierDataFactory.getNotifierService()
                .getInitiatorDataByEmpIds(NotifierDataDto.bulid(obj));
        if (null == initiatorByEmpIds || initiatorByEmpIds.isEmpty()) {
            return;
        }
        notifier.addAll(convertEmpDataList(initiatorByEmpIds));
    }

}
