package com.caidaocloud.message.service.application.message.send.handler;

import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.send.dto.MessageContext;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Maps;

import javax.annotation.PostConstruct;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;

public interface IMsgHandler {
    ConcurrentMap<String, IMsgHandler> handlerManager = Maps.newConcurrentMap();

    @PostConstruct
    default void register() {
        handlerManager.put(getHandlerType(), this);
    }

    String getHandlerType();

    default void msgHandler(MessageContext context){
        doMsgHandler(context);

        afterHandler(context);
    }

    default void doMsgHandler(MessageContext context){

    }

    default void afterHandler(MessageContext context){

    }

    /**
     * List排重
     */
    default void distinctList(List<String> dataList){
        Set<String> set = new HashSet<>(dataList);
        dataList.clear();
        dataList.addAll(set);
    }

    default void addPhoneList(List<String> phones, NotifierDto notifierDto){
        Optional.ofNullable(notifierDto.getPhone())
                .map(phone -> phone.getValue())
                .map(phoneValue -> phoneValue.split("\\+")[0])
                .filter(pValue -> StringUtil.isNotEmpty(pValue))
                .ifPresent(mobile -> phones.add(mobile));
    }
}
