package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.hr.service.dto.EmpDataQueryDto;
import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.message.sdk.dto.*;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class OnBoardingFeignClientFallback implements OnBoardingFeignClient{

    @Override
    public Result<List<EmpDataVo>> getEmpInfos(EmpDataQueryDto empDataQuery) {
        return Result.fail();
    }

    @Override
    public Result<PreEmpPrivateInfoVo> getPrivateInfoByEmpId(String empId) {
        return Result.fail();
    }

    @Override
    public Result<PreEmpWorkInfoVo> getWorkInfoByEmpId(String empId) {
        return Result.fail();
    }

    @Override
    public Result<List<PreEmpDataVo>> getCandidateDataByEmpIds(List<String> empIds) {
        return Result.fail();
    }

    @Override
    public Result<List<EmpDataVo>> getEmpDetailList(EmpDataQueryDto empDataQuery) {
        return Result.fail();
    }

    @Override
    public Result<PreEmpEntryProcessVo> getEmpProcessInfo(String empId) {
        return Result.fail();
    }

    @Override
    public Result<EmpWelcomeFileAttachmentVo> getEmpWelcomeFileAttachment(String empId) {
        return Result.fail();
    }
}
