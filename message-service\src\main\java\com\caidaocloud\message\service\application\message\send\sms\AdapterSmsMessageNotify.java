package com.caidaocloud.message.service.application.message.send.sms;

import com.caidaocloud.message.service.application.common.enums.MsgType;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import com.caidaocloud.message.service.application.feign.SmsAdapterFeignClient;
import com.caidaocloud.message.service.application.log.dto.MessageLogDto;
import com.caidaocloud.message.service.application.log.repository.IMessageLogRepository;
import com.caidaocloud.message.service.application.sms.dto.SmsMessageDto;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.stream.Collectors;

/**
 * 迪士尼短信通知adapter实现
 *
 * <AUTHOR>
 * @date 2022/6/13
 **/
@Slf4j
@Service
public class AdapterSmsMessageNotify implements SmsMessageNotify<Object> {

    @Resource
    private IMessageLogRepository messageLogRepository;

    @Resource
    private SmsAdapterFeignClient smsAdapterFeignClient;

    @Override
    public void sendMessage(SmsMessageDto messageDto) {
        if (messageDto == null) {
            if (log.isDebugEnabled()) {
                log.debug("parameter is null, when invke sendMessage");
            }
            return;
        }
        String mobiles = messageDto.getMobile().stream().map(e -> e.getMobile()).collect(Collectors.joining(","));
        MessageLogDto messageLogDto = new MessageLogDto();
        messageLogDto.setType(MsgType.MESSAGE);
        messageLogDto.setReceiver(mobiles);
        messageLogDto.setMessage(messageDto.getContent());
        messageLogDto.setStatus(SendStatusEnum.SUCESS);
        String bid = messageLogRepository.saveOrUpdateOfLog(messageLogDto);
        messageLogDto.setBid(bid);

        try {
            Result<String> result = smsAdapterFeignClient.sendMesage(messageDto);
            if (!result.isSuccess()) {
                messageLogDto.setErrorMsg(result.getMsg());
                messageLogRepository.saveOrUpdateOfLog(messageLogDto);
            }
        } catch (Exception e) {
            log.error("sendMessage occur error, {} \n {}", e.getMessage(), e);
            messageLogDto.setStatus(SendStatusEnum.FAIL);
            messageLogDto.setErrorMsg(e.getMessage());
            messageLogRepository.saveOrUpdateOfLog(messageLogDto);
        }
    }


    @Override
    public void sendSingleMessage(SmsMessageDto messageDto) {
        if (messageDto == null) {
            if (log.isDebugEnabled()) {
                log.debug("parameter is null, when invke sendSingleMessage");
            }
            return;
        }
        try {
            Result<String> result = smsAdapterFeignClient.sendMesage(messageDto);
            if (!result.isSuccess()) {
                log.info("sendSingleMessage sendMesage fail,code={},msg={}", result.getCode(), result.getMsg());
            }
        } catch (Exception e) {
            log.error("sendSingleMessage occur error, {} \n {}", e.getMessage(), e);
        }
    }

    @Override
    public String getSignType() {
        return "adapter";
    }

}
