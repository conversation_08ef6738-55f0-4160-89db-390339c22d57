package com.caidaocloud.message.service.application.sms.dto;

import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class SmsMessageDto {
    private List<MobileDto> mobile;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 短信模板code
     */
    private String msgCode;

    /**
     * 短信模板匹配内容
     */
    private List<Map<String, String>> params;

    /**
     * 发送状态
     */
    private SendStatusEnum status = SendStatusEnum.SUCESS;


    public static SmsMessageDto bulid(){
        SmsMessageDto sms = new SmsMessageDto();
        return sms;
    }

    public SmsMessageDto addPhone(String phone, String area){
        if(null == mobile){
            mobile = new ArrayList<>();
        }

        MobileDto mobileDto = new MobileDto();
        mobileDto.setMobile(phone);
        mobileDto.setCountryCode(area);
        mobile.add(mobileDto);
        return this;
    }

    @Data
    public static class MobileDto {
        /**
         * 手机
         */
        private String mobile;

        /**
         * 号码区域code, 空值默认:86
         */
        private String countryCode;

    }
}
