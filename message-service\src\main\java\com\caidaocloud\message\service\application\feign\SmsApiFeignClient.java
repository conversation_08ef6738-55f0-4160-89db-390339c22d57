package com.caidaocloud.message.service.application.feign;

import com.alibaba.fastjson.JSONObject;
import com.caidaocloud.security.config.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * created by: FoAng
 * create time: 29/8/2022 3:13 下午
 */
@FeignClient(
        name = "SmsApiClient",
        fallback = SmsApiFeignClientFallBack.class,
        configuration = FeignConfiguration.class,
        url = "${caidaocloud.msg.sms.api.url:}"
)
public interface SmsApiFeignClient {

    /**
     * 请求获取accessToken
     * @param url
     * @param params
     * @return
     */
    @GetMapping("{url}")
    String getAccessToken(@PathVariable("url") String url, @RequestParam Map<String, Object> params);

    /**
     * 请求发送短信
     * @param url
     * @param object
     * @return
     */
    @PostMapping(value = "{url}")
    String doSendSms(@PathVariable("url") String url, @RequestParam("accessToken") String accessToken, @RequestBody JSONObject object);

}
