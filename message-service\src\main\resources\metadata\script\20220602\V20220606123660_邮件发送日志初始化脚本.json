[{"action": "create", "entityDef": {"identifier": "entity.message.LogEmail", "name": "邮件发送日志", "owner": "message", "label": "emailSmtp", "timelineEnabled": false, "standardProperties": [{"property": "operTime", "name": "操作时间", "dataType": "String", "widgetType": "text"}, {"property": "errorMsg", "name": "错误信息", "dataType": "String", "widgetType": "text", "rules": [{"type": "required", "value": "true"}]}, {"property": "emailTemplate", "name": "邮件模板", "dataType": "String", "widgetType": "text", "rules": [{"type": "required", "value": "true"}, {"type": "max<PERSON><PERSON><PERSON>", "value": "2000"}]}, {"property": "status", "name": "状态", "dataType": "Enum", "widgetType": "enum", "visible": true, "modifiable": false, "defaultValue": {"value": 0, "type": "Enum"}, "enumDef": [{"value": 0, "display": "失败"}, {"value": 1, "display": "成功"}]}]}}]