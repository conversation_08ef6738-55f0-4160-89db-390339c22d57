package com.caidaocloud.message.service.domain.msg.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.message.service.domain.base.entity.BaseDomainDo;
import com.caidaocloud.message.service.domain.base.service.BaseDomainServiceImpl;
import com.caidaocloud.message.service.domain.msg.entity.EmailConfigDo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/6/6 下午6:11
 */
@Service
public class EmailConfigDomainService extends BaseDomainServiceImpl<EmailConfigDo, BasePage> {
    @Resource
    private EmailConfigDo emailConfigDo;

    @Override
    public BaseDomainDo<EmailConfigDo> getDoService() {
        return emailConfigDo;
    }
}
