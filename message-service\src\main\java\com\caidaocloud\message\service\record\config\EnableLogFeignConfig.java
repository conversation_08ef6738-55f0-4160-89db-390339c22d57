package com.caidaocloud.message.service.record.config;

import com.caidaocloud.security.config.FeignConfiguration;
import org.jboss.logging.Logger;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * created by: FoAng
 * create time: 23/5/2024 11:24 上午
 */
@Configuration
public class EnableLogFeignConfig extends FeignConfiguration {

    @Bean
    public Logger.Level feignLogLevel() {
        return Logger.Level.DEBUG;
    }

}
