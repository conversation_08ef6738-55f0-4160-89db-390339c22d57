package com.caidaocloud.ding;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import com.caidaocloud.message.sdk.enums.DingtalkTemplateTypeEnum;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.message.send.sms.DingMessageNotifyApi;
import com.caidaocloud.message.service.application.msg.dto.DingtalkMsgDto;
import com.caidaocloud.message.service.application.msg.dto.MsgDto;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import lombok.SneakyThrows;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class DingMsgTest {
    @Resource
    private DingMessageNotifyApi dingMessageNotifyApi;
    @Test
    @SneakyThrows
    public void testDingMsg(){
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
        OapiMessageCorpconversationAsyncsendV2Request req = new OapiMessageCorpconversationAsyncsendV2Request();
        req.setAgentId(2384096720L);
        req.setUseridList("manager7667");
        OapiMessageCorpconversationAsyncsendV2Request.Msg obj1 = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
        obj1.setMsgtype("text");
        req.setMsg(obj1);
        OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(req, "34ef3d9cac113956b2163654aa98b5cc");


        DingtalkMsgDto message = new DingtalkMsgDto();
        message.setSubject("涛哥测试");
        message.setTemplateType(DingtalkTemplateTypeEnum.LINK_MESSAGE.getIndex());
        message.setUrl("https://www.baidu.com");
        message.setContent("hhhha012j12asd");
        message.setAffix("/caidao-pa/259dtfgdb9cdb5ddab4b-jvm.png");
        message.setAffixName("jvm.png");
        message.setMobiles(Lists.newArrayList());
        dingMessageNotifyApi.sendDingMessage(new MsgDto());

//        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
//        OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
//        request.setUseridList(urid);
//        request.setAgentId(Long.valueOf(agentId));
//        request.setToAllUser(false);

    }

    /**
     * 链接消息
     */
    @Test
    @SneakyThrows
    public void testDingLinkMsg(){

        DingtalkMsgDto message = new DingtalkMsgDto();
        message.setSubject("tanhq测试");
        message.setTemplateType(DingtalkTemplateTypeEnum.LINK_MESSAGE.getIndex());
        StringBuilder url = new StringBuilder("dingtalk://dingtalkclient/page/link?url=")
//                .append(geEncodeUrl("http://www.baidu.com"))
                .append(geEncodeUrl("https://hcm-test.ciwork.cn/h5/approvalDetail?businessKey=1876022068049924_TRANSFER-1855046551410689&nodeType=APPROVAL&taskId=bbf8f971-d6a8-11ee-981a-fa163ea69fe7"))
                .append("&pc_slide=true");

        message.setUrl(url.toString());
        message.setContent("martin--tanhq20240306");
//        message.setAffix("/caidao-pa/259dtfgdb9cdb5ddab4b-jvm.png");
//        message.setAffixName("jvm.png");
//        List<PhoneSimple> mobiles = Lists.newArrayList();
//        PhoneSimple phoneSimple = new PhoneSimple();
//        phoneSimple.setValue("***********");
//        mobiles.add(phoneSimple);
//        message.setMobiles(mobiles);
        message.setEmpIds(Lists.newArrayList("1622233880213510"));

        dingMessageNotifyApi.sendDingMessage(new MsgDto());

    }

    /**
     * 卡片消息
     */
    @Test
    @SneakyThrows
    public void testDingActionCardMsg(){

        DingtalkMsgDto message = new DingtalkMsgDto();
        message.setSubject("tanhq测试卡片消息333");
        message.setTemplateType(DingtalkTemplateTypeEnum.ACTION_CARD_MESSAGE.getIndex());
        StringBuilder url = new StringBuilder("dingtalk://dingtalkclient/page/link?url=")
                .append(geEncodeUrl("http://www.baidu.com"))
                .append("&pc_slide=true");

        System.out.println("url===" + url.toString());
        message.setUrl(url.toString());
        message.setContent("卡片消息内容2222  ");
        StringBuilder markdown = new StringBuilder("# 这是支持markdown的文本  \n");
        markdown.append("## 标题2  \n");
        markdown.append("- item1  \n" +
                "- item2");
        message.setMarkdown(markdown.toString());
        List<PhoneSimple> mobiles = Lists.newArrayList();
        PhoneSimple phoneSimple = new PhoneSimple();
        phoneSimple.setValue("***********");
        mobiles.add(phoneSimple);
        message.setMobiles(mobiles);

        dingMessageNotifyApi.sendDingMessage(new MsgDto());
    }

    private String geEncodeUrl(String url) {
        try {
            return URLEncoder.encode(url, "utf-8");
        } catch (UnsupportedEncodingException e) {
            return null;
        }

    }


    @Before
    public void initUser(){
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("8");
        userInfo.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);

        UserInfo user = new UserInfo();
        user.setTenantId(String.valueOf(8));
        user.doSetUserId(0L);
        UserContext.setCurrentUser(user);
    }
}
