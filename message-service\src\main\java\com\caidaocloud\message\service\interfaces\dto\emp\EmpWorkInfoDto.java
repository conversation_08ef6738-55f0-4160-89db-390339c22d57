package com.caidaocloud.message.service.interfaces.dto.emp;


import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
public class EmpWorkInfoDto {
    /**
     * bid
     */
    private String bid;
    /**
     * 员工ID
     */
    private String empId;
    /**
     * 员工工号
     */
    private String workno;
    /**
     * 员工姓名
     */
    private String name;
    /**
     * 员工英文名
     */
    private String enName;
    /**
     * 入职日期
     */
    private Long hireDate;
    /**
     * 员工状态
     */
    private EnumSimple empStatus;
    /**
     * 员工头像
     */
    private Attachment photo;
    /**
     * 直接上级
     */
    private EmpSimple leadEmpId;
    /**
     * 所属组织Id
     */
    private String organize;
    /**
     * 所属组织名称
     */
    private String organizeTxt;
    /**
     * 职级职等
     */
    private JobGradeRange jobGrade;
    /**
     * 职级职等显示内容
     */
    private String jobGradeTxt;

    /**
     * 关联的职务ID
     */
    private String job;
    /**
     * 关联的职务名称
     */
    private String jobTxt;
    /**
     * 岗位ID
     */
    private String post;
    /**
     * 岗位名称
     */
    private String postTxt;
    /**
     * 试用期期限
     */
    private EnumSimple probation;
    /**
     * 转正日期
     */
    private Long confirmationDate;
    /**
     * 转正状态
     */
    private EnumSimple confirmationStatus;
    /**
     * 员工类型
     */
    private DictSimple empType;
    /**
     * 离职日期
     */
    private Long leaveDate;
    /**
     * 工时制
     */
    private EnumSimple workHour;
    /**
     * 司龄
     */
    private Float divisionAge;
    /**
     * 司龄调整
     */
    private Float divisionAgeAdjust;
    /**
     * 员工公司邮箱
     */
    private String companyEmail;
    /**
     * 工作地ID
     */
    private String workplace;
    /**
     * 工作地名称
     */
    private String workplaceTxt;
    /**
     * 入司途径
     */
    private DictSimple joinCompanyWay;
    /**
     * 成本中心
     */
    //private List<EmpCostCenterDto> costCenters;
    /**
     * 合同公司ID
     */
    private String company;
    /**
     * 合同公司名称
     */
    private String companyTxt;
    /**
     * 时间轴数据变更时间
     */
    private Long dataStartTime;

    /**
     * 预计毕业日期
     */
    private Long expectGraduateDate;

    /**
     * 扩展字段
     */
    private Map ext = new LinkedHashMap();
}