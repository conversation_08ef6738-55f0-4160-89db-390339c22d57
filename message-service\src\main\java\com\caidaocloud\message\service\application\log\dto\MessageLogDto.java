package com.caidaocloud.message.service.application.log.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.caidaocloud.message.service.application.common.enums.MsgType;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import lombok.Data;

/**
 * 日志dto
 *
 * <AUTHOR>
 * @date 2022/6/9
 **/
@Data
public class MessageLogDto {

    private String bid;

    /**
     * 发送类型
     */
    private MsgType type;

    /**
     * 接受者
     */
    private String receiver;

    /**
     * 消息内容
     */
    private String message;

    /**
     * 发送状态
     */
    @JSONField(serialize = false)
    private SendStatusEnum status;

    /**
     * 错误日志信息
     */
    private String errorMsg;

    /**
     * 发送运营商渠道
     */
    private String channel;

}
