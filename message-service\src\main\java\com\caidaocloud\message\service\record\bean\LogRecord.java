package com.caidaocloud.message.service.record.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * created by: FoAng
 * create time: 22/5/2024 3:33 下午
 */
@Data
@TableName("log_record")
public class LogRecord implements Serializable {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("操作人")
    private Long operator;

    @ApiModelProperty("操作人账号")
    private String operatorAccount;

    @ApiModelProperty("操作人手机号")
    private String operatorMobile;

    @ApiModelProperty("操作人邮箱")
    private String operatorEmail;

    @ApiModelProperty("操作人姓名")
    private String operatorName;

    @ApiModelProperty("员工ID")
    private Long empId;

    @ApiModelProperty("操作来源")
    private String operatorSource;

    @ApiModelProperty("操作平台")
    private String operatorPlatform;

    @ApiModelProperty("服务名称")
    private String serviceName;

    @ApiModelProperty("请求uri")
    private String uri;

    @ApiModelProperty("请求菜单")
    private String menu;

    @ApiModelProperty("操作类型")
    private String category;

    @ApiModelProperty("操作摘要")
    private String action;

    @ApiModelProperty("操作详细内容")
    private String detail;

    @ApiModelProperty("操作状态")
    private Boolean status;

    @ApiModelProperty("操作时间")
    private Date createTime;

    @ApiModelProperty("是否删除")
    private Integer isDeleted;

}
