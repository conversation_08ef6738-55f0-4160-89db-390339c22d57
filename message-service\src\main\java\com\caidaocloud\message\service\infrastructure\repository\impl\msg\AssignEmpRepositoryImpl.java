package com.caidaocloud.message.service.infrastructure.repository.impl.msg;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.message.service.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.message.service.domain.msg.entity.AssignEmpDo;
import com.caidaocloud.message.service.domain.msg.repository.AssignEmpRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * AssignEmpRepositoryImpl
 *
 * <AUTHOR>
 * @date 2022/6/6 下午6:04
 */
@Repository
public class AssignEmpRepositoryImpl extends BaseRepositoryImpl<AssignEmpDo> implements AssignEmpRepository {
    @Override
    public void deleteByConfigId(String identifier, String config) {
        DataDelete.identifier(identifier).batchDelete(DataFilter.eq("msgConfig", config));
    }

    @Override
    public void batchInsert(String identifier, List<AssignEmpDo> dataList) {
        DataInsert.identifier(identifier).batchInsert(dataList);
    }

    @Override
    public List<AssignEmpDo> selectList(AssignEmpDo data) {
        DataFilter dataFilter = getBaseFilter();
        if (data.getMsgConfig() != null) {
            dataFilter = dataFilter.andEq("msgConfig", data.getMsgConfig());
        }

        return DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1)
                .filter(dataFilter, AssignEmpDo.class).getItems();
    }
}
