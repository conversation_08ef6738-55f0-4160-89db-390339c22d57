package com.caidaocloud.message.service.application.app.service;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;

public interface NotifyService {

    ConcurrentHashMap<String, NotifyService> notifyManager = new ConcurrentHashMap();

    @PostConstruct
    default void register() {
        String type = getMsgType();
        notifyManager.put(type, this);
    }

    String getMsgType();
}
