package com.caidaocloud.record.core.support;

import com.caidaocloud.record.core.beans.LogRecordData;
import com.caidaocloud.record.core.beans.LogRecordOps;
import com.caidaocloud.record.core.beans.MethodExecuteResult;
import com.caidaocloud.record.core.beans.Operator;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.record.core.service.IDispatchRecordService;
import com.caidaocloud.record.core.service.IOperatorGetService;
import com.caidaocloud.record.core.service.IUrlResourceService;
import com.caidaocloud.record.core.support.parse.LogRecordValueParser;
import com.caidaocloud.record.core.util.WebUtil;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Slf4j
public class LogRecordInterceptor extends LogRecordValueParser implements InitializingBean, MethodInterceptor, Serializable {

    private LogRecordOperationSource logRecordOperationSource;

    private IDispatchRecordService dispatchRecordService;

    private IOperatorGetService operatorGetService;

    private IUrlResourceService resourceService;

    private String serviceName;

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        Method method = invocation.getMethod();
        HttpServletRequest request = getHttpServletRequest();
        return execute(invocation, invocation.getThis(), method, invocation.getArguments(), request);
    }

    private HttpServletRequest getHttpServletRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            return attributes.getRequest();
        }
        return null;
    }

    private void registerRecordUrl(Collection<LogRecordOps> operations) {
        for (LogRecordOps operation : operations) {
            String uriStr = operation.getUri();
            resourceService.registerAnnotationUrl(uriStr);
        }
    }

    private Object execute(MethodInvocation invoker, Object target, Method method, Object[] args, HttpServletRequest request) throws Throwable {
        Class<?> targetClass = getTargetClass(target);
        Object ret = null;
        MethodExecuteResult methodExecuteResult = new MethodExecuteResult(true, null, "");
        LogRecordContext.putEmptySpan();
        Collection<LogRecordOps> operations = new ArrayList<>();
        Map<String, String> functionNameAndReturnMap = new HashMap<>();
        try {
            operations = logRecordOperationSource.computeLogRecordOperations(method, targetClass);
            registerRecordUrl(operations);
            List<String> spElTemplates = getBeforeExecuteFunctionTemplate(operations);
            functionNameAndReturnMap = processBeforeExecuteFunctionTemplate(spElTemplates, targetClass, method, args);
        } catch (Exception e) {
            log.error("log record parse before function exception", e);
        }
        try {
            ret = invoker.proceed();
        } catch (Exception e) {
            methodExecuteResult = new MethodExecuteResult(false, e, e.getMessage());
        }
        try {
            final String operatorPlatform = WebUtil.getOperatorPlatform(request);
            final String requestUri = request == null ? null : request.getRequestURI();
            if (!CollectionUtils.isEmpty(operations)) {
                recordExecute(ret, method, args, operations, targetClass,
                        methodExecuteResult.isSuccess(), methodExecuteResult.getErrorMsg(),
                        functionNameAndReturnMap, operatorPlatform, requestUri);
            }
        } catch (Exception t) {
            log.error("log record parse exception, msg:{}", t.getMessage(), t);
        } finally {
            LogRecordContext.clear();
        }
        if (methodExecuteResult.getThrowable() != null) {
            throw methodExecuteResult.getThrowable();
        }
        return ret;
    }

    private List<String> getBeforeExecuteFunctionTemplate(Collection<LogRecordOps> operations) {
        List<String> spElTemplates = new ArrayList<>();
        for (LogRecordOps operation : operations) {
            //执行之前的函数，失败模版不解析
            List<String> templates = getSpElTemplates(operation, operation.getSuccessLogTemplate());
            if (!CollectionUtils.isEmpty(templates)) {
                spElTemplates.addAll(templates);
            }
        }
        return spElTemplates;
    }


    private void recordExecute(Object ret, Method method, Object[] args, Collection<LogRecordOps> operations,
                               Class<?> targetClass, boolean success, String errorMsg, Map<String, String> functionNameAndReturnMap,
                               String platform, String requestUri) {
        for (LogRecordOps operation : operations) {
            try {
                String action = getActionContent(success, operation);
                if (StringUtils.isEmpty(action)) {
                    log.info("[record] invalid record due to action is empty");
                    continue;
                }
                //获取需要解析的表达式
                List<String> spElTemplates = getSpElTemplates(operation, action);
                if (!success) {
                    spElTemplates.remove(operation.getOperatorStatus());
                }
                Operator operator = getOperatorIdFromServiceAndPutTemplate(operation, spElTemplates);
                Map<String, String> expressionValues = processTemplate(spElTemplates, ret, targetClass, method, args, errorMsg, functionNameAndReturnMap);
                if (logConditionPassed(operation.getCondition(), expressionValues)) {
                    Operator realOperator = getRealOperator(operation, operator, expressionValues);
                    LogRecordData logRecord = LogRecordData.builder()
                            .tenantId(realOperator.getTenantId())
                            .operator(realOperator.getOperatorId())
                            .serviceName(serviceName)
                            .operatorPlatform(platform)
                            .operatorSource(operation.getSource())
                            .uri(Optional.ofNullable(expressionValues.get(operation.getUri())).orElse(requestUri))
                            .status(getOperatorStatusValue(success, operation.getOperatorStatus(), expressionValues))
                            .category(expressionValues.get(operation.getCategory()))
                            .detail(expressionValues.get(operation.getDetail()))
                            .action(expressionValues.get(action))
                            .menu(expressionValues.get(operation.getMenu()))
                            .createTime(new Date())
                            .build();
                    if (StringUtils.isEmpty(logRecord.getAction())) {
                        continue;
                    }
                    if (invalidRecord(logRecord)) {
                        log.info("[record] invalid record due to missing parameter, eg: action、category、menu、uri");
                        continue;
                    }
                    Preconditions.checkNotNull(dispatchRecordService, "dispatchService not init!!");
                    dispatchRecordService.dispatchLogRecord(logRecord);
                }
            } catch (Exception t) {
                log.error("[record] execute exception", t);
            }
        }
    }

    public boolean invalidRecord(LogRecordData recordData) {
        return StringUtils.isEmpty(recordData.getUri()) && (StringUtils.isEmpty(recordData.getMenu())
                || StringUtils.isEmpty(recordData.getCategory()));
    }

    private Boolean getOperatorStatusValue(boolean status, String operatorStatus, Map<String, String> expressionValues) {
        if (status) {
            String operatorStatusValue = expressionValues.get(operatorStatus);
            if (StringUtils.endsWithIgnoreCase(operatorStatusValue, "true")) {
                return Boolean.TRUE;
            } else if (StringUtils.endsWithIgnoreCase(operatorStatusValue, "false")) {
                return Boolean.FALSE;
            } else {
                try {
                    return Boolean.TRUE;
                } catch (Exception e) {
                    log.error("resolve status error, msg: {}", e.getMessage(), e);
                }
            }
        }
        return Boolean.FALSE;
    }

    private List<String> getSpElTemplates(LogRecordOps operation, String action) {
        return Stream.of(operation.getBizNo(), action, operation.getDetail(), operation.getCondition(), operation.getCategory(), operation.getMenu(),
                operation.getOperatorStatus(), operation.getTenantId()).collect(Collectors.toList());
    }

    private boolean logConditionPassed(String condition, Map<String, String> expressionValues) {
        return StringUtils.isEmpty(condition) || StringUtils.endsWithIgnoreCase(expressionValues.get(condition), "true");
    }

    private Operator getRealOperator(LogRecordOps operation, Operator operator, Map<String, String> expressionValues) {
        return Optional.ofNullable(operator).orElseGet(() -> {
            String operatorValue = expressionValues.get(operation.getOperatorId());
            return operatorGetService.getUser(operatorValue == null ? 0L : Long.parseLong(operatorValue), expressionValues.get(operation.getTenantId()));
        });
    }

    private Operator getOperatorIdFromServiceAndPutTemplate(LogRecordOps operation, List<String> spElTemplates) {
        if (StringUtils.isEmpty(operation.getOperatorId())) {
            Operator operator = operatorGetService.getUser();
            if (operator == null) {
                throw new IllegalArgumentException("[LogRecord] operator is null");
            }
            return operator;
        } else {
            spElTemplates.add(operation.getOperatorId());
        }
        return null;
    }

    private String getActionContent(boolean success, LogRecordOps operation) {
        String failTemplate = StringUtils.isEmpty(operation.getFailLogTemplate()) ? operation.getSuccessLogTemplate()
                : operation.getFailLogTemplate();
        if (success) {
            return operation.getSuccessLogTemplate();
        } else {
            return failTemplate;
        }
    }

    private Class<?> getTargetClass(Object target) {
        return AopProxyUtils.ultimateTargetClass(target);
    }

    public void setLogRecordOperationSource(LogRecordOperationSource logRecordOperationSource) {
        this.logRecordOperationSource = logRecordOperationSource;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        /*bizLogService = beanFactory.getBean(ILogRecordService.class);
        operatorGetService = beanFactory.getBean(IOperatorGetService.class);
        Preconditions.checkNotNull(bizLogService, "bizLogService not null");*/
    }

    public void setOperatorGetService(IOperatorGetService operatorGetService) {
        this.operatorGetService = operatorGetService;
    }

    public void setDispatchLogService(IDispatchRecordService bizLogService) {
        this.dispatchRecordService = bizLogService;
    }

    public void setResourceService(IUrlResourceService resourceService) {
        this.resourceService = resourceService;
    }
}
