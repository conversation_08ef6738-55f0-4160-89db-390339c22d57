package com.caidaocloud.message.service.application.message.notifier.service;

import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDataDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.message.service.application.message.notifier.input.NotifierDataFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 查询 Recruiters
 */
@Service
public class NotifyRecruiterService implements INotifyTargetService{

    @Override
    public String getNotifyTarget() {
        return NoticeTarget.RECRUITERS.toString();
    }

    @Override
    public void getNotifier(List<NotifierDto> notifier, NotifyObjectDto obj) {
        List<EmpDataVo> recruiters = null;
        if(1 == obj.getType()){
            // 候选人对应的组织有这个角色
            recruiters = NotifierDataFactory.getNotifierService()
                    .getCandidateRecruiter(NotifierDataDto.bulid(obj));
        } else {
            recruiters = NotifierDataFactory.getNotifierService()
                    .getRecruiterByEmpIds(obj.getEmpIds(), obj.getDataTime());
        }

        if(null == recruiters || recruiters.isEmpty()){
            return;
        }

        notifier.addAll(convertEmpDataList(recruiters));
    }
}
