import com.caidaocloud.record.core.configure.LogRecordProxyAutoConfiguration;
import org.junit.Test;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

/**
 * created by: FoAng
 * create time: 22/5/2024 10:42 上午
 */
public class AnnotationTest {

    @Test
    public void testAnnotationInject() {
        AnnotationConfigApplicationContext context = new AnnotationConfigApplicationContext();
        context.register(LogRecordProxyAutoConfiguration.class);
        context.refresh();
    }
}
