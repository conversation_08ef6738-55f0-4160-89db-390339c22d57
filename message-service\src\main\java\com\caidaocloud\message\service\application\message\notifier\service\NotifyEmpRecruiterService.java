package com.caidaocloud.message.service.application.message.notifier.service;

import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.message.service.application.message.notifier.input.NotifierDataFactory;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 查询 Recruiters
 */
@Service
public class NotifyEmpRecruiterService implements INotifyTargetService{

    @Override
    public String getNotifyTarget() {
        return NotifyTargetFactory.ENTITY_PAAS_TARGET;
    }

    @Override
    public void getNotifier(List<NotifierDto> notifier, NotifyObjectDto obj) {
        List<EmpDataVo> recruiters = null;
        List<String> eventEmpIds = Lists.newArrayList(obj.getEventSubject());
        if(1 == obj.getType()){
            // 候选人暂时不支持
        } else {
            recruiters = NotifierDataFactory.getNotifierService()
                    .getEmpDataByEmpIds(eventEmpIds, obj.getDataTime());
            List<String> recruitmentEmpIds = getRecruitmentEmpIds(recruiters);
            if(null == recruitmentEmpIds || recruitmentEmpIds.isEmpty()){
                return;
            }
            recruiters = NotifierDataFactory.getNotifierService()
                    .getEmpDataByEmpIds(recruitmentEmpIds, obj.getDataTime());
        }

        if(null == recruiters || recruiters.isEmpty()){
            return;
        }

        notifier.addAll(convertEmpDataList(recruiters));
    }

    private List<String> getRecruitmentEmpIds(List<EmpDataVo> list){
        if(null == list || list.isEmpty()){
            return null;
        }

        List<String> collect = list.stream().filter(rec -> {
            EmpWorkInfoVo empWorkInfo = rec.getEmpWorkInfo();
            if(null == empWorkInfo){
                return false;
            }
            EmpSimple recruitment = empWorkInfo.getRecruitment();
            return null != recruitment && StringUtil.isNotEmpty(recruitment.getEmpId());
        }).map(emp -> emp.getEmpWorkInfo().getRecruitment().getEmpId()).collect(Collectors.toList());
        return collect;
    }
}
