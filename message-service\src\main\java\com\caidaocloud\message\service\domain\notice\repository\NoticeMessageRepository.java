package com.caidaocloud.message.service.domain.notice.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.service.application.notice.dto.NoticeMsgDto;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;
import com.caidaocloud.message.service.domain.notice.entity.NoticeMessageDo;
import com.caidaocloud.message.service.infrastructure.repository.po.NoticeMessagePo;

import java.util.List;
import java.util.Map;

/**
 * created by: FoAng
 * create time: 15/8/2024 10:33 上午
 */
public interface NoticeMessageRepository extends BaseRepository<NoticeMessagePo> {

    /**
     * 获取未读消息汇总
     * @return
     */
    Map<String, Integer> getTypeUnReadCount(String identifier, String receiver);

    Map<String, Integer> getBusinessUnReadCount(String identifier, String receiver);

    PageResult<NoticeMessagePo> listByBusinessId(String identifier, String businessType, String businessId);

    PageResult<NoticeMessagePo> pageByExtData(String identifier, String receiver, NoticeMsgDto dto);

    void markReadAction(String identifier, List<String> bids, String businessType, boolean markRead);

    void markAllReadAction(String identifier, String receiver, String businessType, boolean markRead);

    void persistEsData(List<NoticeMessagePo> noticeMessagePos);

    void truncateEsIndex();

    void refreshEsData();

}
