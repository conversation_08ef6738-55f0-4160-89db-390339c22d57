package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.message.service.application.sms.dto.SmsMessageDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "SmsHttpApiClient",
        url = "${caidaocloud.msg.sms.url:}",
        fallback = SmsAdapterFeignClientFallBack.class,
        configuration = FeignConfiguration.class)
public interface SmsAdapterFeignClient {
    /**
     * 发送短信
     */
    @PostMapping("/api/adapterDisney/msg/v1/send")
    Result<String> sendMesage(@RequestBody SmsMessageDto messageDto);

}
