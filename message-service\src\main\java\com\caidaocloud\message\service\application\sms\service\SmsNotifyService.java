package com.caidaocloud.message.service.application.sms.service;

import com.caidaocloud.message.service.application.app.service.NotifyService;

import java.util.List;

public interface SmsNotifyService extends NotifyService {

    default void send(SmsMsgSender sender){
        sendMsg(sender);
    }

    boolean sendMsg(SmsMsgSender sender);

    default void batchSend(List<SmsMsgSender> senderList){
        batchSendMsg(senderList);
    }

    boolean batchSendMsg(List<SmsMsgSender> senderList);
}
