package com.caidaocloud.message.service.domain.base.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.service.domain.base.entity.BaseDomainDo;

import java.util.List;

public interface BaseDomainService <T, D extends BasePage> {
    BaseDomainDo getDoService();
    /**
     * 查询详情
     */
    T getDetail(T data);

    /**
     * 查询一个
     */
    T getOne(T data);

    /**
     * 查询所有
     */
    List<T> getAll(T data);

    /**
     * 根据bid查询单个详情
     */
    T getById(String bid);

    /**
     * 分页查询
     */
    PageResult<T> getPage(D query);

    /**
     * 根据BID物理删除
     */
    void delete(String bid);

    /**
     * 根据BID逻辑删除
     */
    void softDelete(String bid);

    /**
     * 新增
     */
    T save(T data);

    void batchSave(List<T> data);

    /**
     * 保存或更新
     */
    T saveOrUpdate(T data);

    /**
     * 更新
     */
    T updateById(T data);

    /**
     * 下拉列表
     */
    List<T> selectList();

    List<T> selectByIds(List<String> ids);

    int deleteBatchIds(List<String> ids);
}
