package com.caidaocloud.message.service.application.msg.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.message.service.application.common.service.BaseServiceImpl;
import com.caidaocloud.message.service.domain.base.service.BaseDomainService;
import com.caidaocloud.message.service.domain.msg.entity.EmpMsgTemplateRelDo;
import com.caidaocloud.message.service.domain.msg.service.EmpMsgTemplateRelDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class EmpMsgTemplateRelService extends BaseServiceImpl<EmpMsgTemplateRelDo, BasePage> {
    @Resource
    private EmpMsgTemplateRelDomainService domainService;

    @Override
    protected BaseDomainService getDomainService() {
        return domainService;
    }

    public void deleteTemplateId(String bid) {
        domainService.deleteTemplateId(bid);
    }

    public List<String> getEmpList(String msgConfig, BasePage page) {
        return domainService.getEmpList(msgConfig, page);
    }

    public List<String> getEmpListByEmpIds(String msgConfig, BasePage page, List<String> empIdList) {
        return domainService.getEmpListByEmpIds(msgConfig, page, empIdList);
    }

    public List<EmpMsgTemplateRelDo> getByEmpIds(List<String> empList) {
        return domainService.getByEmpIds(empList);
    }
}
