package com.caidaocloud.message.service.application.msg.event;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.service.application.email.dto.MailMsgSender;
import com.caidaocloud.message.service.application.email.service.SmtpEmailNotify;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.mq.rabbitmq.consumer.AbsMQConsumer;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class EmailLimiterConsumer extends AbsMQConsumer {
    @Resource
    private SmtpEmailNotify smtpEmailNotify;
    /**
     * 邮箱频率限制延迟消息处理
     */
    @Override
    public void process(String message) {
        log.info("limiter email message={}", message);
        try {
            MailMsgSender sender = FastjsonUtil.toObject(message, MailMsgSender.class);
            String tenantId = sender.getTenantId();
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(tenantId);
            userInfo.setEmpId(0L);
            userInfo.setUserId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            UserInfo user = new UserInfo();
            user.setTenantId(tenantId);
            user.setStaffId(0L);
            user.doSetUserId(0L);
            UserContext.setCurrentUser(user);
            smtpEmailNotify.sendMsg(sender);
        } catch (Exception e){
            log.error("limiter email message err,{}", e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }
}
