package com.caidaocloud.message.service.domain.notice.entity;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.service.application.notice.dto.NoticeMsgDto;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;
import com.caidaocloud.message.service.domain.notice.repository.NoticeMessageRepository;
import com.caidaocloud.message.service.infrastructure.repository.po.NoticeMessagePo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * created by: FoAng
 * create time: 15/8/2024 10:20 上午
 */
@Slf4j
@Data
@Component
@EqualsAndHashCode(callSuper = true)
public class NoticeMessageDo extends NoticeMessagePo {

    @Resource
    private NoticeMessageRepository messageRepository;

    @Resource
    private AsyncTaskExecutor asyncTaskExecutor;

    @Override
    public String getDoIdentifier() {
        return "entity.message.NoticeMessage";
    }

    @Override
    public BaseRepository<NoticeMessagePo> getRepository() {
        return messageRepository;
    }

    public Map<String, Integer> getTypeUnRead(String empId) {
        return messageRepository.getTypeUnReadCount(getDoIdentifier(), empId);
    }

    public Map<String, Integer> getBusinessUnRead(String empId) {
        return messageRepository.getBusinessUnReadCount(getDoIdentifier(), empId);
    }

    public PageResult<NoticeMessagePo> listByBusinessId(String businessType, String businessId) {
        return messageRepository.listByBusinessId(getDoIdentifier(), businessType, businessId);
    }

    public PageResult<NoticeMessagePo> pageByExtData(String receiver, NoticeMsgDto dto) {
        return messageRepository.pageByExtData(getDoIdentifier(), receiver, dto);
    }

    public void markReadAction(List<String> bids, String businessType, boolean markRead) {
        messageRepository.markReadAction(getDoIdentifier(), bids, businessType, markRead);
    }

    public void persistAllEsData() {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        asyncTaskExecutor.execute(() -> {
            try {
                SecurityUserUtil.setSecurityUserInfo(userInfo);
                messageRepository.truncateEsIndex();
                NoticeMsgDto msgDto = new NoticeMsgDto();
                msgDto.setPageNo(1);
                msgDto.setPageSize(20);
                PageResult<NoticeMessagePo> pageResult;
                do {
                    pageResult = messageRepository.pageByExtData(getDoIdentifier(), null, msgDto);
                    msgDto.setPageNo(msgDto.getPageNo() + 1);
                    if (!CollectionUtils.isEmpty(pageResult.getItems())) {
                        messageRepository.persistEsData(pageResult.getItems());
                    }
                } while (!CollectionUtils.isEmpty(pageResult.getItems()));
            } catch (Exception e) {
                log.error("[notice] persist es data error, {}", e.getMessage(), e);
            } finally {
                SecurityUserUtil.removeSecurityUserInfo();
            }
        });
    }

}
