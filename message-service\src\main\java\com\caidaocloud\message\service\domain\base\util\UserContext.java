package com.caidaocloud.message.service.domain.base.util;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.security.dto.RestUserInfo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.RestUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2021/11/24
 */
@Slf4j
public final class UserContext {
    private final static ThreadLocal<UserInfo> USER_CONTEXT = new ThreadLocal<>();

    public static void setCurrentUser(UserInfo user) {
        USER_CONTEXT.remove();
        if (user != null) {
            USER_CONTEXT.set(user);
        }
    }

    /**
     * 获取用户信息
     *
     * @return
     */
    public static UserInfo getCurrentUser() {
        return USER_CONTEXT.get();
    }

    public static UserInfo getContextUser() {
        UserInfo user = USER_CONTEXT.get();
        user = null == user ? getSecurityUser() : user;
        user = null == user ? SpringUtil.getBean(ISessionService.class).getUserInfo() : user;
        return user;
    }

    private static UserInfo getSecurityUser() {
        SecurityUserInfo ui = SecurityUserUtil.getSecurityUserInfo();
        if (null == ui) {
            return null;
        }
        UserInfo userInfo = new UserInfo();
        userInfo.setTenantId(ui.getTenantId());
//        if (null != ui.getUserId()) {
//            userInfo.setUserid(ui.getUserId().intValue());
//        }
        userInfo.doSetUserId(ui.getUserId());
        userInfo.setStaffId(ui.getEmpId());
        return userInfo;
    }

    /**
     * 清除用户信息
     */
    public static void remove() {
        USER_CONTEXT.remove();
    }

    public static boolean checkUser(UserInfo userInfo) {
        return null == userInfo || null == userInfo.getUserId();
    }

    public static void preCheckUser(UserInfo userInfo) {
        PreCheck.preCheckArgument(checkUser(userInfo), "登录失效");
    }

    public static UserInfo preCheckUser() {
        UserInfo userInfo = getContextUser();
        if (userInfo == null) {
            Optional<RestUserInfo> requestUser = RestUtil.getRequestUser();
            if (requestUser.isPresent()) {
                userInfo = new UserInfo();
                Long uid = ((RestUserInfo) requestUser.get()).getUserId();
                Long empId = ((RestUserInfo) requestUser.get()).getEmpId();
                Long tenantId = ((RestUserInfo) requestUser.get()).getTenantId();
                userInfo.setUserid(uid.intValue());
                userInfo.setCorpid(null);
                userInfo.setEmpid(empId.intValue());
                userInfo.setTenantId(null == tenantId ? null : String.valueOf(tenantId));
                userInfo.setCreateBy(tenantId);
            }
        }
        PreCheck.preCheckArgument(checkUser(userInfo), "登录失效");
        return userInfo;
    }

    public static String getUserId() {
        UserInfo userInfo = UserContext.preCheckUser();
        return null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
    }

    public static String getTenantId() {
        UserInfo userInfo = UserContext.preCheckUser();
        return null == userInfo || null == userInfo.getTenantId() ? null : userInfo.getTenantId();
    }

    public static void doInitSecurityUserInfo(String tenantId, String userId, String staffId,
                                              String empName, String userName, String mobnum) {
        Long empId = null == staffId || "null".equals(staffId) ? 0L : Long.valueOf(staffId);
        Long userid = null == userId || "null".equals(userId) ? 0L : Long.valueOf(userId);
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId(tenantId);
        userInfo.setEmpId(empId);
        userInfo.setUserId(userid);// 默认系统超级管理员
        SecurityUserUtil.setSecurityUserInfo(userInfo);

        UserInfo user = new UserInfo();
        user.setTenantId(tenantId);
        // 默认系统超级管理员
        user.setStaffId(empId);
        user.doSetUserId(userid);
        user.setUserid(userid.intValue());
        user.setEmpid(empId.intValue());
        user.setEmpname(empName);
        user.setUserName(userName);
        user.setMobnum(mobnum);
        UserContext.setCurrentUser(user);
    }

    public static void removeSecurityUserInfo() {
        SecurityUserUtil.removeSecurityUserInfo();
        UserContext.remove();
    }
}
