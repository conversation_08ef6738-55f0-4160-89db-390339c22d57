package com.caidaocloud.message.service.application.message.notifier.service;

import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.message.service.application.feign.HrFeignClient;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NotifyWorkflowApproverService implements INotifyTargetService {

    @Resource
    private HrFeignClient hrFeignClient;

    @Override
    public String getNotifyTarget() {
        return NotifyTargetFactory.WORKFLOW_APPROVER_TARGET;
    }

    @Override
    public void getNotifier(List<NotifierDto> notifier, NotifyObjectDto obj) {
        if(StringUtils.isEmpty(obj.getEventSubject())){
            return;
        }

        String settingBids = obj.getEmpIds().stream().collect(Collectors.joining(","));
        Result<String> empInfos = hrFeignClient.getApprovers(obj.getEventSubject(), settingBids, obj.getType() == 1);
        val empIdList= FastjsonUtil.toList(empInfos.getData(), Map.class)
                .stream().map(it->it.get("empId").toString()).collect(Collectors.toList());
        if(empIdList.isEmpty()){
            return;
        }
        List<Map<String, String>> phoneList = DataQuery.identifier("entity.hr.EmpPrivateInfo")
                .filterProperties(DataFilter.in("empId", empIdList),
                        Lists.list("empId", "phone"), System.currentTimeMillis())
                .getItems();
        phoneList.removeIf(map -> {
            if (map.get("phone") == null) {
                log.warn("phone is empty,empId={}", map.get("empId"));
                return true;
            }
            return false;
        });
        val phoneMap = phoneList.stream()
                .collect(Collectors.toMap(it->it.get("empId"), it->it.get("phone")));
        var result = hrFeignClient.getEmpListByEmpIds(empIdList);
        if (result.isSuccess() && !CollectionUtils.isEmpty(result.getData())) {

            List<NotifierDto> collect = result.getData().stream().map(emp->{
                NotifierDto notifierDto = new NotifierDto();
                String phone = phoneMap.get(emp.getEmpId());
                PhoneSimple phoneSimple = new PhoneSimple();
                phoneSimple.setValue(phone);
                PhoneSimple.doValue(phoneSimple);
                notifierDto.setPhone(phoneSimple);
                notifierDto.setEmail(emp.getCompanyEmail());
                notifierDto.setEmpId(emp.getEmpId());
                return notifierDto;
            }).collect(Collectors.toList());
            notifier.addAll(collect);
        }
    }
}
