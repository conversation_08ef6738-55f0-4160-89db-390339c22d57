package com.caidaocloud.message.service.application.message.notifier.service;

import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 查询全部员工
 */
@Service
public class NotifyAllEmpService implements INotifyTargetService{

    @Override
    public String getNotifyTarget() {
        return NoticeTarget.ALL_EMP.toString();
    }

    @Override
    public void getNotifier(List<NotifierDto> notifier, NotifyObjectDto obj) {

    }
}
