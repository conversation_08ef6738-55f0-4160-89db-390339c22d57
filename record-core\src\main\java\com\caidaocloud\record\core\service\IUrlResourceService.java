package com.caidaocloud.record.core.service;

import com.caidaocloud.record.core.annotation.LogRecordUrl;
import com.caidaocloud.record.core.beans.AuthResourceUrl;
import com.caidaocloud.record.core.configure.LogRecordProperties;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.RequestHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.reflections.Reflections;
import org.reflections.scanners.MethodAnnotationsScanner;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * logRecordUrl获取的url地址
 * 排除logRecord
 * created by: FoAng
 * create time: 29/5/2024 10:14 上午
 */
@Slf4j
public abstract class IUrlResourceService {

    protected ConcurrentMap<String, String> classUrlMaps = new ConcurrentHashMap<>();

    protected Set<String> annotationUrls = new HashSet<>();

    protected Set<String> registerUrls = new HashSet<>();

    public void registerAnnotationUrl(String recordAnnotationUrl) {
        HttpServletRequest request = RequestHelper.getRequest();
        if (StringUtils.isNotEmpty(recordAnnotationUrl)) {
            annotationUrls.add(recordAnnotationUrl);
        } else if (request != null) {
            annotationUrls.add(request.getRequestURI());
        }
    }

    /**
     * 需要过滤url
     * @param packageName
     */
    public void registerFilterUrl(String packageName, LogRecordProperties properties) {
        log.info("[record] auto register filter urls by scan class packageName");
        boolean matchAll = properties.isMatchAll();
        if (!matchAll) {
            Reflections reflections = new Reflections(new ConfigurationBuilder()
                    .setUrls(ClasspathHelper.forPackage(packageName))
                    .setScanners(new MethodAnnotationsScanner()));
            Set<Method> annotatedMethods = reflections.getMethodsAnnotatedWith(LogRecordUrl.class);
            for (Method method : annotatedMethods) {
                String className = method.getClass().getName();
                String requestUrl = Optional.ofNullable(classUrlMaps.get(className)).orElse(fetchBaseUrl(method));
                if (StringUtils.isNotEmpty(requestUrl)) {
                    String methodUri = getMethodUri(method);
                    if (StringUtils.isNotEmpty(methodUri)) {
                        registerUrls.add(String.format("%s%s", requestUrl, methodUri));
                        continue;
                    }
                    registerUrls.add(requestUrl);
                }
            }
        }
        log.info("[record] scan package result:{}", FastjsonUtil.toJson(registerUrls));
    }

    private String getMethodUri(Method method) {
        RequestMapping requestAnnotation = AnnotatedElementUtils.findMergedAnnotation(method, RequestMapping.class);
        if (requestAnnotation != null) {
            String[] methodPaths = requestAnnotation.value();
            if (methodPaths.length > 0) {
                return methodPaths[0];
            }
        }
        return null;
    }

    @SafeVarargs
    private final String getMethodUri(Method method, Class<? extends Annotation>... requestTypes) {
        for (Class<? extends Annotation> requestType : requestTypes) {
            Annotation methodAnnotation = AnnotatedElementUtils.findMergedAnnotation(method,
                    requestType);
            if (methodAnnotation != null) {
                Annotation[] metaAnnotations = methodAnnotation.annotationType().getAnnotations();
                for (Annotation metaAnnotation : metaAnnotations) {
                    if (metaAnnotation instanceof RequestMapping) {
                        RequestMapping meta = (RequestMapping) metaAnnotation;
                        String[] methodPaths = meta.value();
                        if (methodPaths.length > 0) {
                            return methodPaths[0];
                        }
                    }
                }
            }
        }
        return null;
    }

    private String fetchBaseUrl(Method method) {
        RequestMapping classRequestMapping = AnnotatedElementUtils.findMergedAnnotation(method.getDeclaringClass(),
                RequestMapping.class);
        if (classRequestMapping != null) {
            String[] urls = classRequestMapping.value();
            if (urls.length > 0) {
                classUrlMaps.put(method.getClass().getName(), urls[0]);
                return urls[0];
            }
        }
        return null;
    }


    public List<String> getAllExcludeUrls() {
        return Optional.ofNullable(excludeFilterUrls())
                .map(it -> {
                    List<String> urls = new ArrayList<>(annotationUrls);
                    urls.addAll(excludeFilterUrls());
                    return urls;
                }).orElseGet(() -> Lists.newArrayList(annotationUrls.iterator()));
    }

    public List<String> getRegisterUrls() {
        return new ArrayList<>(registerUrls);
    }


    /**
     * 排除过滤url
     * @return
     */
    public abstract List<String> excludeFilterUrls();

    /**
     * 自定义资源
     * @return
     */
    public abstract List<AuthResourceUrl> urlResources();

}
