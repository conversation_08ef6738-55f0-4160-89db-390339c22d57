package com.caidaocloud.message.service.application.message.send;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.message.sdk.dto.MsgNoticeDto;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.event.publish.MsgPublish;
import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import com.caidaocloud.message.service.application.log.dto.MessageLogRecordDto;
import com.caidaocloud.message.service.application.log.repository.IMessageLogRecordRepository;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.message.service.application.message.notifier.service.NotifyAssignEmpService;
import com.caidaocloud.message.service.application.message.notifier.service.NotifyTargetFactory;
import com.caidaocloud.message.service.application.message.send.dto.MessageContext;
import com.caidaocloud.message.service.application.message.send.handler.IMsgHandler;
import com.caidaocloud.message.service.application.msg.dto.MsgDto;
import com.caidaocloud.message.service.application.msg.event.ContractEventEntity;
import com.caidaocloud.message.service.application.msg.service.MsgConfigService;
import com.caidaocloud.message.service.application.msg.service.MsgService;
import com.caidaocloud.message.service.application.template.service.analysis.TemplateAnalysisService;
import com.caidaocloud.message.service.domain.msg.entity.AssignEmpDo;
import com.caidaocloud.message.service.interfaces.vo.msg.MsgConfigDetailVo;
import com.caidaocloud.message.service.interfaces.vo.template.TemplateVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MessageSendService {
    @Resource
    private MsgConfigService msgConfigService;
    @Resource
    private TemplateAnalysisService templateAnalysisService;
    @Resource
    private MsgService msgService;
    @Resource
    private AssignEmpDo assignEmpDo;
    @Resource
    private IMessageLogRecordRepository iMessageLogRecordRepository;
    @Resource
    private MsgPublish msgPublish;

    public void doConsumeMessage(ContractEventEntity entity) {
        MsgConfigDetailVo msgConfig = msgConfigService.getDetail(entity.getMsgConfig());
        if (msgConfig.getMsgType().equals(NoticeType.MANUAL_PUSH)) {
            //  此处不处理手动推送相关的消息
            return;
        }
        long sendTime = msgConfig.getSendTime() == null ? 0 : msgConfig.getSendTime()*1000;
        long currentTimeMillis = System.currentTimeMillis();
        if (currentTimeMillis < sendTime) {
            log.info("当前时间小于发送时间，延迟发送， sendTime:{}, currentTimeMillis:{}", sendTime, currentTimeMillis);
            msgPublish.msgNoticePublish(ObjectConverter.convert(entity, MsgNoticeDto.class), (int) (sendTime - currentTimeMillis));
        }
        else {
            messageHandle(entity, msgConfig);
        }
    }

    public void messageHandle(ContractEventEntity entity, MsgConfigDetailVo msgConfig) {
        log.info("messageHandle ContractEventEntity:{}, MsgConfigDetailVo:{}", FastjsonUtil.toJson(entity), FastjsonUtil.toJson(msgConfig));
        String msgConfigId = entity.getMsgConfig();
        // subjects 事件人 empId 集合
        List<String> subjects = entity.getSubjects();
        // 不支持传入多个事件人
        if (subjects == null || subjects.size() > 1) {
            throw new ServerException("Multiple subjects are not support");
        }
        // 是否开启事件人过滤
        final boolean subjectFilter = msgConfig.getSubjectFilter() != null && msgConfig.getSubjectFilter();
        if (isSkip(subjectFilter, msgConfig, subjects)) {
            // 如果当前事件人不符合匹配条件，则跳过消息发送
            return;
        }
        // 根据事件人查询通知对象信息
        long dataTime = System.currentTimeMillis();
        NotifyObjectDto notify = new NotifyObjectDto();
        notify.setDataTime(dataTime);
        notify.setType(entity.getType());
        notify.setEmpIds(subjects);
        Map<String, NotifierDto> notifierMap = loadNotifierMap(notify);

        List<String> notifyObject = msgConfig.getNotifyObject();
        log.info("发送消息通知对象类型：{}", notifyObject);
        // 处理指定系统人员
        List<String> assignEmpList = new ArrayList<>();
        if (!NoticeType.EMP_MANUAL_PUSH.equals(msgConfig.getMsgType())) {
            assignEmpList = getAssignEmpList(notifyObject, msgConfigId);
        }

        List<NotifierDto> receivers = new ArrayList<>();

        final boolean isMerge = mergeNotifyObject(notifyObject);

        if (null != assignEmpList && !assignEmpList.isEmpty()) {
            notify.setEmpIds(assignEmpList);
            List<NotifierDto> distinctNotifier = new ArrayList<>();
            // 添加指定系统人员
            NotifyAssignEmpService assignEmpService = (NotifyAssignEmpService) NotifyTargetFactory.getNotifyTargetService(NoticeTarget.ASSIGN_EMP.toString());
            assignEmpService.getNotifier(distinctNotifier, notify, 0);
            receivers.addAll(distinctNotifier);
        }

        notifierMap.forEach((empId, notifier) -> {
            // 添加事件人
            mergeEventEmp(isMerge, receivers, notifier);
            notify.setSubject(empId);
            // 根据事件人查找其他通知对象
            List<NotifierDto> distinctNotifier = NotifyTargetFactory.getDistinctNotifier(notifyObject, notify);
            receivers.addAll(distinctNotifier);
            if (receivers.isEmpty()) {
                // 通知对象为空
                log.warn("msgConfigId ={} subject = {} receivers is empty.", msgConfigId, empId);
                return;
            }
            log.info("notice match receivers={}", FastjsonUtil.toJson(receivers));
            // 系统人员 EmpId
            List<String> sysNoticeEmpIdList = new ArrayList<>();
            // 系统人员接收人 map
            Map<String, NotifierDto> receiverMap = new HashMap<>();
            // 非系统人员
            List<NotifierDto> otherObjList = new ArrayList<>();
            receivers.forEach(receiver -> {
                if (null == receiver.getEmpId()) {
                    // 非系统内的消息接收人
                    otherObjList.add(receiver);
                    return;
                }
                sysNoticeEmpIdList.add(receiver.getEmpId());
                receiverMap.put(receiver.getEmpId(), receiver);
            });
            // 通知对象过滤
            if (!subjectFilter) {
                retainReceiver(sysNoticeEmpIdList, msgConfig);
            }
            if (sysNoticeEmpIdList.isEmpty() && otherObjList.isEmpty()) {
                log.info("The notification object after the intersection is taken is empty,empId={}", empId);
                return;
            }

            // 对通知对象去重
            Set<String> empList = new HashSet<>(sysNoticeEmpIdList);
            log.info("remove repeated:{}", FastjsonUtil.toJson(empList));

            // 获取消息配置相关发送方式，循环接收对象发送消息
            List<TemplateVo> templates = JsonEnhanceUtil.toObjects(msgConfig.getTemplates(), TemplateVo.class);
            // 遍历消息模版进行消息发送
            MsgDto msgDto = new MsgDto();
            msgDto.setTenantId(entity.getTenantId());
            msgDto.setUserId(entity.getUserId());
            msgDto.setChannel(msgConfig.getChannel());
            msgDto.setMsgConfig(msgConfigId);
            msgDto.setMsgFrom(entity.getMsgFrom());

            MessageContext context = new MessageContext();
            context.setNoticeType(msgConfig.getMsgType());
            context.setOtherObjList(otherObjList);
            context.setNotify(notify);
            context.setReceiverMap(receiverMap);
            if (NoticeType.EMP_MANUAL_PUSH.equals(msgConfig.getMsgType())) {
                empList = new HashSet<>(entity.getSubjects());
            }
            context.setEmpList(empList);
            context.setMsgDto(msgDto);
            context.setEmpId(empId);

            for (TemplateVo template : templates) {
                // 模版变量解析
                templateAnalysisService.analysisTemplateInfo(template, empId, entity.getExt(), entity.getType());
                log.info("analysis template:{}，对应事件人：{}", FastjsonUtil.toJson(template), empId);
                context.setTemplate(template);
                IMsgHandler.handlerManager.get(template.getCategory().getValue()).msgHandler(context);
            }
            //非工作流记录消息日志
            MessageLogRecordDto messageLogRecordDto = saveLogRecord(entity, msgDto, notifier);
            msgDto.setMdc(messageLogRecordDto.getMdc());
            //  发送消息
            log.info("start send msg:{}", FastjsonUtil.toJson(msgDto));
            msgService.doMsg(msgDto);
            log.info("over send msg-------");
        });
        log.info("send message over!");
    }

    public MessageLogRecordDto saveLogRecord(ContractEventEntity entity, MsgDto msgDto, NotifierDto notifierDto) {
        MessageLogRecordDto recordDto = new MessageLogRecordDto();
        recordDto.setEmpId(notifierDto.getEmpId());
        recordDto.setWorkno(notifierDto.getWorkno());
        recordDto.setName(notifierDto.getName());
        recordDto.setType(String.valueOf(entity.getType()));
        //消息流水号
        recordDto.setMdc(StringUtils.isNotBlank(msgDto.getMdc()) ? msgDto.getMdc() : SnowUtil.nextId());
        recordDto.setMsgFrom(entity.getMsgFrom());
        recordDto.setContent(FastjsonUtil.toJson(msgDto));
        recordDto.setMsgConfig(msgDto.getMsgConfig());
        recordDto.setChannel(StringUtils.join(msgDto.getChannel(), ","));
        EnumSimple status = new EnumSimple();
        status.setText(SendStatusEnum.SUCESS.txt);
        status.setValue(SendStatusEnum.SUCESS.status);
        recordDto.setStatus(status);
        iMessageLogRecordRepository.saveMessageLogRecord(recordDto);
        return recordDto;
    }

    private List<String> getAssignEmpList(List<String> notifyObject, String msgConfig) {
        Option<String> option = Sequences.sequence(notifyObject)
                .find(notifyObj -> NoticeTarget.ASSIGN_EMP.name().equals(notifyObj));
        if (option.isDefined()) {
            notifyObject.removeIf(notifyObj -> NoticeTarget.ASSIGN_EMP.name().equals(notifyObj));
            return assignEmpDo.getByConfigId(msgConfig).stream().map(aed -> aed.getEmp().getEmpId()).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 通知对象排重
     */
    private void retainReceiver(List<String> noticeEmpIdList, MsgConfigDetailVo msgConfig) {
        if (null == msgConfig.getCondition()) {
            return;
        }

        BasePage page = new BasePage();
        page.setPageNo(1);
        page.setPageSize(2000);
        List<String> matchEmpIdList = new ArrayList<>();
        msgConfigService.doGetAndFilterEmp(msgConfig.getBid(), page, noticeEmpIdList, matchEmpIdList);
        log.info("doGetAndFilterEmp matchEmpIdList={}", FastjsonUtil.toJson(matchEmpIdList));
        noticeEmpIdList.retainAll(matchEmpIdList);
    }

    /**
     * 加载通知对象
     */
    private Map<String, NotifierDto> loadNotifierMap(NotifyObjectDto notify) {
        List<NotifierDto> notifier = NotifyTargetFactory.getNotifier(NoticeTarget.ASSIGN_EMP.toString(), notify);
        if (notifier.isEmpty()) {
            log.warn("notifier is empty. subjects = {}", FastjsonUtil.toJson(notify.getEmpIds()));
            return new HashMap<>();
        }
        return notifier.stream().collect(Collectors.toMap(NotifierDto::getEmpId, item -> item, (o1, o2) -> o1));
    }

    /**
     * 合并消息所属事件人
     */
    private void mergeEventEmp(boolean merge, List<NotifierDto> receivers, NotifierDto notifier) {
        if (!merge) {
            return;
        }

        // 事件人通知内容已经查询过，这里过滤 merge 掉，无需再次查询
        receivers.add(notifier);
    }

    private boolean mergeNotifyObject(List<String> notifyObjectList) {
        Iterator<String> iterator = notifyObjectList.iterator();
        String eventEmpKey = NoticeTarget.EVENT_EMP.toString();
        while (iterator.hasNext()) {
            if (eventEmpKey.equals(iterator.next())) {
                iterator.remove();
                return true;
            }
        }

        return false;
    }

    private boolean isSkip(boolean subjectFilter, MsgConfigDetailVo msgConfig, List<String> subjects) {
        if (!subjectFilter || msgConfig.getCondition() == null) {
            return false;
        }
        BasePage page = new BasePage();
        page.setPageSize(2000);
        page.setPageNo(1);
        List<String> matchEmpIdList = new ArrayList<>();
        msgConfigService.doGetAndFilterEmp(msgConfig.getBid(), page, subjects, matchEmpIdList);
        log.info("isSkip doGetAndFilterEmp matchEmpIdList={}", FastjsonUtil.toJson(matchEmpIdList));
        subjects.retainAll(matchEmpIdList);
        return subjects.isEmpty();
    }
}
