package com.caidaocloud.message.service.application.message.send.sms;

import com.alibaba.fastjson.JSONObject;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.message.service.application.common.enums.MsgType;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import com.caidaocloud.message.service.application.feign.SmsApiFeignClient;
import com.caidaocloud.message.service.application.log.dto.MessageLogDto;
import com.caidaocloud.message.service.application.log.repository.IMessageLogRepository;
import com.caidaocloud.message.service.application.sms.dto.SmsMessageDto;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

/**
 *
 * created by: FoAng
 * create time: 29/8/2022 2:58 下午
 */
@Service
@Slf4j
public class MessageNotifyApiImpl implements SmsMessageNotify<String> {

    @Resource
    private IMessageLogRepository messageLogRepository;

    @Resource
    private SmsApiFeignClient smsApiFeignClient;

    @Value("${caidaocloud.msg.sms.api.apiId:}")
    private String appId;

    @Value("${caidaocloud.msg.sms.api.appSecret:}")
    private String appSecret;

    @Value("${caidaocloud.msg.sms.api.sendSmsUrl:}")
    private String sendSmsUrl;

    @Value("${caidaocloud.msg.sms.api.accessTokenUrl:}")
    private String accessTokenUrl;

    @Value("${caidaocloud.msg.sms.api.signName:}")
    private String appSignName;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private Locker locker;

    @Override
    public String beforeSendSms() {
        final String tenantId = UserContext.getTenantId();
        String redisKeyAccessToken = "REDIS_KEY_ACCESS_TOKEN_%s";
        String lockerKeyAccessToken = "LOCK_KEY_ACCESS_TOKEN_%s";
        String accessTokenCache = cacheService.getValue(String.format(redisKeyAccessToken, tenantId));
        if (StringUtils.isEmpty(accessTokenCache)) {
            Lock lock = locker.getLock(String.format(lockerKeyAccessToken, tenantId));
            try {
                boolean lockResult = lock.tryLock(10, TimeUnit.SECONDS);
                if (lockResult) {
                    Map<String, Object> params = Maps.newHashMap();
                    params.put("appid", appId);
                    params.put("appsecret", appSecret);
                    String apiResult = smsApiFeignClient.getAccessToken(accessTokenUrl, params);
                    log.info("get access token result={}", apiResult);
                    JSONObject jsonResult = JSONObject.parseObject(apiResult);
                    Integer errorCode = jsonResult.getInteger("ErrCode");
                    String errorMsg = jsonResult.getString("ErrMsg");
                    JSONObject tokenData = (JSONObject) jsonResult.get("Data");
                    return Optional.ofNullable(tokenData)
                            .filter(it -> errorCode != null && errorCode == 0)
                            .map(o1 -> {
                                String accessToken = o1.getString("AccessToken");
                                Long expireTime = o1.getLong("Expires");
                                cacheService.cacheValue(String.format(redisKeyAccessToken, tenantId), accessToken, expireTime);
                                log.info("get accessToken success:{}", accessToken);
                                return accessToken;
                            }).orElseThrow(() -> new Exception("get access token error:" + errorMsg));
                }
            } catch (Exception e) {
               log.error("get access token err,{}", e.getMessage(), e);
            } finally {
                lock.unlock();
            }
        }
        return accessTokenCache;
    }

    @Override
    public void sendMessage(SmsMessageDto messageDto) {
        log.info("api sms send. message body={}", FastjsonUtil.toJson(messageDto));
        if (messageDto == null) {
            if (log.isDebugEnabled()) {
                log.debug("parameter is null, when invoke sendMessage");
            }
            return;
        }
        List<String> mobiles = messageDto.getMobile().stream()
                .map(SmsMessageDto.MobileDto::getMobile)
                .collect(Collectors.toList());
        MessageLogDto messageLogDto = new MessageLogDto();
        messageLogDto.setType(MsgType.MESSAGE);
        messageLogDto.setReceiver(StringUtils.join(mobiles, ","));
        messageLogDto.setMessage(FastjsonUtil.toJson(messageDto));
        messageLogDto.setStatus(SendStatusEnum.SUCESS);
        String bid = messageLogRepository.saveOrUpdateOfLog(messageLogDto);
        messageLogDto.setBid(bid);
        try {
            JSONObject params = new JSONObject();
            params.put("PhoneNumbers", mobiles);
            params.put("TemplateCode", messageDto.getMsgCode());
            params.put("TemplateParams", CollectionUtils.isEmpty(messageDto.getParams()) ?
                    Lists.newArrayList(Maps.newHashMap()) : messageDto.getParams());
            params.put("SignName", appSignName);
            final String accessToken = beforeSendSms();
            String apiResult = smsApiFeignClient.doSendSms(sendSmsUrl, accessToken, params);
            log.info("api send sms success. result={}", apiResult);
            JSONObject jsonResult = JSONObject.parseObject(apiResult);
            Integer code = jsonResult.getInteger("code");
            if (code == null || code != 200) {
                messageLogDto.setErrorMsg(apiResult);
                messageLogRepository.saveOrUpdateOfLog(messageLogDto);
            }
        } catch (Exception e) {
            log.error("sendMessage occur error, {} \n {}", e.getMessage(), e);
            messageLogDto.setErrorMsg(e.getMessage());
            messageLogRepository.saveOrUpdateOfLog(messageLogDto);
        }
    }

    @Override
    public String getSignType() {
        return "rocheApi";
    }
}
