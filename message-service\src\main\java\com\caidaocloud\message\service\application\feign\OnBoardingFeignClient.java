package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.hr.service.dto.EmpDataQueryDto;
import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.message.sdk.dto.*;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
    value = "caidaocloud-on-boarding-service",
    fallback = OnBoardingFeignClientFallback.class,
    configuration = FeignConfiguration.class,
    contextId = "OnBoardingFeignClient"
)
public interface OnBoardingFeignClient {
    @PostMapping("/api/hr/empData/output/v1/getEmpInfos")
    Result<List<EmpDataVo>> getEmpInfos(@RequestBody EmpDataQueryDto empDataQuery);

    /**
     * 查询个人信息
     */
    @GetMapping("/api/onboarding/employee/private/v1/info")
    Result<PreEmpPrivateInfoVo> getPrivateInfoByEmpId(@RequestParam("empId") String empId);

    /**
     * 查询任职信息
     */
    @GetMapping("/api/onboarding/employee/work/v1/info")
    Result<PreEmpWorkInfoVo> getWorkInfoByEmpId(@RequestParam("empId") String empId);

    @GetMapping("/api/onboarding/employee/work/v1/getEmpDataByEmpIds")
    Result<List<PreEmpDataVo>> getCandidateDataByEmpIds(@RequestParam("empIds") List<String> empIds);

    @PostMapping("/api/onboarding/employee/work/v1/getEmpDetails")
    Result<List<EmpDataVo>> getEmpDetailList(@RequestBody EmpDataQueryDto empDataQuery);

    /**
     * 查询候选人流程信息
     * @param empId
     * @return
     */
    @GetMapping("/api/onboarding/employee/entry/process/v1/getEmpProcessInfo")
    Result<PreEmpEntryProcessVo> getEmpProcessInfo(@RequestParam("empId") String empId);


    @GetMapping("/api/onboarding/mobile/candidate/task/v1/welcome/placeholder")
    Result<EmpWelcomeFileAttachmentVo> getEmpWelcomeFileAttachment(@RequestParam("empId") String empId);


}
