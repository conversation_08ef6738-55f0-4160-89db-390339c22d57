package com.caidaocloud.message.service.application.email.service;

import com.caidaocloud.message.service.application.email.dto.EmailTmplateMessage;
import com.caidaocloud.message.service.application.email.props.MailAuthProperties;
import com.caidaocloud.oss.service.OssService;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.sun.mail.util.MailSSLSocketFactory;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.*;
import javax.mail.Message.RecipientType;
import javax.mail.internet.*;
import javax.mail.util.ByteArrayDataSource;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.util.Calendar;
import java.util.Properties;

@Slf4j
@Component
public class SendMail {

    private static String SocketFactoryClass;

    private static boolean mailOpen;

    private static MailAuthProperties mailAuthProperties;

    @Value("${mail.smtp.socketFactory.class:}")
    public void setSocketFactoryClass(String socketFactoryClass) {
        SocketFactoryClass = socketFactoryClass;
    }

    @Value("${mail.open:true}")
    public void setMailOpen(boolean mailOpen) {
        this.mailOpen = mailOpen;
    }

    @Autowired
    public void setMailAuthProperties(MailAuthProperties mailAuthProperties) {
        SendMail.mailAuthProperties = mailAuthProperties;
    }

    @SuppressWarnings("static-access")
    public static void sendMessage(String smtpHost, String user, String from,
                                   String fromUserPassword, String port, String nick, String to, String subject,
                                   String messageText, String affix, String affixName, String cc) throws Exception {
        if(mailOpen) {
            // 第一步：配置javax.mail.Session对象
            log.info("为" + smtpHost + "配置mail session对象");

            Properties props = new Properties();
            props.put("mail.smtp.host", smtpHost);
            props.put("mail.smtp.starttls.enable", "true");// 使用 STARTTLS安全连接
            props.put("mail.smtp.auth", "true"); // 使用验证
            // props.put("mail.debug", "true");
            //-------当需使用SSL验证时添加，邮箱不需SSL验证时删除即可（测试SSL验证使用QQ企业邮箱）
//        String SSL_FACTORY="javax.net.ssl.SSLSocketFactory";
            if (StringUtils.isNotEmpty(SocketFactoryClass) && StringUtils.isNotEmpty(port) && port.equals("465")) {
                props.put("mail.smtp.socketFactory.class", SocketFactoryClass);
            }
            props.put("mail.smtp.socketFactory.fallback", "false");
            props.put("mail.smtp.socketFactory.port", port);
            props.put("mail.smtp.port", port); //google使用465或587端口
            MailSSLSocketFactory sf = new MailSSLSocketFactory();
            sf.setTrustAllHosts(true);
            props.put("mail.smtp.ssl.socketFactory", sf);
            //-------
            Session mailSession = Session.getInstance(props, new MyAuthenticator(user, fromUserPassword));

            // 第二步：编写消息
            log.info("编写消息from——to:" + from + "——" + to);

            MimeMessage message = new MimeMessage(mailSession);
            String nickName = "";
            try {
                nickName = MimeUtility.encodeText(nick);
            } catch (UnsupportedEncodingException e) {
                log.error("MimeUtility.encodeText err,{}", e.getMessage(), e);
            }
            InternetAddress fromAddress = new InternetAddress(nickName + " <" + from + ">");
            InternetAddress toAddress = new InternetAddress(to);

            // 指定发件人邮箱
            message.setFrom(fromAddress);
            message.addRecipient(RecipientType.TO, toAddress);
            // 指定抄送给谁
            if (!StringUtil.isEmpty(cc)) {
                InternetAddress[] listcs = new InternetAddress().parse(cc);
                message.setRecipients(RecipientType.CC, listcs); // 抄送人
            }
            message.setSentDate(Calendar.getInstance().getTime());
            message.setSubject(subject);
            message.setContent(messageText, "text/html;charset=utf-8");
            message.setHeader("X-Priority", "1");
            // 添加附件的内容
            if (!StringUtil.isEmpty(affix)) {
                // todo oss
                String path = "" + affix;
                File attachment = new File(path);
                if (attachment != null && attachment.exists()) {
                    Multipart multipart = new MimeMultipart();
                    // 添加邮件正文
                    BodyPart contentPart = new MimeBodyPart();
                    contentPart.setContent(messageText, "text/html;charset=UTF-8");
                    multipart.addBodyPart(contentPart);

                    BodyPart attachmentBodyPart = new MimeBodyPart();
                    DataSource source = new FileDataSource(attachment);
                    attachmentBodyPart.setDataHandler(new DataHandler(source));

                    if (affixName == null) {
                        affixName = attachment.getName();
                    }
                    // MimeUtility.encodeWord可以避免文件名乱码
                    attachmentBodyPart.setFileName(MimeUtility.encodeWord(affixName));
                    multipart.addBodyPart(attachmentBodyPart);
                    message.setContent(multipart);
                }
            }

            // 第三步：发送消息
            Transport transport = mailSession.getTransport("smtp");
            transport.connect(smtpHost, user, fromUserPassword);
            transport.send(message, message.getRecipients(RecipientType.TO));
            log.info("message yes");
        }else{
            log.warn("发送邮件的开关未开启");
        }
    }

    public static void sendMessage2(String smtpHost, String fromUserPassword, String port, String from, String nick, EmailTmplateMessage template) throws Exception {
        if(mailOpen) {
            log.info("为" + smtpHost + "配置mail session对象 收件人:" + template.getTo());
            Properties props = new Properties();
            props.setProperty("mail.smtp.host", smtpHost);
            props.setProperty("mail.transport.protocol", "smtp");
            props.setProperty("mail.smtp.starttls.enable", mailAuthProperties.isStarttls() ? "true" : "false");
            props.setProperty("mail.smtp.port", port);
            if (mailAuthProperties.isSsl()) {
                props.setProperty("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
                props.setProperty("mail.smtp.socketFactory.fallback", "false");
                props.setProperty("mail.smtp.socketFactory.port", port);
                /*创建ssl认证配置*/
                MailSSLSocketFactory sf = new MailSSLSocketFactory();
                sf.setTrustAllHosts(true);
                props.put("mail.smtp.ssl.socketFactory", sf);
            }
            Session mailSession;
            if (mailAuthProperties.isAuth()) {
                props.setProperty("mail.smtp.auth", "true");
                mailSession = Session.getDefaultInstance(props, new Authenticator() {
                    @Override
                    protected PasswordAuthentication getPasswordAuthentication() {
                        return new PasswordAuthentication(from, fromUserPassword);
                    }
                });
            } else {
                mailSession = Session.getDefaultInstance(props);
            }
            MimeMessage message = new MimeMessage(mailSession);
            String nickName;
            try {
                nickName = MimeUtility.encodeText(nick);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
                nickName = "";
            }
            InternetAddress fromAddress = new InternetAddress(nickName + " <" + from + ">");
            // 指定发件人邮箱
            message.setFrom(fromAddress);
            message.addRecipients(RecipientType.TO, buildInternetList(template.getTo()));
            // 指定抄送给谁
            if (!StringUtil.isEmpty(template.getCc())) {
                message.setRecipients(RecipientType.CC, buildInternetList(template.getCc()));
            }
            // 密送
            if (!StringUtil.isEmpty(template.getBcc())) {
                message.setRecipients(RecipientType.BCC, buildInternetList(template.getBcc()));
            }
            message.setSentDate(Calendar.getInstance().getTime());
            message.setSubject(template.getSubject());
            message.setContent(template.getContent(), "text/html;charset=utf-8");
            message.setHeader("X-Priority", "1");
            // 添加附件的内容
            if (!StringUtil.isEmpty(template.getAffix())) {
                Multipart multipart = new MimeMultipart();
                // 添加邮件正文
                BodyPart contentPart = new MimeBodyPart();
                contentPart.setContent(template.getContent(), "text/html;charset=UTF-8");
                multipart.addBodyPart(contentPart);
                //添加邮件附件
                String[] filePaths = template.getAffix().split(",");
                String[] affixNames = StringUtils.split(template.getAffixName(),",");
                String path;
                for (int i = 0; i <filePaths.length ; i++) {
                    path = filePaths[i];
                    path = "" + path;
                    log.info("---------filePath={}", path);

                    ByteArrayDataSource byteArrayResource =
                            new ByteArrayDataSource(SpringUtil.getBean(OssService.class).getInputStream(path), "application/octet-stream");
                    BodyPart attachmentBodyPart = new MimeBodyPart();
                    attachmentBodyPart.setDataHandler(new DataHandler(byteArrayResource));
                    if(affixNames != null && affixNames.length-1 >= i && StringUtils.isNotBlank(affixNames[i])){
                        // MimeUtility.encodeWord可以避免文件名乱码
                        attachmentBodyPart.setFileName(MimeUtility.encodeWord(affixNames[i]));
                        template.setAffixName(null);
                    }else{
                        attachmentBodyPart.setFileName(MimeUtility.encodeWord(affixNames[i]));
                    }
                    multipart.addBodyPart(attachmentBodyPart);
                }
                message.setContent(multipart);
            }
            message.saveChanges();
            // 第三步：发送消息
            Transport.send(message);
            log.info("send success");
        }else{
            log.warn("发送邮件的开关未开启");
        }
    }

    @SneakyThrows
    public static InternetAddress[] buildInternetList(String targetStr) {
        InternetAddress[] targetAddress = new InternetAddress[targetStr.split(";").length];
        String[] targetArrays = targetStr.split(";");
        for (int i = 0; i < targetArrays.length; i++) {
            targetAddress[i] = new InternetAddress(targetArrays[i]);
        }
        return targetAddress;
    }
}

class MyAuthenticator extends Authenticator {
    String userName = "";
    String password = "";

    public MyAuthenticator() {

    }

    public MyAuthenticator(String userName, String password) {
        this.userName = userName;
        this.password = password;
    }

    protected PasswordAuthentication getPasswordAuthentication() {
        return new PasswordAuthentication(userName, password);
    }
}