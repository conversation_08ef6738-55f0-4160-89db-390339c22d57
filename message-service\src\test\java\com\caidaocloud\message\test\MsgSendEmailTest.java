package com.caidaocloud.message.test;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.msg.dto.EmailMsgDto;
import com.caidaocloud.message.service.application.msg.dto.MsgDto;
import com.caidaocloud.message.service.application.msg.service.MsgConfigService;
import com.caidaocloud.message.service.application.msg.service.MsgService;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.domain.msg.entity.MsgConfigDo;
import com.caidaocloud.message.service.interfaces.dto.msg.MsgConfigDto;
import com.caidaocloud.message.service.interfaces.dto.msg.MsgConfigQueryDto;
import com.caidaocloud.message.service.interfaces.vo.msg.MsgConfigListVo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * MsgSendEmailTest
 *
 * <AUTHOR>
 * @date 2022/6/9 下午3:25
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class MsgSendEmailTest {
    @Resource
    private MsgService msgService;

    @Resource
    private MsgConfigService msgConfigService;

    @Before
    public void initUser(){
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("33");
        userInfo.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);

        UserInfo user = new UserInfo();
        user.setTenantId(String.valueOf(33));
        user.doSetUserId(0L);
        UserContext.setCurrentUser(user);
    }

    @Test
    public void testList(){
        MsgConfigQueryDto msgConfigQueryDto = new MsgConfigQueryDto();
        PageResult<MsgConfigDo> page = msgConfigService.getPage(msgConfigQueryDto);
        msgConfigService.toVoList(page.getItems());
    }

    @Test
    public void testEnableMsgConfig(){
//        msgConfigService.enable("1460766325880846");
    }

    @Test
    public void testUpdateMsgConfig() {
        String reqBody = "{\n" +
                "    \"bid\":\"1462753599207425\",\n" +
                "    \"templates\":[\n" +
                "        {\n" +
                "            \"bid\":\"1462753601779715\",\n" +
                "            \"name\":\"测试长度\",\n" +
                "            \"content\":\"<p><span style=\\\"color: rgb(16, 129, 246);\\\"><strong>手动推送取消测试1122222256666</strong></span></p>\",\n" +
                "            \"category\":{\n" +
                "                \"text\":\"邮件模版\",\n" +
                "                \"value\":\"1\"\n" +
                "            },\n" +
                "            \"title\":\"手动推送取消测试\",\n" +
                "            \"attachFile\":{\n" +
                "                \"names\":[\n" +
                "\n" +
                "                ],\n" +
                "                \"urls\":[\n" +
                "\n" +
                "                ]\n" +
                "            },\n" +
                "            \"msgConfig\":\"1462753599207425\",\n" +
                "            \"variable\":[\n" +
                "\n" +
                "            ],\n" +
                "            \"copyObject\":[\n" +
                "\n" +
                "            ],\n" +
                "            \"copyMail\":null,\n" +
                "            \"externalMail\":null,\n" +
                "            \"noticeRule\":\"0\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"msgType\":\"MANUAL_PUSH\",\n" +
                "    \"name\":\"手动推送取消测试\",\n" +
                "    \"notifyObject\":[\n" +
                "        \"ASSIGN_EMP\"\n" +
                "    ],\n" +
                "    \"channel\":[\n" +
                "        \"1\"\n" +
                "    ],\n" +
                "    \"emps\":[\n" +
                "        {\n" +
                "            \"dataStartTime\":0,\n" +
                "            \"empId\":\"1434400592427011\",\n" +
                "            \"workno\":\"E123456\",\n" +
                "            \"name\":\"卢芳格\",\n" +
                "            \"enName\":\"ethan\",\n" +
                "            \"deptDesc\":null\n" +
                "        }\n" +
                "    ],\n" +
                "    \"triggerDate\":1658678400000,\n" +
                "    \"sendTime\":1658726400\n" +
                "}";

        MsgConfigDto msgDto = FastjsonUtil.convertObject(reqBody, MsgConfigDto.class);

//        msgConfigService.saveOrUpdateObj(msgDto);

        // 检查消息配置是否已启用
//        Boolean msgConfigStatus = msgConfigService.checkMsgConfigEnable("33", msgDto.getBid());
//        if (!msgConfigStatus) {
//            log.info("doMsg msgConfigStatus is not enable msgConfig={}", msgDto.getBid());
//        }
    }

    @Test
    public void testDoMsg() {
        MsgDto msgDto = new MsgDto();
        msgDto.setChannel(Lists.newArrayList("1"));
        msgDto.setTenantId("33");
        msgDto.setUserId(0L);

        EmailMsgDto emailMsgDto = new EmailMsgDto();
        emailMsgDto.setCc("<EMAIL>");
        emailMsgDto.setContent("hello caidao,hello disney111111111");
        emailMsgDto.setTo("<EMAIL>;<EMAIL>");
        emailMsgDto.setSubject("111111111111111");

        msgDto.setEmailMsg(emailMsgDto);
        System.out.println(FastjsonUtil.toJson(msgDto));

        try {
            SecurityUserInfo userInfo = new SecurityUserInfo();
            String tenantId = msgDto.getTenantId();
            userInfo.setTenantId(tenantId);
            Long userId = msgDto.getUserId();
            userInfo.setUserId(userId);
            SecurityUserUtil.setSecurityUserInfo(userInfo);

            UserInfo user = new UserInfo();
            user.setTenantId(tenantId);
            user.doSetUserId(userId);
            UserContext.setCurrentUser(user);

//            msgService.doMsg(msgDto);
        } catch (Exception e) {
            log.error("process plain msg err,{}", e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }
}
