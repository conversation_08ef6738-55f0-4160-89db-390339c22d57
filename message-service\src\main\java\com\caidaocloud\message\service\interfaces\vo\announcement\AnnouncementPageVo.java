package com.caidaocloud.message.service.interfaces.vo.announcement;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.message.service.domain.announcement.enums.AnnouncementStatus;
import com.caidaocloud.message.service.domain.announcement.enums.ReceiveType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@ApiModel(description = "公告Vo")
public class AnnouncementPageVo {

	@ApiModelProperty(value = "公告ID")
	private String bid;

	@ApiModelProperty(value = "公告名称")
	@NotBlank(message = "公告名称不能为空")
	private String name;

	@ApiModelProperty(value = "公告类型")
	@NotBlank(message = "公告类型不能为空")
	private String type;

	@ApiModelProperty(value = "发布时间（Unix时间戳，毫秒）")
	private Long releaseTime;

	@ApiModelProperty(value = "生效时间（Unix时间戳，毫秒）")
	private Long effectiveTime;

	@ApiModelProperty(value = "到期时间（Unix时间戳，毫秒）")
	private Long expiryTime;

	@ApiModelProperty("创建人")
	private String createBy;

	@ApiModelProperty("创建时间")
	private Long createTime;

	@ApiModelProperty("最后一次编辑时间")
	private Long updateTime;

	@ApiModelProperty(value = "接收人员类型")
	private ReceiveType receiveType;

	@ApiModelProperty(value = "是否置顶")
	private Boolean isTop = false;

	@ApiModelProperty(value = "公告适用域名列表")
	private List<String> domain;

	@ApiModelProperty("发布状态")
	private AnnouncementStatus status;

}
