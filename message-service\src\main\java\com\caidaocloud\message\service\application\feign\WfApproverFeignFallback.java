package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.message.service.application.feign.dto.WorkflowApproverSetting;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/3/21 下午5:46
 * @Version 1.0
 **/
@Component
public class WfApproverFeignFallback implements IWfApproverFeign {


    @Override
    public Result<List<WorkflowApproverSetting>> listSettings() {
        return Result.fail();
    }

    @Override
    public Result<EmpWorkInfoVo> getEmpWorkInfo(String empId, Long dataTime) {
        return Result.fail();
    }

    @Override
    public Result<WorkflowApproverSetting> loadSetting(String bid) {
        return Result.fail();
    }

    @Override
    public Result<String> getTransferApprovers(String applicantId, String code, String businessKey) {
        return Result.fail();
    }

    @Override
    public Result<String> getTransferBeforeApprovers(String applicantId, String code, String businessKey) {
        return Result.fail();
    }
}

 
    
    
    
    