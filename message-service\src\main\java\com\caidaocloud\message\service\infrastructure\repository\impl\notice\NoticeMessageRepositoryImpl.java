package com.caidaocloud.message.service.infrastructure.repository.impl.notice;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.GroupedIndicateResult;
import com.caidaocloud.hrpaas.metadata.sdk.enums.IndicateType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.message.sdk.enums.NoticeBusinessEnum;
import com.caidaocloud.message.sdk.enums.NoticeMsgTypeEnum;
import com.caidaocloud.message.service.application.notice.dto.NoticeMsgDto;
import com.caidaocloud.message.service.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.message.service.domain.notice.repository.NoticeMessageRepository;
import com.caidaocloud.message.service.infrastructure.es.NoticeEsData;
import com.caidaocloud.message.service.infrastructure.repository.po.NoticeMessagePo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.refresh.RefreshRequest;
import org.elasticsearch.action.admin.indices.refresh.RefreshResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.zxp.esclientrhl.index.ElasticsearchIndex;
import org.zxp.esclientrhl.repository.PageList;
import org.zxp.esclientrhl.repository.PageSortHighLight;
import org.zxp.esclientrhl.util.MetaData;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 15/8/2024 2:45 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class NoticeMessageRepositoryImpl extends BaseRepositoryImpl<NoticeMessagePo> implements NoticeMessageRepository {

    private NoticeEsRepository noticeEsRepository;

    private ElasticsearchIndex<NoticeEsData> elasticsearchIndex;

    private RestHighLevelClient client;

    @Override
    public NoticeMessagePo insert(NoticeMessagePo data) {
        NoticeMessagePo noticeMessageDo = super.insert(data);
        saveEsProperty(noticeMessageDo);
        return noticeMessageDo;
    }

    @Override
    public int delete(NoticeMessagePo data) {
        deleteEsData(Long.valueOf(data.getBid()));
        return super.delete(data);
    }


    @Override
    public Map<String, Integer> getTypeUnReadCount(String identifier, String empId) {
        DataFilter dataFilter = getBaseFilter().andEq("markRead", "false")
                .andEq("receiver$empId", empId);
        List<GroupedIndicateResult> indicateResults = DataQuery.identifier(identifier).indicateByGroup(dataFilter, "", IndicateType.COUNT,
                System.currentTimeMillis(), "type");
        if (!CollectionUtils.isEmpty(indicateResults)) {
            return indicateResults.stream().collect(Collectors.toMap(it -> it.getBy().values().stream().findFirst().map(o1 -> {
                NoticeMsgTypeEnum typeEnum = NoticeMsgTypeEnum.of(o1);
                return typeEnum.getValue().toLowerCase();
            }).orElse(""), o1 -> Integer.valueOf(o1.getIndicate()), (v1, v2) -> v2));
        }
        return new HashMap<>();
    }

    @Override
    public Map<String, Integer> getBusinessUnReadCount(String identifier, String receiver) {
        DataFilter dataFilter = getBaseFilter().andEq("markRead", "false")
                .andEq("receiver$empId", receiver);
        List<GroupedIndicateResult> indicateResults = DataQuery.identifier(identifier).indicateByGroup(dataFilter, "", IndicateType.COUNT,
                System.currentTimeMillis(), "businessType");
        if (!CollectionUtils.isEmpty(indicateResults)) {
            return indicateResults.stream().collect(Collectors.toMap(it -> it.getBy().values().stream().findFirst().map(o1 -> {
                NoticeBusinessEnum businessEnum = NoticeBusinessEnum.of(o1);
                return businessEnum.getValue().toLowerCase();
            }).orElse(""), o1 -> Integer.valueOf(o1.getIndicate()), (v1, v2) -> v2));
        }
        return new HashMap<>();
    }

    @Override
    public PageResult<NoticeMessagePo> listByBusinessId(String identifier, String businessType, String businessId) {
        DataFilter dataFilter = getBaseFilter().andEq("markRead", "false")
                .andEq("businessId", businessId).andEq("businessType", businessType);
        return DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .filter(dataFilter, NoticeMessagePo.class);
    }

    @Override
    public PageResult<NoticeMessagePo> pageByExtData(String identifier, String receiver, NoticeMsgDto dto) {
        DataFilter dataFilter = getBaseFilter();
        // 关键字、扩展字段搜索
        int totalSize = 0;
        if (StringUtil.isNotEmpty(dto.getKeyword()) || dto.getExt() != null) {
            List<Long> bids = queryExtBids(dto, receiver);
            totalSize = bids.size();
            if (CollectionUtils.isEmpty(bids)) {
                dataFilter = dataFilter.andEq("bid", "-1");
            } else {
                dataFilter = dataFilter.andIn("bid", bids.stream().map(String::valueOf).collect(Collectors.toList()));
            }
        }

        if (StringUtil.isNotEmpty(receiver)) {
            dataFilter = dataFilter.andEq("receiver$empId", receiver);
        }

        if (dto.getStartTime() != null) {
            dataFilter = dataFilter.andGe("createTime", String.valueOf(dto.getStartTime()));
        }

        if (dto.getEndTime() != null) {
            dataFilter = dataFilter.andLe("createTime", String.valueOf(dto.getEndTime()));
        }

        if (dto.getMarkRead() != null) {
            dataFilter = dataFilter.andEq("markRead", dto.getMarkRead() ? "true" : "false");
        }

        if (dto.getBusinessType() != null) {
            dataFilter = dataFilter.andEq("businessType", dto.getBusinessType().getValue());
        }

        if (StringUtil.isNotEmpty(dto.getBusinessId())) {
            dataFilter = dataFilter.andEq("businessId", dto.getBusinessId());
        }

        if (dto.getType() != null) {
            dataFilter = dataFilter.andEq("type", dto.getType().getValue());
        }

        PageResult<NoticeMessagePo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(dto.getPageSize(), dto.getPageNo()).filter(dataFilter, NoticeMessagePo.class);
        pageResult.setTotal(Math.max(pageResult.getTotal(), totalSize));
        return pageResult;
    }

    @Override
    public void markReadAction(String identifier, List<String> bids, String businessType, boolean markRead) {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        if (CollectionUtils.isEmpty(bids)) {
            markAllReadAction(identifier, String.valueOf(userInfo.getEmpId()), businessType, markRead);
        } else {
            List<NoticeMessagePo> messageDos = selectBatchIds(bids, identifier);
            messageDos.forEach(it -> {
                try {
                    it.setMarkRead(markRead);
                    updateById(it);
                } catch (Exception e) {
                    log.error("[notice] mark read error, msg:{}", e.getMessage(), e);
                }
            });
        }
    }

    @Override
    public void markAllReadAction(String identifier, String receiver, String businessType, boolean markRead) {
        NoticeMsgDto dto = new NoticeMsgDto();
        dto.setMarkRead(false);
        dto.setBusinessType(NoticeBusinessEnum.of(businessType));
        PageResult<NoticeMessagePo> pageResult;
        do {
            pageResult = pageByExtData(identifier, receiver, dto);
            dto.setPageNo(dto.getPageNo() + 1);
            if (!CollectionUtils.isEmpty(pageResult.getItems())) {
                markReadAction(identifier, pageResult.getItems().stream().map(NoticeMessagePo::getBid).map(String::valueOf)
                        .collect(Collectors.toList()), businessType, markRead);
            }
        } while (!CollectionUtils.isEmpty(pageResult.getItems()));
    }


    @Override
    public void persistEsData(List<NoticeMessagePo> noticeMessagePos) {
        Optional<List<NoticeEsData>> esDataList = Optional.ofNullable(noticeMessagePos)
                .map(it -> it.stream().map(this::convertEsData).collect(Collectors.toList()));
        esDataList.ifPresent(it -> {
            checkEsIndexAuto();
            try {
                noticeEsRepository.save(it);
            } catch (Exception e) {
                log.error("[notice] batch save error:{}", e.getMessage());
            }
        });
    }


    @SneakyThrows
    private void saveEsProperty(NoticeMessagePo notice) {
        try {
            checkEsIndexAuto();
            noticeEsRepository.save(convertEsData(notice));
        } catch (Exception e) {
            log.error("[notice] save es notice error, {}", e.getMessage());
        }
    }

    private NoticeEsData convertEsData(NoticeMessagePo notice) {
        return new NoticeEsData()
                .setTitle(notice.getTitle())
                .setReceiver(notice.getReceiver())
                .setContent(notice.getContent())
                .setSummary(notice.getSummary())
                .setBusinessType(notice.getBusinessType())
                .setType(Optional.ofNullable(notice.getType()).map(EnumSimple::getValue).orElse(""))
                .setId(Long.valueOf(notice.getBid()))
                .setExt(FastjsonUtil.toObject(Optional.ofNullable(notice.getExt()).orElse(""),
                        new TypeReference<Map<String, Object>>(){}));
    }

    @SneakyThrows
    private void deleteEsData(Long bid) {
        NoticeEsData noticeEsData = new NoticeEsData()
                .setId(bid);
        noticeEsRepository.delete(noticeEsData);
    }

    @SneakyThrows
    private boolean checkEsIndex() {
        return elasticsearchIndex.exists(NoticeEsData.class);
    }

    @SneakyThrows
    private void checkEsIndexAuto() {
        boolean checkResult = elasticsearchIndex.exists(NoticeEsData.class);
        if (!checkResult) {
            elasticsearchIndex.createIndex(NoticeEsData.class);
        }
    }

    @Override
    public void truncateEsIndex() {
        try {
            elasticsearchIndex.dropIndex(NoticeEsData.class);
        } catch (Exception e) {
            log.error("[notice] drop es data error, {}", e.getMessage(), e);
        }
    }

    @Override
    public void refreshEsData() {
        MetaData metaData = this.elasticsearchIndex.getMetaData(NoticeEsData.class);
        String[] indexNames = metaData.getSearchIndexNames();
        try {
            RefreshRequest refreshRequest = new RefreshRequest(indexNames);
            RefreshResponse refreshResponse = this.client.indices().refresh(refreshRequest, RequestOptions.DEFAULT);
            if (refreshResponse.getStatus() != RestStatus.OK) {
                log.error("[notice] refresh es data error, status: {}", refreshResponse.getStatus());
            }
        } catch (IOException e) {
            log.error("[notice] refresh es data error, msg: {}", e.getMessage(), e);
        }
    }

    /**
     * 根据扩展参数查询主键
     * @return
     */
    @SneakyThrows
    private List<Long> queryExtBids(NoticeMsgDto msgDto, String receiver) {
        if (!checkEsIndex())  return Lists.newArrayList();
        String keyword = msgDto.getKeyword();
        Map<String, Object> ext = msgDto.getExt();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        int currentPage = msgDto.getPageNo();
        int pageSize = msgDto.getPageSize();
        PageSortHighLight pageSortHighLight = new PageSortHighLight(currentPage, pageSize);
        if (StringUtil.isNotEmpty(keyword)) {
            BoolQueryBuilder keywordQuery = QueryBuilders.boolQuery();
            keywordQuery.should(QueryBuilders.wildcardQuery("title", String.format("*%s*", keyword)))
                    .should(QueryBuilders.wildcardQuery("summary", String.format("*%s*", keyword)))
                    .should(QueryBuilders.wildcardQuery("content", String.format("*%s*", keyword)))
                    .minimumShouldMatch(1);
            queryBuilder.must(keywordQuery);
        }
        if (!CollectionUtils.isEmpty(ext)) {
            BoolQueryBuilder extQuery = QueryBuilders.boolQuery();
            ext.keySet().forEach(it -> {
                extQuery.should(QueryBuilders.wildcardQuery(String.format("ext.%s", it), String.format("*%s*", ext.get(it))))
                    .should(QueryBuilders.wildcardQuery(String.format("ext.%s.keyword", it), String.format("*%s*", ext.get(it))));
            });
            extQuery.minimumShouldMatch(1);
            queryBuilder.must(extQuery);
        }
        if (StringUtil.isNotEmpty(receiver)) {
            queryBuilder.must(QueryBuilders.termQuery("receiver.empId", receiver));
        }

        if (msgDto.getType() != null) {
            queryBuilder.must(QueryBuilders.termQuery("type", msgDto.getType().getValue()));
        }

        if (msgDto.getBusinessType() != null) {
            queryBuilder.must(QueryBuilders.termQuery("businessType", msgDto.getBusinessType().getValue()));
        }

        PageList<NoticeEsData> esDataPageList = noticeEsRepository.search(queryBuilder, pageSortHighLight);
        if (esDataPageList != null && !CollectionUtils.isEmpty(esDataPageList.getList())) {
            return esDataPageList.getList().stream().map(NoticeEsData::getId).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }
}
