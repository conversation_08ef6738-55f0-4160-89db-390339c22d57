package com.caidaocloud.message.service.application.app.dto;

import com.caidaocloud.message.service.application.app.enums.MsgAccountType;
import com.caidaocloud.message.service.application.app.enums.MsgPushType;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AppMsgSender {
    private String userId;
    private MsgPushType msgType = MsgPushType.text;

    private String title;

    private String content;

    private Long createTime = System.currentTimeMillis() / 1000L;

    private MsgAccountType type = MsgAccountType.email;

    // 代办消息的图片地址
    private String picUrls;

    private String url;

    private String msgId;

    /**
     * 发送状态
     */
    private String status = SendStatusEnum.SUCESS.status;
}
