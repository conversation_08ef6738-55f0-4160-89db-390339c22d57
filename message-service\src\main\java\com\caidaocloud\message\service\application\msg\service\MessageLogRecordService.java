package com.caidaocloud.message.service.application.msg.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import com.caidaocloud.message.service.application.app.dto.AppMsgSender;
import com.caidaocloud.message.service.application.common.enums.MsgTemplateEnum;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import com.caidaocloud.message.service.application.log.repository.IMessageLogRecordRepository;
import com.caidaocloud.message.service.application.msg.dto.*;
import com.caidaocloud.message.service.application.sms.dto.SmsMessageDto;
import com.caidaocloud.message.service.domain.msg.entity.MessageLogRecordDetailVo;
import com.caidaocloud.message.service.domain.msg.entity.MessageLogRecordDo;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MessageLogRecordService {
    @Resource
    private IMessageLogRecordRepository iMessageLogRecordRepository;

    public List<MessageLogRecordDetailVo> getMessageLogRecord(Long bid) {
        List<MessageLogRecordDetailVo> list = Lists.newArrayList();
        MessageLogRecordDo logRecordDo = iMessageLogRecordRepository.getMessageLogRecordByBid(bid);
        if (logRecordDo == null) {
            throw new ServerException("消息记录不存在！");
        }

        MsgDto msgDto = FastjsonUtil.toObject(logRecordDo.getContent(), MsgDto.class);
        for (String channel : msgDto.getChannel()) {
            switch (channel) {
                case "0":
                    // 短信
                    buildSmsMessage(logRecordDo, msgDto, list);
                    break;
                case "1":
                    // 邮件通知
                    buildEmail(logRecordDo, msgDto, list);
                    break;
                case "2":
                    // 系统通知
                    buildSystemNoticeMsg(logRecordDo, msgDto, list);
                    break;
                case "3":
                    // app 通知
                    buildAppMsg(logRecordDo, msgDto, list);
                    break;
                case "4":
                    // 钉钉消息通知
                    buildDingtalkMessage(logRecordDo, msgDto, list);
                    break;
                case "5":
                    // 钉钉待办通知
                    buildDingtalkToDoNoticeMessage(logRecordDo, msgDto, list);
                    break;
                default:
            }
        }

        return list;
    }

    /**
     * 钉钉待办通知
     * @param logRecordDo
     * @param msgDto
     * @param list
     */
    private void buildDingtalkToDoNoticeMessage(MessageLogRecordDo logRecordDo, MsgDto msgDto, List<MessageLogRecordDetailVo> list) {
        MessageLogRecordDetailVo detailVo = new MessageLogRecordDetailVo();
        DingtalkToDoNoticeDto dingtalkToDoNoticeDto = msgDto.getDingtalkToDoNoticeDto();

        detailVo.setChannel(MsgTemplateEnum.DING_TALK_TODO.getName());
        detailVo.setSubject(dingtalkToDoNoticeDto.getSubject());
        detailVo.setStatus(convertEnum(dingtalkToDoNoticeDto.getStatus()));
        detailVo.setSendTime(logRecordDo.getUpdateTime());
        Optional.ofNullable(dingtalkToDoNoticeDto.getMobiles()).ifPresent(mobiles -> {
            String receivers = mobiles.stream().map(PhoneSimple::getValue).collect(Collectors.joining(","));
            detailVo.setReceiver(receivers);
        });
        list.add(detailVo);
    }

    private EnumSimple convertEnum(SendStatusEnum status) {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(status.getStatus());
        enumSimple.setText("success".equals(status.getTxt()) ? "成功" : "失败");
        return enumSimple;
    }

    /**
     * 钉钉消息通知
     * @param logRecordDo
     * @param msgDto
     * @param list
     */
    private void buildDingtalkMessage(MessageLogRecordDo logRecordDo, MsgDto msgDto, List<MessageLogRecordDetailVo> list) {
        MessageLogRecordDetailVo detailVo = new MessageLogRecordDetailVo();
        DingtalkMsgDto dingTalkMsg = msgDto.getDingTalkMsg();

        detailVo.setChannel(MsgTemplateEnum.DING_TALK.getName());
        detailVo.setSubject(dingTalkMsg.getSubject());
        detailVo.setStatus(convertEnum(dingTalkMsg.getStatus()));
        detailVo.setSendTime(logRecordDo.getUpdateTime());
        Optional.ofNullable(dingTalkMsg.getMobiles()).ifPresent(mobiles -> {
            String receivers = mobiles.stream().map(PhoneSimple::getValue).collect(Collectors.joining(","));
            detailVo.setReceiver(receivers);
        });
        list.add(detailVo);
    }

    private void buildAppMsg(MessageLogRecordDo logRecordDo, MsgDto msgDto, List<MessageLogRecordDetailVo> list) {
        MessageLogRecordDetailVo detailVo = new MessageLogRecordDetailVo();
        List<AppMsgSender> appMsg = msgDto.getAppMsg();
        AppMsgSender appMsgSender = appMsg.get(0);
        detailVo.setChannel(MsgTemplateEnum.APP.getName());
        detailVo.setSubject(appMsgSender.getTitle());
        detailVo.setStatus(logRecordDo.getStatus());
        detailVo.setSendTime(logRecordDo.getUpdateTime());
        detailVo.setReceiver(appMsgSender.getUserId());
        list.add(detailVo);
    }

    private void buildSystemNoticeMsg(MessageLogRecordDo logRecordDo, MsgDto msgDto, List<MessageLogRecordDetailVo> list) {
        MessageLogRecordDetailVo detailVo = new MessageLogRecordDetailVo();
        NoticeMessageDto noticeMsg = msgDto.getNoticeMsg();
        Optional.ofNullable(noticeMsg).ifPresent(it -> {
            detailVo.setChannel(MsgTemplateEnum.SYSTEM.getName());
            detailVo.setSubject(noticeMsg.getTitle());
            detailVo.setStatus(logRecordDo.getStatus());
            detailVo.setSendTime(logRecordDo.getUpdateTime());
            detailVo.setReceiver(Optional.ofNullable(noticeMsg.getEmpIds()).map(o1 -> String.join(",", o1))
                    .orElse(""));
            list.add(detailVo);
        });
    }

    private void buildEmail(MessageLogRecordDo logRecordDo, MsgDto msgDto, List<MessageLogRecordDetailVo> list) {
        MessageLogRecordDetailVo detailVo = new MessageLogRecordDetailVo();
        EmailMsgDto emailMsg = msgDto.getEmailMsg();

        detailVo.setChannel(MsgTemplateEnum.EMAIL.getName());
        detailVo.setSubject(emailMsg.getSubject());
        detailVo.setStatus(convertEnum(emailMsg.getStatus()));
        detailVo.setSendTime(logRecordDo.getUpdateTime());
        //收件人
        String to = emailMsg.getTo();
        detailVo.setReceiver(to);
        list.add(detailVo);
    }

    /**
     * 短信
     * @param logRecordDo
     * @param msgDto
     * @param list
     */
    private void buildSmsMessage(MessageLogRecordDo logRecordDo, MsgDto msgDto, List<MessageLogRecordDetailVo> list) {
        MessageLogRecordDetailVo detailVo = new MessageLogRecordDetailVo();
        SmsMessageDto message = msgDto.getMessage();

        detailVo.setChannel(MsgTemplateEnum.SMS.getName());
        detailVo.setSubject("");
        detailVo.setStatus(convertEnum(message.getStatus()));
        detailVo.setSendTime(logRecordDo.getUpdateTime());
        Optional.ofNullable(message.getMobile()).ifPresent(mobiles -> {
            String receivers = mobiles.stream().map(SmsMessageDto.MobileDto::getMobile).collect(Collectors.joining(","));
            detailVo.setReceiver(receivers);
        });
        list.add(detailVo);
    }
}
