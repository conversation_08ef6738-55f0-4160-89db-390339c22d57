package com.caidaocloud.message.service.domain.email.service;

import com.caidaocloud.message.service.domain.email.dto.LogEmailDto;
import com.caidaocloud.message.service.domain.email.entity.LogEmailDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class LogEmailDomainServiceImpl implements LogEmailDomainService{
    @Resource
    private LogEmailDo logEmailDo;
    @Override
    public String save(LogEmailDto logEmailDto) {
        return logEmailDo.save(logEmailDto);
    }

    @Override
    public void update(LogEmailDto logEmailDto) {
        logEmailDo.update(logEmailDto);
    }
}
