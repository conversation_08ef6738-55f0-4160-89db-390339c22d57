package com.caidaocloud.message.service.application.message.send.sms;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.aliyun.dingtalktodo_1_0.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.caidaocloud.message.sdk.enums.DingtalkTemplateTypeEnum;
import com.caidaocloud.message.service.application.common.enums.MsgType;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import com.caidaocloud.message.service.application.feign.DingTalkFeignClient;
import com.caidaocloud.message.service.application.feign.UserServiceFeignClent;
import com.caidaocloud.message.service.application.feign.dto.UserDetailInfoVo;
import com.caidaocloud.message.service.application.log.dto.MessageLogDto;
import com.caidaocloud.message.service.application.log.repository.IMessageLogRepository;
import com.caidaocloud.message.service.application.msg.dto.DingtalkMsgDto;
import com.caidaocloud.message.service.application.msg.dto.DingtalkToDoNoticeDto;
import com.caidaocloud.message.service.application.msg.dto.MsgDto;
import com.caidaocloud.message.service.application.msg.event.CommonMsgPublish;
import com.caidaocloud.message.service.infrastructure.utils.FileUtils;
import com.caidaocloud.oss.service.OssService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMediaUploadRequest;
import com.dingtalk.api.response.OapiMediaUploadResponse;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.api.ApiException;
import com.taobao.api.FileItem;
import com.vladsch.flexmark.html2md.converter.FlexmarkHtmlConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/2/10 下午4:31
 * @Version 1.0
 **/
@Service
@Slf4j
public class DingMessageNotifyApi {
    @NacosValue("${caidaocloud.msg.dingtalk.agentId:}")
    private String agentId;
    @NacosValue("${caidaocloud.msg.dingtalk.appKey:}")
    private String appKey;
    @NacosValue("${caidaocloud.msg.dingtalk.appSecret:}")
    private String appSecret;
    @NacosValue("${caidaocloud.msg.dingtalk.url:https://oapi.dingtalk.com/media/upload}")
    private String dingServiceUrl;
    @NacosValue("${caidaocloud.msg.caidaoVerifyUrl.url:http://***********:20001/h5/approvalDetail}")
    private String caiDaoVerifyUrl;
    @Resource
    private DingTalkFeignClient dingTalkFeignClient;
    @Resource
    private IMessageLogRepository messageLogRepository;
    @Resource
    private OssService ossService;
    @Resource
    private CommonMsgPublish commonMsgPublish;
    @Resource
    private UserServiceFeignClent userServiceFeignClent;

    public boolean sendDingMessage(MsgDto msgDto) {
        replaceContent(msgDto);
        DingtalkMsgDto messageDto = msgDto.getDingTalkMsg();
        boolean sendStatus = true;
        log.info("api sendDingMessage body={}", FastjsonUtil.toJson(messageDto));
        if (messageDto == null) {
            return sendStatus;
        }
        Map<String, String> ssoToken = dingTalkFeignClient.getDingTalkToken(appKey, appSecret);
        String accessToken = ssoToken.get("access_token");

        Map params = new HashMap();
        //文本消息
        if (DingtalkTemplateTypeEnum.TEXT_MESSAGE.getIndex().equals(messageDto.getTemplateType())) {
            Map<String, String> map = Maps.newHashMap();
            map.put("content", messageDto.getContent());

            params.put("msgtype", "text");
            params.put("text", map);

        } else if (DingtalkTemplateTypeEnum.LINK_MESSAGE.getIndex().equals(messageDto.getTemplateType())) {
            //链接消息
            Map<String, String> map = Maps.newHashMap();

            map.put("messageUrl", messageDto.getUrl());
//            map.put("picUrl", getDingMediaId(accessToken, messageDto));
            map.put("picUrl", "@lALOACZwe2Rk");
            map.put("title", messageDto.getSubject());
            map.put("text", messageDto.getContent());

            params.put("msgtype", "link");
            params.put("link", map);
        } else if (DingtalkTemplateTypeEnum.MARKDOWN_MESSAGE.getIndex().equals(messageDto.getTemplateType())) {
            Map<String, String> map = Maps.newHashMap();
            map.put("title", messageDto.getSubject());
            String content = messageDto.getContent();
            log.info("send sendDingMessage before content={}", content);
            content = html2markdown(content);
            content = content.replaceAll("\\\\&\\\\&", "  \n  ");
            log.info("send sendDingMessage after content={}", content);
            map.put("text", content);

            params.put("msgtype", "markdown");
            params.put("markdown", map);
        } else if (DingtalkTemplateTypeEnum.ACTION_CARD_MESSAGE.getIndex().equals(messageDto.getTemplateType())) {
            Map<String, String> map = Maps.newHashMap();
            map.put("title", messageDto.getSubject());
            map.put("markdown", messageDto.getMarkdown());
            map.put("single_title", messageDto.getContent());
            map.put("single_url", messageDto.getUrl());

            params.put("msgtype", "action_card");
            params.put("action_card", map);
        }

        Map reqMap = new HashMap();
        List<String> userIds = Lists.newArrayList();
        if(null != messageDto.getEmpIds() && !messageDto.getEmpIds().isEmpty()){
            messageDto.getEmpIds().forEach(empId  -> {
                UserDetailInfoVo ud = getUserDetailByEmpId(empId);
                userIds.add(ud.getDingUserId());
            });
        }
        if(messageDto.getMobiles() != null){
            messageDto.getMobiles().forEach(mob  -> {
                if(null == mob || StringUtil.isBlank(mob.getValue())){
                    return;
                }
                userIds.add(getDingUserIdByPhone(mob.getValue(), accessToken));
            });
        }

        List<String> collect = userIds.stream().distinct().collect(Collectors.toList());
        if(null == collect || collect.isEmpty()){
            insertMsgLog(messageDto, SendStatusEnum.FAIL, "UnionId is Empty.");
            return false;
        }

        reqMap.put("agent_id", agentId);
        reqMap.put("userid_list", StringUtils.join(collect, ","));
        reqMap.put("msg", params);

        MessageLogDto messageLogDto = insertMsgLog(messageDto, SendStatusEnum.SUCESS);
        //钉钉工作通知
        try {
            log.info("start send sendDingMessage param, reqMap={}, accessToken={}", FastjsonUtil.toJson(reqMap), accessToken);
            Map map = dingTalkFeignClient.pushMsg(reqMap, accessToken);
            log.info("send sendDingMessage result={}", map);
            Integer code = (Integer) map.get("errcode");
            if (code == null || code != 200) {
                messageLogDto.setErrorMsg(map.toString());
                messageLogDto.setStatus(SendStatusEnum.FAIL);
                messageLogRepository.saveOrUpdateOfLog(messageLogDto);
            }
        } catch (Exception e) {
            log.error("sendDingMessage occur error, {} \n {}", e.getMessage(), e);
            messageLogDto.setErrorMsg(e.getMessage());
            messageLogDto.setStatus(SendStatusEnum.FAIL);
            messageLogRepository.saveOrUpdateOfLog(messageLogDto);
            sendStatus = false;
        }
        return sendStatus;
    }

    private MessageLogDto insertMsgLog(DingtalkMsgDto messageDto, SendStatusEnum status){
        return insertMsgLog(messageDto, status, null);
    }

    private MessageLogDto insertMsgLog(DingtalkMsgDto messageDto, SendStatusEnum status, String errMsg){
        MessageLogDto messageLogDto = new MessageLogDto();
        messageLogDto.setType(MsgType.DING_WORK_MESSAGE);
        if(null != messageDto.getEmpIds() && messageDto.getEmpIds().isEmpty()){
            // 多个手机号逗号分隔
            messageLogDto.setReceiver(messageDto.getEmpIds().stream().collect(Collectors.joining(",")));
        }
        messageLogDto.setMessage(FastjsonUtil.toJson(messageDto));
        messageLogDto.setStatus(status);
        messageLogDto.setErrorMsg(errMsg);
        String bid = messageLogRepository.saveOrUpdateOfLog(messageLogDto);
        messageLogDto.setBid(bid);
        return messageLogDto;
    }

    /**
     * 钉钉待办通知
     * @param messageDto
     */
    public boolean sendDingtalkToDoNoticeMessage(DingtalkToDoNoticeDto messageDto) {
        boolean sendStatus = true;
        log.info("api sendDingtalkToDoNoticeMessage body={}", FastjsonUtil.toJson(messageDto));
        if (messageDto == null) {
            return sendStatus;
        }
        Map<String, String> ssoToken = dingTalkFeignClient.getDingTalkToken(appKey, appSecret);
        String accessToken = ssoToken.get("access_token");

        MessageLogDto messageLogDto = new MessageLogDto();
        messageLogDto.setType(MsgType.DING_TODO_MESSAGE);
        messageLogDto.setReceiver((messageDto.getEmpIds().stream().collect(Collectors.joining(","))));
        messageLogDto.setMessage(FastjsonUtil.toJson(messageDto));
        messageLogDto.setStatus(SendStatusEnum.SUCESS);
        String bid = messageLogRepository.saveOrUpdateOfLog(messageLogDto);
        messageLogDto.setBid(bid);

        DingtalkToDoNoticeDto doNoticeDto = convertToNoticeDto(messageDto, accessToken);

        CreateTodoTaskResponse response;
        try {
            response = sendDingToDoNoticeMesage(doNoticeDto);
            if (response != null) {
                CreateTodoTaskResponseBody body = response.getBody();
                String thirdId = body.getId();
                //发送消息到workflow服务，进行才到流程任务和钉钉待办任务的绑定
                commonMsgPublish.sendDingTaskIdToWorkFlow(doNoticeDto, thirdId);
            } else {
                sendStatus = false;
            }
        } catch (Exception e) {
            log.error("sendDingToDoNoticeMesage occur error, {} \n {}", e.getMessage(), e);
            sendStatus = false;
        }
        return sendStatus;
    }

    /**
     * 待办任务更新成已完成
     * @param dingtalkToDoNoticeDto
     * @throws Exception
     */
    public void updateDingTaskStatus(DingtalkToDoNoticeDto dingtalkToDoNoticeDto) throws Exception {
        log.info("DingMessageNotifyApi updateDingTaskStatus dingtalkToDoNoticeDto = {}", FastjsonUtil.toJson(dingtalkToDoNoticeDto));
        com.aliyun.dingtalktodo_1_0.Client client = createClient();
        UpdateTodoTaskHeaders updateTodoTaskHeaders = new UpdateTodoTaskHeaders();
        Map<String, String> ssoToken = dingTalkFeignClient.getDingTalkToken(appKey, appSecret);
        String accessToken = ssoToken.get("access_token");
        //第三方id 钉钉待办任务id
        String dingTaskId = dingtalkToDoNoticeDto.getThirdId();
        String unionId = null;
        List<String> executorIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dingtalkToDoNoticeDto.getMobiles())) {
            //执行人
            dingtalkToDoNoticeDto.getMobiles().forEach(mob  -> {
                if(null == mob || StringUtils.isBlank(mob.getValue())){
                    return;
                }
                String executorId = getUnionId(mob.getValue(), accessToken);
                executorIds.add(executorId);
            });
        }
        if (CollectionUtils.isNotEmpty(dingtalkToDoNoticeDto.getEmpIds())) {
            //执行人
            dingtalkToDoNoticeDto.getEmpIds().forEach(empId  -> {
                String executorId = getUnionIdByEmpId(empId, accessToken);
                executorIds.add(executorId);
            });
        }

        List<String> collect = executorIds.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            unionId = collect.get(0);
        }
        updateTodoTaskHeaders.xAcsDingtalkAccessToken = accessToken;
        UpdateTodoTaskRequest updateTodoTaskRequest = new UpdateTodoTaskRequest()
                .setOperatorId(unionId)
//                .setExecutorIds(executorIds)
                //待办任务更新成已完成
                .setDone(true);
        UpdateTodoTaskResponse response = null;
        try {
            log.info("DingMessageNotifyApi request unionid= {}, dingTaskId = {},executorIds ={}, updateTodoTaskRequest = {}，updateTodoTaskHeaders ={}", unionId, dingTaskId, collect, FastjsonUtil.toJson(updateTodoTaskRequest), FastjsonUtil.toJson(updateTodoTaskHeaders));
            response = client.updateTodoTaskWithOptions(unionId, dingTaskId, updateTodoTaskRequest, updateTodoTaskHeaders, new RuntimeOptions());
            log.info("DingMessageNotifyApi UpdateTodoTaskResponse response= {}, dingTaskId ={}", FastjsonUtil.toJson(response), dingTaskId);
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                log.error("updateTodoTaskWithOptions occur error, {} \n {}", err.getMessage(), err);
            }

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                log.error("updateTodoTaskWithOptions occur error, {} \n {}", err.getMessage(), err);
            }
        }

    }

    /**
     * 删除待办任务
     * @param dingtalkToDoNoticeDto
     * @throws Exception
     */
    public void deleteDingTaskStatus(DingtalkToDoNoticeDto dingtalkToDoNoticeDto) throws Exception {
        log.info("DingMessageNotifyApi deleteDingTaskStatus dingtalkToDoNoticeDto = {}", FastjsonUtil.toJson(dingtalkToDoNoticeDto));
        com.aliyun.dingtalktodo_1_0.Client client = createClient();

        DeleteTodoTaskHeaders deleteTodoTaskHeaders = new DeleteTodoTaskHeaders();
        Map<String, String> ssoToken = dingTalkFeignClient.getDingTalkToken(appKey, appSecret);

        String accessToken = ssoToken.get("access_token");
        deleteTodoTaskHeaders.xAcsDingtalkAccessToken = accessToken;
        //第三方id 钉钉待办任务id
        String dingTaskId = dingtalkToDoNoticeDto.getThirdId();
        String unionId = null;
        List<String> executorIds = Lists.newArrayList();
        //手机号不能用，去掉
//        if (CollectionUtils.isNotEmpty(dingtalkToDoNoticeDto.getMobiles())) {
//            //执行人
//            dingtalkToDoNoticeDto.getMobiles().forEach(mob  -> {
//                String executorId = getUnionId(mob.getValue(), accessToken);
//                executorIds.add(executorId);
//            });
//        }
        if (CollectionUtils.isNotEmpty(dingtalkToDoNoticeDto.getEmpIds())) {
            //执行人
            dingtalkToDoNoticeDto.getEmpIds().forEach(empId  -> {
                String executorId = getUnionIdByEmpId(empId, accessToken);
                executorIds.add(executorId);
            });
        }

        if (CollectionUtils.isNotEmpty(executorIds)) {
            unionId = executorIds.get(0);
        }
        DeleteTodoTaskRequest deleteTodoTaskRequest = new DeleteTodoTaskRequest()
                .setOperatorId(unionId);
        DeleteTodoTaskResponse response = null;
        try {
            response = client.deleteTodoTaskWithOptions(unionId, dingTaskId, deleteTodoTaskRequest, deleteTodoTaskHeaders, new RuntimeOptions());
            log.info("DingMessageNotifyApi deleteTodoTaskResponse response= {}, dingTaskId ={}", FastjsonUtil.toJson(response), dingTaskId);
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("deleteTodoTaskWithOptions occur error, {} \n {}", err.getMessage(), err);
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("deleteTodoTaskWithOptions occur error, {} \n {}", err.getMessage(), err);
            }
        }
    }


    private DingtalkToDoNoticeDto convertToNoticeDto(DingtalkToDoNoticeDto messageDto, String accessToken) {
        DingtalkToDoNoticeDto doNoticeDto = new DingtalkToDoNoticeDto();
        doNoticeDto.setSubject(messageDto.getSubject());
        List<String> executorIds = Lists.newArrayList();
        messageDto.getEmpIds().forEach(empId  -> {
            String unionId = getUnionIdByEmpId(empId, accessToken);
            executorIds.add(unionId);
        });

        if (CollectionUtils.isNotEmpty(executorIds)) {
            doNoticeDto.setCreatorId(executorIds.get(0));
        }
        doNoticeDto.setExecutorIds(executorIds);
        doNoticeDto.setDueTime(messageDto.getDueTime());
        DingtalkToDoNoticeDto.DetailUrl detailUrl = new DingtalkToDoNoticeDto.DetailUrl();
        String url = caiDaoVerifyUrl + "?businessKey=" + messageDto.getBusinessKey() + "&nodeType=APPROVAL&taskId=" +  messageDto.getTaskId();
        log.info("convertToNoticeDto url= {} ", url);
        detailUrl.setAppUrl(url);
        detailUrl.setPcUrl(url);
        doNoticeDto.setDetailurl(detailUrl);
        doNoticeDto.setBusinessKey(messageDto.getBusinessKey());
        doNoticeDto.setTaskId(messageDto.getTaskId());
        doNoticeDto.setDingCaidaoId(messageDto.getDingCaidaoId());
        String description = messageDto.getDescription();
        if(StringUtil.isNotEmpty(description)){
            description = description
                .replaceAll("#businessKey#", messageDto.getBusinessKey())
                .replaceAll("#taskId#", messageDto.getTaskId());
        }
        doNoticeDto.setDescription(description);
        return doNoticeDto;
    }


    private String getUnionId(String mobile, String accessToken) {
        mobile = mobile.indexOf("/") > 0 ? mobile.substring(0, mobile.indexOf("/")) : mobile;
        Map map = dingTalkFeignClient.getUserId(mobile, accessToken);
        log.info("getUnionId getUserId result={}", FastjsonUtil.toJson(map));
        String  userId = null;
        if (map != null && "ok".equals(map.get("errmsg"))) {
            Map result = (Map) map.get("result");
            userId = (String) result.get("userid");
        }
        String unionId = null;
        if (StringUtils.isNotEmpty(userId)) {
            Map unionIdMap = dingTalkFeignClient.getUnionId(userId, accessToken);
            if (unionIdMap != null && "ok".equals(unionIdMap.get("errmsg"))) {
                Map result = (Map) unionIdMap.get("result");
                unionId = (String) result.get("unionid");
            }
        }
        return unionId;
    }

    private String getUnionIdByEmpId(String empId, String accessToken) {
        return getUnionIdByEmpId(empId, accessToken, new DingtalkToDoNoticeDto());
    }

    private String getUnionIdByEmpId(String empId, String accessToken, DingtalkToDoNoticeDto messageDto) {
        Result<UserDetailInfoVo> user = userServiceFeignClent.getUserByEmpId(Long.valueOf(empId));
        String unionId = null;
        if (user != null && user.getData() != null && StringUtils.isNotEmpty(user.getData().getDingUserId())) {
            messageDto.addMobile(user.getData().getMobile());
            Map unionIdMap = dingTalkFeignClient.getUnionId(user.getData().getDingUserId(), accessToken);
            if (unionIdMap != null && "ok".equals(unionIdMap.get("errmsg"))) {
                Map result = (Map) unionIdMap.get("result");
                unionId = (String) result.get("unionid");
            }
        }
        return unionId;
    }

    private String getUserIdByEmpId(String empId) {
        return getUserDetailByEmpId(empId).getDingUserId();
    }

    private UserDetailInfoVo getUserDetailByEmpId(String empId) {
        Result<UserDetailInfoVo> user = userServiceFeignClent.getUserByEmpId(Long.valueOf(empId));
        if (user != null && user.getData() != null && StringUtils.isNotEmpty(user.getData().getDingUserId())) {
            return user.getData();
        }
        return new UserDetailInfoVo();
    }

    private String getDingUserIdByPhone(String mobile, String accessToken) {
        mobile = mobile.indexOf("/") > 0 ? mobile.substring(0, mobile.indexOf("/")) : mobile;
        Map map = dingTalkFeignClient.getUserId(mobile, accessToken);
        if (map != null && "ok".equals(map.get("errmsg"))) {
            Map result = (Map) map.get("result");
            return (String) result.get("userid");
        }
        log.error("getDingUserIdByPhone occur error, mobile: {} accessToken: {}", mobile, accessToken);
        return "";
    }

    private String getDingMediaId(String accessToken, DingtalkMsgDto messageDto) {
        DingTalkClient client = new DefaultDingTalkClient(dingServiceUrl);
        OapiMediaUploadRequest req = new OapiMediaUploadRequest();
        req.setType("image");

        InputStream inputStream = ossService.getInputStream(messageDto.getAffix());
        byte[] bytes = FileUtils.inputStreamToByteArray(inputStream);

        // 要上传的媒体文件
        FileItem item = new FileItem(messageDto.getAffixName(), bytes);
        req.setMedia(item);

        OapiMediaUploadResponse response = null;
        try {
            response = client.execute(req, accessToken);
            if (response != null && "ok".equals(response.getErrmsg())) {
                return response.getMediaId();
            }
        } catch (ApiException e) {
            log.error("getDingMediaId occur error, {} \n {}", e.getMessage(), e);
        }
        return "";
    }

    public com.aliyun.dingtalktodo_1_0.Client createClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalktodo_1_0.Client(config);
    }

    public CreateTodoTaskResponse sendDingToDoNoticeMesage(DingtalkToDoNoticeDto doNoticeDto) throws Exception {
        log.info("DingMessageNotifyApi sendDingToDoNoticeMesage doNoticeDto = {}", FastjsonUtil.toJson(doNoticeDto));
        com.aliyun.dingtalktodo_1_0.Client client = createClient();
        CreateTodoTaskHeaders createTodoTaskHeaders = new CreateTodoTaskHeaders();

        Map<String, String> ssoToken = dingTalkFeignClient.getDingTalkToken(appKey, appSecret);

        String accessToken = ssoToken.get("access_token");
        createTodoTaskHeaders.xAcsDingtalkAccessToken = accessToken;

        //DING通知配置，目前仅支持取值为1，表示应用内DING
        CreateTodoTaskRequest.CreateTodoTaskRequestNotifyConfigs notifyConfigs = new CreateTodoTaskRequest.CreateTodoTaskRequestNotifyConfigs()
                .setDingNotify("1");
        CreateTodoTaskRequest.CreateTodoTaskRequestContentFieldList contentFieldList0 = new CreateTodoTaskRequest.CreateTodoTaskRequestContentFieldList();

        CreateTodoTaskRequest.CreateTodoTaskRequestDetailUrl detailUrl = new CreateTodoTaskRequest.CreateTodoTaskRequestDetailUrl()
                .setAppUrl(doNoticeDto.getDetailurl() != null ? doNoticeDto.getDetailurl().getAppUrl() : "")
                .setPcUrl(doNoticeDto.getDetailurl() != null ? doNoticeDto.getDetailurl().getPcUrl() : "");

        CreateTodoTaskRequest createTodoTaskRequest = new CreateTodoTaskRequest()
                .setOperatorId(doNoticeDto.getCreatorId())
//                .setSourceId(String.valueOf(System.currentTimeMillis()))
                .setSubject(doNoticeDto.getSubject())
                .setCreatorId(doNoticeDto.getCreatorId())
                .setDescription(doNoticeDto.getDescription())
                .setDueTime(doNoticeDto.getDueTime())
                .setExecutorIds(doNoticeDto.getExecutorIds())
                .setParticipantIds(doNoticeDto.getParticipantIds())
                .setDetailUrl(detailUrl)
                .setContentFieldList(Lists.newArrayList(contentFieldList0))
//                .setIsOnlyShowExecutor(true)
//                .setPriority(20)
                .setNotifyConfigs(notifyConfigs);

        CreateTodoTaskResponse todoTaskWithOptions = null;
        try {
            log.info("DingMessageNotifyApi sendDingToDoNoticeMesage request unionid= {}, createTodoTaskRequest = {}，createTodoTaskHeaders ={}", doNoticeDto.getCreatorId(), FastjsonUtil.toJson(createTodoTaskRequest), FastjsonUtil.toJson(createTodoTaskHeaders));
            todoTaskWithOptions = client.createTodoTaskWithOptions(doNoticeDto.getCreatorId(), createTodoTaskRequest, createTodoTaskHeaders, new RuntimeOptions());
            log.info("DingMessageNotifyApi sendDingToDoNoticeMesage todoTaskWithOptions = {}", FastjsonUtil.toJson(todoTaskWithOptions));
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("createTodoTaskWithOptions occur error, {} \n {}", err.getMessage(), err);
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("createTodoTaskWithOptions occur error, {} \n {}", err.getMessage(), err);
            }
        }
        //回传ID给workflow
        return todoTaskWithOptions;
    }

    private String html2markdown(String htmlInput){
        FlexmarkHtmlConverter converter = FlexmarkHtmlConverter.builder().build();
        return converter.convert(htmlInput);
    }

    private void replaceContent(MsgDto msgDto){
        DingtalkToDoNoticeDto dtn = msgDto.getDingtalkToDoNoticeDto();
        if(null == dtn || StringUtil.isEmpty(dtn.getBusinessKey())){
            return;
        }
        DingtalkMsgDto dm = msgDto.getDingTalkMsg();
        if(null == dm || StringUtil.isEmpty(dm.getContent())){
            return;
        }

        dm.setContent(dm.getContent().replaceAll("#businessKey#", dtn.getBusinessKey())
                .replaceAll("#taskId#", dtn.getTaskId()));
    }

}

 
    
    
    
    