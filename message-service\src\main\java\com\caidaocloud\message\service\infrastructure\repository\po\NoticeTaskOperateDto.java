package com.caidaocloud.message.service.infrastructure.repository.po;

import lombok.Data;

/**
 * created by: FoAng
 * create time: 27/12/2024 10:41 上午
 */
@Data
public class NoticeTaskOperateDto {

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 任务执行ID
     */
    private String taskId;

    /**
     * 节点ID
     */
    private String nodeId;

    /**
     * 结束事件
     */
    private boolean endEvent;

    /**
     * 流程id
     */
    private String procInstId;

    /**
     * 流程定义
     */
    private Long defId;

    /**
     * 事件任务ID
     */
    private String businessKey;

    /**
     * 审批动作
     */
    private String choice;

    /**
     * 移交事件
     */
    private TransferNoticeEvent transferNoticeEvent;


    @Data
    public static class TransferNoticeEvent {

        /**
         *	转交前审批人empId
         */
        private String transferFrom;

        /**
         *	转交后审批人empId
         */
        private String transferTo;


        public TransferNoticeEvent(String transferFrom, String transferTo) {
            this.transferFrom = transferFrom;
            this.transferTo = transferTo;
        }
    }

}
