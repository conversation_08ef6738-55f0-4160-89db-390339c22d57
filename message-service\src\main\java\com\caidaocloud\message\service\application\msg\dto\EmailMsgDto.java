package com.caidaocloud.message.service.application.msg.dto;

import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import lombok.Data;

/**
 * EmailMsgDto
 *
 * <AUTHOR>
 * @date 2022/6/9 下午2:45
 */
@Data
public class EmailMsgDto {
    // 主题，标题
    private String subject;

    // 内容
    private String content;

    // 抄送
    private String cc;

    // 抄送
    private String bcc;

    // 收件人
    private String to;

    /**
     * 多个附件用逗号分割
     * 附件地址
     */
    private String affix;

    /**
     * 指定附件对应的名称，多个用逗号分割同affix一一对应，如果为空或对应下标上的值为空，则取文件名作为名称
     * 附件名称
     */
    private String affixName;

    /**
     * 发送状态
     */
    private SendStatusEnum status = SendStatusEnum.SUCESS;
}
