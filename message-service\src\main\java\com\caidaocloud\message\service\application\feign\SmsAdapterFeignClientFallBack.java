package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.message.service.application.sms.dto.SmsMessageDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

@Component
public class SmsAdapterFeignClientFallBack implements SmsAdapterFeignClient{
    @Override
    public Result<String> sendMesage(SmsMessageDto messageDto) {
        return Result.fail("fail");
    }
}
