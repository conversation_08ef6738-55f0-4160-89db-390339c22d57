package com.caidaocloud.message.service.infrastructure.config.annotation;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.message.service.application.common.constant.LangCodeConstant;
import com.caidaocloud.message.service.infrastructure.repository.impl.OpLogRepositoryImpl;
import com.caidaocloud.message.service.infrastructure.repository.po.LogPo;
import com.caidaocloud.message.service.infrastructure.utils.LangUtil;
import com.caidaocloud.security.dto.RestUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.RestUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Optional;
import java.util.UUID;

@Slf4j
// @Aspect
// @Configuration
@Deprecated
public class OperationLogAspect {
    @Resource
    private ISessionService sessionService;
    @Resource
    private OpLogRepositoryImpl opLogRepository;
    
    @Pointcut("@annotation(io.swagger.annotations.ApiOperation)")
    public void pointcut() {}

    @Around("pointcut()")
    public Object around(ProceedingJoinPoint point) {
        Object result = null;
        long beginTime = System.currentTimeMillis();
        boolean success = true;
        try {
            // 执行方法
            result = point.proceed();
        } catch (ServerException e){
            // 保存操作日志
            // saveLog(point, System.currentTimeMillis() - beginTime, result, beginTime, false);
            throw e;
        } catch (Throwable e) {
            log.error("Failed to execute request,{}", e.getMessage(), e);
            success = false;
        }

        // 执行时长(毫秒)
        long time = System.currentTimeMillis() - beginTime;
        // 保存操作日志
        // saveLog(point, time, result, beginTime, success);
        if(!success){
            throw new ServerException(LangUtil.getMsg(LangCodeConstant.code_70006));
        }
        return result;
    }

    private void saveLog(ProceedingJoinPoint joinPoint, long time, Object result, long createTime, boolean success) {
        Class<?> cla = joinPoint.getTarget().getClass();
        RequestMapping rm = cla.getAnnotation(RequestMapping.class);
        String [] value = rm.value();
        Api annotation = cla.getAnnotation(Api.class);
        String moduleName = annotation.value();
        if (moduleName == null) {
            moduleName = annotation.description();
        }

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        ApiOperation logAnnotation = method.getAnnotation(ApiOperation.class);
        if (logAnnotation == null) {
            return;
        }

        LogPo logPo = new LogPo();
        logPo.setSuccess(success);
        logPo.setModel(moduleName);
        logPo.setUrl(value[0]);
        // 注解上的描述
        logPo.setMenu(logAnnotation.value());
        // 请求的方法名
        logPo.setClassName(cla.getName());
        logPo.setMethodName(signature.getName());
        String mdc = MDC.get("goalMdcId");
        mdc = StringUtil.isEmpty(mdc) ? UUID.randomUUID().toString() : mdc;
        MDC.put("goalMdcId", mdc);
        logPo.setMdc(mdc);

        // 请求的方法参数值
        Object [] args = joinPoint.getArgs();
        // 请求的方法参数名称
        LocalVariableTableParameterNameDiscoverer localParameter = new LocalVariableTableParameterNameDiscoverer();
        String [] paramNames = localParameter.getParameterNames(method);
        // 拼接请求参数
        if (args != null && paramNames != null) {
            StringBuilder params = new StringBuilder("");
            for (int i = 0; i < args.length; i++) {
                params.append(paramNames[i]);
                params.append("=");
                params.append(args[i]);
                params.append(";");
            }
            logPo.setParams(params.toString());
        }

        logPo.setResult(FastjsonUtil.toJson(result));
        // 从获取RequestAttributes中获取HttpServletRequest的信息
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        logPo.setUrl(request.getRequestURI());
        // 从获取RequestAttributes中获取HttpServletResponse的信息
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();

        logPo.setStatus(response.getStatus());
        logPo.setType("");
        logPo.setTime(time);

        UserInfo userInfo = sessionService.getUserInfo();
        if(userInfo == null){
            Optional<RestUserInfo> requestUser = RestUtil.getRequestUser();
            if(requestUser.isPresent()){
                userInfo = new UserInfo();
                Long uid = ((RestUserInfo) requestUser.get()).getUserId();
                Long empId = ((RestUserInfo) requestUser.get()).getEmpId();
                Long tenantId = ((RestUserInfo) requestUser.get()).getTenantId();
                userInfo.setUserid(uid.intValue());
                userInfo.setCorpid(null);
                userInfo.setEmpid(empId.intValue());
                userInfo.setTenantId(null == tenantId? null : String.valueOf(tenantId));
                userInfo.setCreateBy(tenantId);
            }
        }
        logPo.setCreateBy(null != userInfo.getUserId() ? userInfo.getUserId().toString() : "");
        logPo.setCreateTime(createTime);
        logPo.setUpdateBy(logPo.getCreateBy());
        logPo.setUpdateTime(logPo.getCreateTime());
        logPo.setTenantId(userInfo.getTenantId());
        opLogRepository.insert(logPo);
    }
}
