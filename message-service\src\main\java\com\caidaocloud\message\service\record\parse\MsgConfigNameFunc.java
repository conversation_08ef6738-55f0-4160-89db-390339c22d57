package com.caidaocloud.message.service.record.parse;

import com.caidaocloud.message.service.application.msg.service.MsgConfigService;
import com.caidaocloud.message.service.interfaces.vo.msg.MsgConfigDetailVo;
import com.caidaocloud.record.core.service.IParseFunction;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * created by: FoAng
 * create time: 26/7/2024 4:05 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class MsgConfigNameFunc implements IParseFunction {

    private MsgConfigService msgConfigService;

    @Override
    public String functionName() {
        return "msgName";
    }

    @Override
    public boolean executeBefore() {
        return true;
    }

    @Override
    public String apply(String msgConfigId) {
        return Optional.ofNullable(msgConfigId).map(it -> {
            MsgConfigDetailVo detailVo = msgConfigService.getDetail(it);
            return detailVo == null ? null : detailVo.getName();
        }).orElse("");
    }
}
