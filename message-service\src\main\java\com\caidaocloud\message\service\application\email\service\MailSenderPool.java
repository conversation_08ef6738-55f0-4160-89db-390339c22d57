package com.caidaocloud.message.service.application.email.service;

import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import org.springframework.mail.javamail.JavaMailSender;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.concurrent.ConcurrentMap;

public class MailSenderPool {
    private static ConcurrentMap<String, MailSenderPooledObjectFactory> poolManager = Maps.newConcurrentMap();

    public static JavaMailSender borrowObj(String smtpHost, String port, String user, String password, String socketFactoryClass) throws Exception {
        // 修复密码变更，配置无法更新问题
        MailSenderPooledObjectFactory factory = poolManager.computeIfAbsent(String.format("%s-%s-%s-%s", smtpHost, user, md5(password), port),
            (v)->{
                MailSenderPooledObjectFactory fac = new MailSenderPooledObjectFactory();
                fac.setHost(smtpHost);
                fac.setPort(port);
                fac.setUser(user);
                fac.setPassword(password);
                if(null != socketFactoryClass){
                    fac.setSocketFactoryClass(socketFactoryClass);
                }
                return fac;
            });
        return factory.borrowObject();
    }

    @SneakyThrows
    public static String md5(String data) {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] md5 = md.digest(data.getBytes(StandardCharsets.UTF_8));

        // 将处理后的字节转成 16 进制，得到最终 32 个字符
        StringBuilder sb = new StringBuilder();
        for (byte b : md5) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    public static void returnObj(String smtpHost, JavaMailSender transport, String user, String password, String port) throws Exception {
        MailSenderPooledObjectFactory factory = poolManager.get(String.format("%s-%s-%s-%s", smtpHost, user, md5(password), port));
        if(null == factory){
            return;
        }
        factory.returnObject(transport);
    }
}
