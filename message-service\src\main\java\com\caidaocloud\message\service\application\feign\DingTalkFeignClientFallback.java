package com.caidaocloud.message.service.application.feign;

import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/2/6 上午10:40
 * @Version 1.0
 **/
@Component
public class DingTalkFeignClientFallback implements DingTalkFeignClient {

    @Override
    public Map<String, String> getDingTalkToken(String appKey, String appSecret) {
        return new HashMap<>();
    }

    @Override
    public Map pushMsg(Map reqMap, String accessToken) {
        return new HashMap<>();
    }

    @Override
    public Map getUserId(String mobile, String accessToken) {
        return new HashMap<>();
    }



    @Override
    public Map getUnionId(String userid, String accessToken) {
        return new HashMap<>();
    }

//    @Override
//    public Map uploadMedia(MultipartFile media, String type, String accessToken) {
//        return new HashMap<>();
//    }


}

 
    
    
    
    