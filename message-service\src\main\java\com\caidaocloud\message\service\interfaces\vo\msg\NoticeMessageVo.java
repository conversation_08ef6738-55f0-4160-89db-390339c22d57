package com.caidaocloud.message.service.interfaces.vo.msg;

import com.caidaocloud.message.sdk.enums.ContentTypeEnum;
import com.caidaocloud.message.sdk.enums.NoticeActionEnum;
import com.caidaocloud.message.sdk.enums.NoticeBusinessEnum;
import com.caidaocloud.message.sdk.enums.NoticeMsgTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * created by: FoAng
 * create time: 16/8/2024 5:52 下午
 */
@Data
public class NoticeMessageVo implements Serializable {

    @ApiModelProperty("消息主键")
    private String bid;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("摘要汇总")
    private String summary;

    @ApiModelProperty("文本类型：plain text, rich")
    private ContentTypeEnum contentType;

    @ApiModelProperty("类型")
    private NoticeMsgTypeEnum type;

    @ApiModelProperty("业务类型")
    private NoticeBusinessEnum businessType;

    @ApiModelProperty("标记已读")
    private Boolean markRead;

    @ApiModelProperty("操作类型")
    private NoticeActionEnum action;

    @ApiModelProperty("操作内容")
    private String actionContent;

    @ApiModelProperty("创建时间")
    private long createTime;

}
