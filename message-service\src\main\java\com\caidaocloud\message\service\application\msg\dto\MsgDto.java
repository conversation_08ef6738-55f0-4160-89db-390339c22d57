package com.caidaocloud.message.service.application.msg.dto;

import com.caidaocloud.message.service.application.app.dto.AppMsgSender;
import com.caidaocloud.message.service.application.sms.dto.SmsMessageDto;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * MstDto
 * <AUTHOR>
 * @date 2022/6/9 下午2:44
 */
@Data
public class MsgDto {

    private String tenantId;

    private Long userId;

    /**
     * 批量消息的批次id
     */
    private String mdc;

    /**
     * 发送途径：1,2,3,4
     * 4: 钉钉通知
     * 3：app 通知
     * 2：系统通知
     * 0：短信通知
     * 1：邮件通知
     * <p>
     * 多个发送途径，英文逗号分割
     */
    private List<String> channel;

    /**
     * 邮件消息
     */
    private EmailMsgDto emailMsg;

    /**
     * 短信消息
     */
    private SmsMessageDto message;

    /**
     * 系统消息，对应channel为2
     */
    private NoticeMessageDto noticeMsg;

    /**
     * app消息
     */
    private List<AppMsgSender> appMsg;

    /**
     * 钉钉消息
     */
    private DingtalkMsgDto dingTalkMsg;

    /**
     * 钉钉待办通知消息
     */
    private DingtalkToDoNoticeDto dingtalkToDoNoticeDto;


    /**
     * 业务数据ID
     */
    private String bid;

    /**
     * 消息来源
     * 用于追踪消息来源于某个业务或模块
     */
    private String msgFrom;

    /**
     * 消息设置
     */
    private String msgConfig;

    /**
     * 更改钉钉待办任务状态的字段, 1:代表去message服务更新任务状态
     */
    private String dingStatus;

    /**
     * 自定义参数
     */
    private Map<String, Object> ext;

}
