package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.message.service.application.feign.dto.WorkflowApproverSetting;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/3/21 下午5:45
 * @Version 1.0
 **/
@FeignClient(value = "caidaocloud-hr-service",
        fallback = WfApproverFeignFallback.class,
        configuration = FeignConfiguration.class)
public interface IWfApproverFeign {

    @GetMapping("/api/hr/v1/workflow/approver/setting/list")
    Result<List<WorkflowApproverSetting>> listSettings();

    @GetMapping("/api/hr/emp/work/v1/detail")
    Result<EmpWorkInfoVo> getEmpWorkInfo(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime);

    @GetMapping("/api/hr/v1/workflow/approver/setting")
    public Result<WorkflowApproverSetting> loadSetting(@RequestParam String bid);

    @GetMapping("/api/hr/v1/workflow/approver/transfer")
    public Result<String> getTransferApprovers(@RequestParam("applicantId") String applicantId,
                                               @RequestParam("code") String code, @RequestParam("businessKey") String businessKey);

    @GetMapping("/api/hr/v1/workflow/approver/transferBefore")
    public Result<String> getTransferBeforeApprovers(@RequestParam("applicantId") String applicantId,
                                               @RequestParam("code") String code, @RequestParam("businessKey") String businessKey);

}
