package com.caidaocloud.message.service.domain.notice.sse;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.util.FastjsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.lang.ref.WeakReference;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * created by: FoAng
 * create time: 15/8/2024 3:24 下午
 */
@Slf4j
@Component
@EnableScheduling
@AllArgsConstructor
public class SseEmitterFactory {

    private static final String CACHE_KEY_USER_CLIENT = "sse_user_%s_%s";

    private static final String CLIENT_FORMAT_KEY = "%s_%s";

    private static final String CLIENT_KEY_PREFIX = "sse_client_%s_%s";

    private static final ConcurrentHashMap<String, WeakReference<ConcurrentHashMap<String, SseEmitter>>> tenantClients = new ConcurrentHashMap<>();

    private CacheService cacheService;

    public static String getSseClientIdPrefix(String tenantId, Long empId) {
        return String.format(CLIENT_KEY_PREFIX, tenantId, empId);
    }

    public static String getSseClientId(String tenantId, Long empId, String clientId) {
        return String.format(CLIENT_FORMAT_KEY, getSseClientIdPrefix(tenantId, empId), clientId);
    }

    public static String getUserSseCacheKey(String tenantId, Long userId) {
        return String.format(CACHE_KEY_USER_CLIENT, tenantId, userId);
    }

    /**
     * 设备注册
     * @param clientId
     * @return
     */
    public SseEmitter register(String tenantId, Long userId, Long empId, String clientId) {
        String clientKey = getSseClientId(tenantId, empId, clientId);
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        putSseEmitter(tenantId, clientKey, emitter);
        emitter.onCompletion(() -> removeSseEmitter(tenantId, clientKey));
        emitter.onTimeout(() -> removeSseEmitter(tenantId, clientKey));
        cacheService.cacheSet(getUserSseCacheKey(tenantId, userId), clientKey);
        return emitter;
    }

    public void putSseEmitter(String tenantId, String clientId, SseEmitter sseEmitter) {
        tenantClients.compute(tenantId, (k, v) -> {
            ConcurrentHashMap<String, SseEmitter> map = (v != null) ? v.get() : null;
            if (map == null) {
                map = new ConcurrentHashMap<>();
                map.put(clientId, sseEmitter);
                return new WeakReference<>(map);
            } else {
                map.put(clientId, sseEmitter);
                return v;
            }
        });
    }

    public SseEmitter getSseEmitter(String tenantId, String clientId) {
        WeakReference<ConcurrentHashMap<String, SseEmitter>> ref = tenantClients.get(tenantId);
        if (ref != null) {
            ConcurrentHashMap<String, SseEmitter> map = ref.get();
            if (map != null) {
                return map.get(clientId);
            }
        }
        return null;
    }

    public ConcurrentHashMap<String, SseEmitter> getTenantClients(String tenantId) {
        WeakReference<ConcurrentHashMap<String, SseEmitter>> ref = tenantClients.get(tenantId);
        if (ref != null) {
            ConcurrentHashMap<String, SseEmitter> map = ref.get();
            return Optional.ofNullable(map).orElse(new ConcurrentHashMap<>());
        }
        return new ConcurrentHashMap<>();
    }

    public void removeSseEmitter(String tenantId, String clientId) {
        WeakReference<ConcurrentHashMap<String, SseEmitter>> ref = tenantClients.get(tenantId);
        if (ref != null) {
            ConcurrentHashMap<String, SseEmitter> map = ref.get();
            if (map != null) {
                log.info("[sse] remove sse clientId: {}", clientId);
                map.remove(clientId);
            }
        }
    }

    /**
     * 设备发送消息
     * @param clientId
     * @param message
     */
    public boolean sendMessage(String tenantId, String clientId, Object message) {
        SseEmitter emitter = getSseEmitter(tenantId, clientId);
        return Optional.ofNullable(emitter).map(it -> {
            try {
                emitter.send(SseEmitter.event().data(FastjsonUtil.toJson(message)));
                return true;
            } catch (IOException e) {
                log.info("[sse] send message error, {}", e.getMessage());
                removeSseEmitter(tenantId, clientId);
                return false;
            }
        }).orElseGet(() -> {
            log.info("[sse] client is not present, clientId:{}", clientId);
            return false;
        });
    }


    /**
     * 系统广播通知
     * @param message
     */
    public void broadcastMessage(String tenantId, String message) {
        getTenantClients(tenantId).forEach((clientId, emitter) -> {
            try {
                emitter.send(SseEmitter.event().data(message));
            } catch (IOException e) {
                removeSseEmitter(tenantId, clientId);
            }
        });
    }

    /**
     * 定时移除不活跃链接
     */
    @Scheduled(fixedRate = 60000)
    public void cleanInactiveClients() {
        tenantClients.forEach((tenantId, rel) -> {
            cleanTenantClients(getTenantClients(tenantId));
        });
    }

    public void cleanTenantClients(ConcurrentMap<String, SseEmitter> clients) {
        clients.forEach((clientId, emitter) -> {
            try {
                emitter.send(SseEmitter.event().comment("ping"));
            } catch (IOException e) {
                clients.remove(clientId);
            }
        });
    }
}
