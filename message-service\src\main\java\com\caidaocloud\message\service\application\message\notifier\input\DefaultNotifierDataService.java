package com.caidaocloud.message.service.application.message.notifier.input;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.hr.service.dto.EmpDataQueryDto;
import com.caidaocloud.hr.service.enums.EmpDataQueryTypeEnum;
import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.hr.service.vo.EmpPrivateInfoVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.message.sdk.dto.PreEmpDataVo;
import com.caidaocloud.message.sdk.dto.PreEmpEntryProcessVo;
import com.caidaocloud.message.service.application.feign.AuthServiceFeignClient;
import com.caidaocloud.message.service.application.feign.HrFeignClient;
import com.caidaocloud.message.service.application.feign.OnBoardingFeignClient;
import com.caidaocloud.message.service.application.feign.UserServiceFeignClent;
import com.caidaocloud.message.service.application.feign.dto.UserDetailInfoVo;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDataDto;
import com.caidaocloud.message.service.application.message.notifier.dto.RoleUserInfoDto;
import com.caidaocloud.message.service.application.message.notifier.dto.RoleUserQueryDto;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class DefaultNotifierDataService implements INotifierDataService {
    @Resource
    private HrFeignClient hrFeignClient;
    @Resource
    private AuthServiceFeignClient authServiceFeignClient;
    @Resource
    private OnBoardingFeignClient onBoardingFeignClient;
    @Resource
    private UserServiceFeignClent userServiceFeignClent;

    private List<EmpWorkInfoVo> getEmpDataList(EmpDataQueryDto empDataQuery) {
        Result<List<EmpDataVo>> empInfos = hrFeignClient.getEmpInfos(empDataQuery);
        List<EmpDataVo> dataList = null;
        if (null == empInfos || !empInfos.isSuccess()
                || null == (dataList = empInfos.getData()) || dataList.isEmpty()) {
            return Lists.newArrayList();
        }

        List<EmpWorkInfoVo> empList = new ArrayList<>(dataList.size());
        dataList.forEach(empDataDto -> {
            EmpWorkInfoVo empWorkInfo = empDataDto.getEmpWorkInfo();
            empList.add(empWorkInfo);
        });

        return empList;
    }

    private List<EmpPrivateInfoVo> getEmpPrivateDataList(EmpDataQueryDto empDataQuery) {
        List<EmpDataVo> dataList = null;
        Result<List<EmpDataVo>> empInfos = hrFeignClient.getEmpInfos(empDataQuery);
        if (null == empInfos || !empInfos.isSuccess()
                || null == (dataList = empInfos.getData()) || dataList.isEmpty()) {
            return Lists.newArrayList();
        }

        List<EmpPrivateInfoVo> empList = new ArrayList<>(dataList.size());
        dataList.forEach(empDataDto -> {
            EmpPrivateInfoVo empWorkInfo = empDataDto.getEmpPrivateInfo();
            empList.add(empWorkInfo);
        });

        return empList;
    }

    @Override
    public List<RoleUserInfoDto> getUserInfoByRoles(List<String> roleIds) {
        List<RoleUserInfoDto> dataList = new ArrayList<>(200);
        if (null == roleIds || roleIds.isEmpty()) {
            return dataList;
        }

        RoleUserQueryDto query = new RoleUserQueryDto();
        query.setRoleIds(roleIds);
        QueryPageBean queryPage = new QueryPageBean();
        queryPage.setPageNo(1);
        queryPage.setPageSize(5000);
        // 角色集合下超过 5000 的暂不处理
        query.setQueryPageBean(queryPage);

        Result<PageResult<RoleUserInfoDto>> result = authServiceFeignClient.getUserInfoByRoles(query);
        PageResult<RoleUserInfoDto> pageResult = null;
        List<RoleUserInfoDto> itemList = null;
        if (null == result || !result.isSuccess() || null == (pageResult = result.getData())
                || null == (itemList = pageResult.getItems()) || itemList.isEmpty()) {
            return dataList;
        }

        dataList.addAll(itemList);
        return dataList;
    }

    @Override
    public List<EmpDataVo> getRecruiterByEmpIds(List<String> empIds, Long dataTime) {
        EmpDataQueryDto empDataQuery = new EmpDataQueryDto();
        empDataQuery.setDateTime(dataTime);
        empDataQuery.setEmpIds(empIds);
        empDataQuery.setTypeEnum(EmpDataQueryTypeEnum.ORGANIZATIONAL_ROLE);
        empDataQuery.setRoleCode("recruiters");
        return getEmpDataListByQuery(empDataQuery);
    }

    @Override
    public List<EmpDataVo> getOrgLeadersByEmpIds(List<String> empIds, Long dataTime) {
        EmpDataQueryDto empDataQuery = new EmpDataQueryDto();
        empDataQuery.setDateTime(dataTime);
        empDataQuery.setEmpIds(empIds);
        empDataQuery.setTypeEnum(EmpDataQueryTypeEnum.ORG_LEADER_EMP);
        return getEmpDataListByQuery(empDataQuery);
    }

    @Override
    public List<EmpWorkInfoVo> getEmpDataInfoByEmpIds(List<String> empIds, Long dataTime) {
        EmpDataQueryDto empDataQuery = new EmpDataQueryDto();
        empDataQuery.setDateTime(dataTime);
        empDataQuery.setEmpIds(empIds);
        empDataQuery.setTypeEnum(EmpDataQueryTypeEnum.EMP);
        return getEmpDataList(empDataQuery);
    }

    @Override
    public List<EmpPrivateInfoVo> getEmpGuardianByEmpIds(List<String> empIds, Long dataTime) {
        EmpDataQueryDto empDataQuery = new EmpDataQueryDto();
        empDataQuery.setDateTime(dataTime);
        empDataQuery.setEmpIds(empIds);
        empDataQuery.setTypeEnum(EmpDataQueryTypeEnum.EMP);
        return getEmpPrivateDataList(empDataQuery);
    }

    @Override
    public List<EmpDataVo> getEmpDataByEmpIds(List<String> empIds, Long dataTime) {
        EmpDataQueryDto empDataQuery = new EmpDataQueryDto();
        empDataQuery.setDateTime(dataTime);
        empDataQuery.setEmpIds(empIds);
        empDataQuery.setTypeEnum(EmpDataQueryTypeEnum.EMP);
        Result<List<EmpDataVo>> empInfos = hrFeignClient.getEmpInfos(empDataQuery);
        return getDataAndDefaultEmpty(empInfos);
    }

    @Override
    public List<EmpDataVo> getHrbpDataByEmpIds(NotifierDataDto request) {
        EmpDataQueryDto empDataQuery = new EmpDataQueryDto();
        empDataQuery.setDateTime(request.getDataTime());
        empDataQuery.setEmpIds(request.getEmpIds());
        empDataQuery.setTypeEnum(EmpDataQueryTypeEnum.ORGANIZATIONAL_ROLE);
        empDataQuery.setRoleCode("HRBP");
        if (1 == request.getType()) {
            return getCandidateDataListByQuery(empDataQuery);
        }

        return getEmpDataListByQuery(empDataQuery);
    }

    @Override
    public List<EmpDataVo> getInitiatorDataByEmpIds(NotifierDataDto request) {
        log.info("获取入职发起人参数：{}", request);
        List<String> empIds = request.getEmpIds();
        List<String> initiatorEmpIds = new ArrayList<>();
        empIds.forEach(empId -> {
            Result<PreEmpEntryProcessVo> empProcessInfo = onBoardingFeignClient.getEmpProcessInfo(empId);
            if (empProcessInfo.isSuccess()) {
                //流程信息；
                PreEmpEntryProcessVo processInfoData = empProcessInfo.getData();
                String initiatorId = processInfoData.getInitiatorId();
                Result<UserDetailInfoVo> userById = userServiceFeignClent.getUserById(Long.valueOf(initiatorId));
                initiatorEmpIds.add(String.valueOf(userById.getData().getEmpId()));
            }
        });
        EmpDataQueryDto empDataQuery = new EmpDataQueryDto();
        empDataQuery.setDateTime(request.getDataTime());
        empDataQuery.setEmpIds(initiatorEmpIds);
        empDataQuery.setTypeEnum(EmpDataQueryTypeEnum.EMP);
        log.info("入职发起人信息：{}", empDataQuery);
//        if (1 == request.getType()) {
//            return getCandidateDataListByQuery(empDataQuery);
//        }
        return getEmpDataListByQuery(empDataQuery);
    }


    @Override
    public List<EmpDataVo> getLeadersByEmpIds(NotifierDataDto request) {
        EmpDataQueryDto empDataQuery = new EmpDataQueryDto();
        empDataQuery.setDateTime(request.getDataTime());
        empDataQuery.setEmpIds(request.getEmpIds());
        empDataQuery.setTypeEnum(EmpDataQueryTypeEnum.EMP_LEADER_EMP);

        if (1 == request.getType()) {
            return getCandidateDataListByQuery(empDataQuery);
        }
        return getEmpDataListByQuery(empDataQuery);
    }

    private List<EmpDataVo> getEmpDataListByQuery(EmpDataQueryDto empDataQuery) {
        Result<List<EmpDataVo>> empInfos = hrFeignClient.getEmpInfos(empDataQuery);
        return getDataAndDefaultEmpty(empInfos);
    }

    private List<EmpDataVo> getCandidateDataListByQuery(EmpDataQueryDto empDataQuery) {
        Result<List<EmpDataVo>> empInfos = onBoardingFeignClient.getEmpDetailList(empDataQuery);
        return getDataAndDefaultEmpty(empInfos);
    }

    @Override
    public List<EmpDataVo> getCandidateDataByEmpIds(List<String> empIds, Long dataTime) {
        Result<List<PreEmpDataVo>> candidateDataByEmpIds = onBoardingFeignClient.getCandidateDataByEmpIds(empIds);
        return getDataOrDefaultEmpty(candidateDataByEmpIds);
    }

    @Override
    public List<EmpDataVo> getCandidateOrgLeaders(NotifierDataDto request) {
        return getCandidateDataListByQuery(buildQueryDtoByType(request, EmpDataQueryTypeEnum.ORG_LEADER_EMP));
    }

    @Override
    public List<EmpDataVo> getCandidateRecruiter(NotifierDataDto request) {
        EmpDataQueryDto empDataQuery = new EmpDataQueryDto();
        empDataQuery.setDateTime(request.getDataTime());
        empDataQuery.setEmpIds(request.getEmpIds());
        empDataQuery.setTypeEnum(EmpDataQueryTypeEnum.ORGANIZATIONAL_ROLE);
        empDataQuery.setRoleCode("recruiters");
        return getCandidateDataListByQuery(empDataQuery);
    }

    /**
     * 获取预入职招聘顾问
     *
     * @param request
     * @return
     */
    @Override
    public List<EmpDataVo> getCandidateRecruitment(NotifierDataDto request) {
        return getCandidateDataListByQuery(buildQueryDtoByType(request, EmpDataQueryTypeEnum.RECRUITMENT));
    }


    private EmpDataQueryDto buildQueryDtoByType(NotifierDataDto request, EmpDataQueryTypeEnum type) {
        EmpDataQueryDto empDataQuery = new EmpDataQueryDto();
        empDataQuery.setDateTime(request.getDataTime());
        empDataQuery.setEmpIds(request.getEmpIds());
        empDataQuery.setTypeEnum(type);
        return empDataQuery;
    }

    private List<EmpDataVo> getDataOrDefaultEmpty(Result<List<PreEmpDataVo>> result) {
        List<EmpDataVo> dataList = Lists.newArrayList();
        if (null == result || !result.isSuccess()) {
            return dataList;
        }

        List<PreEmpDataVo> data = result.getData();
        data.forEach(ped -> {
            EmpDataVo empData = new EmpDataVo();
            empData.setEmpId(ped.getEmpId());
            empData.setEmpWorkInfo(ped.getEmpWorkInfo());

            EmpPrivateInfoVo empPrivateInfoVo = new EmpPrivateInfoVo();
            BeanUtil.copyProperties(ped.getEmpPrivateInfo(), empPrivateInfoVo, "nativePlace");
            empData.setEmpPrivateInfo(empPrivateInfoVo);
            dataList.add(empData);
        });
        return dataList;
    }

    private List<EmpDataVo> getDataAndDefaultEmpty(Result<List<EmpDataVo>> result) {
        if (null == result || !result.isSuccess()) {
            return Lists.newArrayList();
        }

        return result.getData();
    }
}
