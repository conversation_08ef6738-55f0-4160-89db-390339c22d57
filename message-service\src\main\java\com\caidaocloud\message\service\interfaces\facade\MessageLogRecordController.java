package com.caidaocloud.message.service.interfaces.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.service.application.common.enums.MsgFromEnum;
import com.caidaocloud.message.service.application.log.repository.IMessageLogRecordRepository;
import com.caidaocloud.message.service.application.msg.dto.MsgDto;
import com.caidaocloud.message.service.application.msg.service.MessageLogRecordService;
import com.caidaocloud.message.service.application.msg.service.MsgService;
import com.caidaocloud.message.service.domain.msg.entity.MessageLogRecordDetailVo;
import com.caidaocloud.message.service.domain.msg.entity.MessageLogRecordDo;
import com.caidaocloud.message.service.interfaces.dto.msg.MessageLogRecordQueryDto;
import com.caidaocloud.util.Base64Util;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/msg/logRecord/v1")
@Api(value = "/api/msg/logRecord/v1", description = "消息日志", tags = "v0.2")
public class MessageLogRecordController {
    @Resource
    private IMessageLogRecordRepository iMessageLogRecordRepository;
    @Resource
    private MsgService msgService;
    @Resource
    private MessageLogRecordService messageLogRecordService;

    @ApiOperation("查询消息记录列表")
    @PostMapping("/messageLogRecordList")
    public Result<PageResult<MessageLogRecordDo>> messageLogRecordList(@RequestBody MessageLogRecordQueryDto queryDto) {
        PageResult<MessageLogRecordDo> pageResult = iMessageLogRecordRepository.selectPage(queryDto);
        List<MessageLogRecordDo> items = pageResult.getItems();
        for (MessageLogRecordDo messageLogRecordDo : items) {
            MsgFromEnum enumByCode = MsgFromEnum.getEnumByCode(messageLogRecordDo.getMsgFrom());
            if (enumByCode != null) {
                messageLogRecordDo.setMsgFrom(enumByCode.getName());
            }
        }
        pageResult.setItems(items);
        return Result.ok(pageResult);
    }

    @ApiOperation("消息记录重新发送")
    @GetMapping("/resendMessageLogRecord")
    public Result<String> resendMessageLogRecord(Long bid) {
        MessageLogRecordDo logRecordDo = iMessageLogRecordRepository.getMessageLogRecordByBid(bid);

        if (logRecordDo == null) {
            return Result.fail("消息记录不存在！");
        }

        MsgDto msgDto = FastjsonUtil.toObject(logRecordDo.getContent(), MsgDto.class);
        msgDto.setMdc(logRecordDo.getMdc());
        //邮件内容解密
        if (msgDto.getEmailMsg() != null && StringUtils.isNotBlank(msgDto.getEmailMsg().getContent())) {
            var content = Base64Util.decode(msgDto.getEmailMsg().getContent());
            msgDto.getEmailMsg().setContent(content);
        }
        msgService.doMsg(msgDto);
        return Result.ok("发送成功！");
    }

    @ApiOperation("消息记录详情")
    @GetMapping("/getMessageLogRecord")
    public Result<List<MessageLogRecordDetailVo>> getMessageLogRecord(Long bid) {
        List<MessageLogRecordDetailVo> detailVos = messageLogRecordService.getMessageLogRecord(bid);
        return Result.ok(detailVos);
    }
}
