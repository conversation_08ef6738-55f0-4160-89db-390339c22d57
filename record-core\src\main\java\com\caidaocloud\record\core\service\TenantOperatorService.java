package com.caidaocloud.record.core.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 租户初始化开启日志模块
 * created by: FoAng
 * create time: 21/5/2024 7:05 下午
 */
@Slf4j
public class TenantOperatorService {

    /**
     * 初始化租户脚本
     * @param tenantId
     */
    public void initTenantTable(String tenantId) {

    }

    private String fetchResource(String filePath) throws IOException {
        ClassPathResource pathResource = new ClassPathResource(filePath);
        return IOUtils.toString(pathResource.getInputStream(), StandardCharsets.UTF_8.name());
    }
}
