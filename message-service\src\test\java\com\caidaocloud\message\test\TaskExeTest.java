package com.caidaocloud.message.test;

import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.msg.cron.MsgNoticeTaskService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class TaskExeTest {
    @Resource
    private MsgNoticeTaskService msgNoticeTaskService;

    @Test
    public void testAutoMessageNoticeJobHandler(){
//        msgNoticeTaskService.autoMessageNoticeJobHandler();
    }

}
