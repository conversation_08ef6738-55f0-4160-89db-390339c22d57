@echo off
echo ========================================
echo 公告功能单元测试执行脚本
echo ========================================

cd /d "c:\caidao\caidao-message-service"

echo.
echo 1. 执行公告Repository层测试（验证insert、update操作）
echo ----------------------------------------
call mvn test -Dtest=AnnouncementRepositoryTest -f message-service/pom.xml

echo.
echo 2. 执行公告Controller层测试（验证接口功能）
echo ----------------------------------------
call mvn test -Dtest=AnnouncementControllerTest -f message-service/pom.xml

echo.
echo 3. 执行所有公告相关测试
echo ----------------------------------------
call mvn test -Dtest=Announcement*Test -f message-service/pom.xml

echo.
echo ========================================
echo 测试执行完成
echo ========================================
pause
