package com.caidaocloud.message.service.interfaces.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.sdk.enums.SseEventEnum;
import com.caidaocloud.message.service.application.notice.dto.NoticeMsgDto;
import com.caidaocloud.message.service.application.notice.service.NoticeMessageService;
import com.caidaocloud.message.service.domain.notice.sse.SseEmitterFactory;
import com.caidaocloud.message.service.interfaces.vo.msg.NoticeBusinessCountVo;
import com.caidaocloud.message.service.interfaces.vo.msg.NoticeMessageVo;
import com.caidaocloud.message.service.interfaces.vo.msg.NoticeUnReadCountVo;
import com.caidaocloud.message.service.interfaces.vo.msg.SseEventVo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

/**
 * 系统消息
 * created by: FoAng
 * create time: 15/8/2024 2:49 下午
 */
@Slf4j
@RestController
@RequestMapping("/api/msg/notice/v1")
@AllArgsConstructor
@Api(value = "/api/msg/notice/v1", description = "系统消息", tags = "v0.7")
public class NoticeMessageController {

    private NoticeMessageService noticeMessageService;

    private SseEmitterFactory sseEmitterFactory;

    @GetMapping("/register/{clientId}")
    @ApiOperation("设备注册推流")
    public SseEmitter register(@PathVariable("clientId") String clientId) {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        return sseEmitterFactory.register(userInfo.getTenantId(), userInfo.getUserId(), userInfo.getEmpId(), clientId);
    }

    @GetMapping("/register/status/{clientId}")
    @ApiOperation("注册状态")
    public Result<?> registerStatus(@PathVariable("clientId") String clientId) {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        String clientKey = SseEmitterFactory.getSseClientId(userInfo.getTenantId(), userInfo.getEmpId(), clientId);
        SseEmitter sseEmitter = sseEmitterFactory.getSseEmitter(userInfo.getTenantId(), clientKey);
        return Result.ok(sseEmitter != null);
    }

    @PostMapping("/sse/message")
    @ApiOperation("推送事件消息")
    public Result<?> sendSseEmitterMsg(@RequestParam String message, @RequestParam String clientId) {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        String clientKey = SseEmitterFactory.getSseClientId(userInfo.getTenantId(), userInfo.getEmpId(), clientId);
        SseEventVo eventVo = new SseEventVo();
        eventVo.setEventType(SseEventEnum.FETCH);
        eventVo.setContent(message);
        boolean result = sseEmitterFactory.sendMessage(userInfo.getTenantId(), clientKey, eventVo);
        return Result.ok(result);
    }

    @GetMapping("/unread/count")
    @ApiOperation("未读消息数量")
    public Result<NoticeUnReadCountVo> noticeCount() {
        return Result.ok(noticeMessageService.getUnReadCount());
    }

    @GetMapping("/unread/count/business")
    @ApiOperation("所属类型未读消息数量")
    public Result<NoticeBusinessCountVo> businessNoticeCount() {
        return Result.ok(noticeMessageService.getBusinessUnReadCount());
    }

    @PostMapping("/page")
    @ApiOperation("分页")
    public Result<PageResult<NoticeMessageVo>> page(@RequestBody NoticeMsgDto dto) {
        return Result.ok(noticeMessageService.page(dto));
    }

    @PostMapping("/delete")
    @ApiOperation("删除")
    public Result<?> delete(@RequestParam("id") String id) {
        noticeMessageService.deleteById(id);
        return Result.ok(true);
    }

    @PostMapping("/detail")
    @ApiOperation("消息详情")
    public Result<?> detail(@RequestParam String bid) {
        return Result.ok(noticeMessageService.detailVo(bid));
    }


    @PostMapping("/markRead")
    @ApiOperation("标记已读")
    public Result<?> markRead(@RequestBody List<String> bids) {
        noticeMessageService.markReadAction(bids, null, true);
        return Result.ok(true);
    }

    @PostMapping("/persist/all")
    @ApiOperation("重建索引")
    public Result<?> persistAllEsData() {
        noticeMessageService.persistAllEsData();
        return Result.ok();
    }

    @PostMapping("/workflow/marked")
    @ApiOperation("标记业务数据已读")
    public Result<?> markRead(@RequestParam String businessId) {
        noticeMessageService.markReadAction(businessId, true);
        return Result.ok();
    }

    @PostMapping("/markAllRead")
    @ApiOperation("一键标记已读")
    public Result<?> markAllRead(@RequestParam String businessType) {
        noticeMessageService.markReadAction(null, businessType, true);
        return Result.ok(true);
    }

    @PostMapping("/markUnRead")
    @ApiOperation("标记未读")
    public Result<?> markUnRead(@RequestBody List<String> bids) {
        noticeMessageService.markReadAction(bids, null, false);
        return Result.ok(true);
    }

}
