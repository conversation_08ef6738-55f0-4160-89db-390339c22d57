package com.caidaocloud.message.sdk.enums;

import com.caidaocloud.util.StringUtil;
import lombok.Getter;

import java.util.Arrays;

/**
 * 消息文本类型
 * created by: FoAng
 * create time: 15/8/2024 10:54 上午
 */
@Getter
public enum ContentTypeEnum {

    PLAIN("文本", "PLAIN"),

    RICH("富文本","RICH");

    final String label;

    final String value;

    ContentTypeEnum(String label, String value) {
        this.label = label;
        this.value = value;
    }

    public static ContentTypeEnum of(String value) {
        return Arrays.stream(values()).filter(it -> StringUtil.isNotEmpty(value) && it.value.equals(value))
                .findFirst().orElse(PLAIN);
    }
}
