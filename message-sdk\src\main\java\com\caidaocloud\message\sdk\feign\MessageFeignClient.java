package com.caidaocloud.message.sdk.feign;

import com.caidaocloud.message.sdk.dto.EmpMsgConfigQueryDto;
import com.caidaocloud.message.sdk.dto.MsgConfigDto;
import com.caidaocloud.message.sdk.dto.MsgConfigItemVo;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
        value = "caidaocloud-message-service",
        fallback = MessageFeignClientFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "MessageFeignClient"
)
public interface MessageFeignClient {
    /**
     * 获取启用的消息配置列表
     */
    @GetMapping("/api/msg/config/v1/configList")
    Result<List<MsgConfigDto>> getEnableMsgConfigList(@RequestParam("type") String type);

    /**
     * 获取启用的消息配置列表
     */
    @GetMapping("/api/msg/config/v1/configListSequence")
    Result<String> getEnableMsgConfigListSequence(@RequestParam("type") String type);

    /**
     * 获取启用的消息配置
     */
    @GetMapping("/api/msg/config/v1/detail")
    Result<MsgConfigDto> getMsgConfigOne(@RequestParam("bid") String bid);

    /**
     * 获取消息列表
     * @param bids
     * @return
     */
    @GetMapping("/api/msg/config/v1/list/ids")
    Result<List<MsgConfigItemVo>> listByIds(@RequestParam("bids") String bids);

    /**
     * 获取员工对应的消息配置
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/api/msg/config/v1/queryList")
    Result<List<MsgConfigDto>> getMsgConfig(@RequestBody EmpMsgConfigQueryDto queryDto);

}
