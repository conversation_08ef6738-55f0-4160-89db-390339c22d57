package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.message.service.application.app.dto.AppMsgSender;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "AppHttpApiClient",
        url = "${caidaocloud.msg.app.url:}",
        fallback = AppAdapterFeignClientFallBack.class,
        configuration = FeignConfiguration.class)
public interface AppAdapterFeignClient {
    @PostMapping("/api/adapterDisney/msg/v1/simple")
    Result sendMesage(@RequestBody AppMsgSender messageDto);

    @PostMapping("/api/adapterDisney/msg/v1/batch")
    Result batchSendMesage(@RequestBody List<AppMsgSender> msgList);
}
