package com.caidaocloud.message.test;

import com.caidaocloud.util.FastjsonUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AliyunSmsTest {
    public static void main(String[] args) {
        String input = "This is a $a:test$ string with $b:multiple$ $c:occurrences$ of $d:dollar signs$.";
        //         input = "This is a ${a}test$ string with ${b}multiple$ ${c}occurrences$ of ${d}dollar signs$.";
        Map<String, String> params = new HashMap<>();
        Pattern pattern = Pattern.compile("\\$(.*?)\\$");
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            String group = matcher.group(1);
            if(null != group){
                String [] split = group.split(":");
                if(split.length > 1){
                    params.put(split[0], split[1]);
                }
            }
            System.out.println(group);
        }
        System.out.println();
        System.out.println(FastjsonUtil.toJson(params));
    }
}
