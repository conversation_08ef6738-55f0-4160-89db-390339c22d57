package com.caidaocloud.message.service.application.feign.dto;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsObject;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import lombok.Data;

import java.util.List;

@Data
public class WorkflowApproverSettingDetail extends DataSimple {

    private String settingBid;
    @DisplayAsArray
    private List<String> approveTarget;
    @DisplayAsArray
    private List<EmpSimple> approvers;
    private int sort;

    // 匹配规则
    @DisplayAsObject
    private ConditionTree matchingCondition;
}
