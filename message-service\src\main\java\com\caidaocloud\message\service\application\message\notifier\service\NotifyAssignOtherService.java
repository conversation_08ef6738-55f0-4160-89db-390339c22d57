package com.caidaocloud.message.service.application.message.notifier.service;

import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 处理 指定外部人员
 */
@Slf4j
@Service
public class NotifyAssignOtherService implements INotifyTargetService {
    @Override
    public String getNotifyTarget() {
        return NoticeTarget.ASSIGN_OTHER.toString();
    }

    @Override
    public void getNotifier(List<NotifierDto> notifier, NotifyObjectDto obj) {
        log.info("Specify external personnel message notification. notifier={}, obj={}",
                FastjsonUtil.toJson(notifier), FastjsonUtil.toJson(obj));

        // 外部人员设置 empId 为 null
        notifier.add(new NotifierDto().setEmpId(null));
        /*if(2 != obj.getType()){
            // 外部人员类型不匹配
            throw new ServerException("The specified external personnel type does not match");
        }*/
    }
}
