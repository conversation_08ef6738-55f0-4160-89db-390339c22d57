package com.caidaocloud.message.test;

import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.msg.event.ContractConsumer;
import com.caidaocloud.message.service.application.msg.event.ContractEventEntity;
import com.caidaocloud.util.FastjsonUtil;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class AppMsgTest {
    @Resource
    private ContractConsumer contractConsumer;
    @Test
    public void testMsg(){
        ContractEventEntity msg = new ContractEventEntity();
        msg.setTenantId("33");
        msg.setCreateTime(System.currentTimeMillis());
        msg.setMsgConfig("1447909033965571");
        msg.setUserId(0L);
        msg.setMdc("1212");
        msg.setSubjects(Lists.newArrayList("1434400592427011"));

        String sstr = "{\n" +
                "    \"createTime\":1658806659306,\n" +
                "    \"ext\":{\n" +
                "        \"docTemplate.businessProcess.en\":\"Contract renew\",\n" +
                "        \"docTemplate.businessProcess.cn\":\"合同续签\"\n" +
                "    },\n" +
                "    \"msgConfig\":\"1453494690879503\",\n" +
                "    \"subjects\":[\n" +
                "        \"1434400592427011\"\n" +
                "    ],\n" +
                "    \"tenantId\":\"33\",\n" +
                "    \"userId\":0\n" +
                "}";
//        contractConsumer.process(sstr);
    }
}
