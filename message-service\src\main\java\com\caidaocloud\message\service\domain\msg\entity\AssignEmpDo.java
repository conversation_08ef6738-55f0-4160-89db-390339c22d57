package com.caidaocloud.message.service.domain.msg.entity;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.message.service.application.common.constant.LangCodeConstant;
import com.caidaocloud.message.service.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;
import com.caidaocloud.message.service.domain.msg.repository.AssignEmpRepository;
import com.caidaocloud.message.service.infrastructure.utils.LangUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;
import java.util.stream.Collectors;

import static com.caidaocloud.message.service.application.common.constant.LangCodeConstant.code_70000;
import static com.caidaocloud.message.service.application.common.enums.NoticeTarget.ASSIGN_EMP;

/**
 * AssignEmpDo
 *
 * <AUTHOR>
 * @date 2022/6/6 下午5:58
 */
@Data
@Service
@Slf4j
public class AssignEmpDo extends BaseDomainDoImpl {
    /**
     * 消息设置ID
     */
    private String msgConfig;

    /**
     * 指定人员
     */
    private EmpSimple emp;

    private final static String IDENTIFIER = "entity.message.AssignEmp";

    @Resource
    private AssignEmpRepository repository;

    @Override
    public BaseRepository getRepository() {
        return repository;
    }

    @Override
    public String getDoIdentifier() {
        return IDENTIFIER;
    }

    @Override
    protected DataSimple build() {
        AssignEmpDo data = new AssignEmpDo();
        data.setIdentifier(IDENTIFIER);
        return data;
    }

    private final static int MAX_SIZE = 100;

    public void saveAssignEmp(MsgConfigDo config) {
        if (config.getNotifyObject() == null || !config.getNotifyObject().contains(ASSIGN_EMP.toString())) {
            return;
        }

        // 指定系统人员不为空
        PreCheck.preCheckArgument(config.getEmps() == null || config.getEmps().isEmpty(),
                LangUtil.getFormatMsg(code_70000,LangUtil.getMsg(LangCodeConstant.ASSIGN_EMP)));
        // 指定系统人员最大不超过100
        PreCheck.preCheckArgument(config.getEmps().size() > MAX_SIZE,LangUtil.getMsg(LangCodeConstant.ASSIGN_EMP_EXCEED_100));
        log.info("delete assign emp of config '{}'", config.getBid());
        deleteByConfigId(config);

        // 批量保存
        List<AssignEmpDo> dataList = config.getEmps().stream().map(emp -> {
            AssignEmpDo data = new AssignEmpDo();
            data.setEmp(emp);
            data.setMsgConfig(config.getBid());
            data.setDataEndTime(DateUtil.MAX_TIMESTAMP);
            return data;
        }).collect(Collectors.toList());
        repository.batchInsert(IDENTIFIER, dataList);
    }

    private void deleteByConfigId(MsgConfigDo config) {
        repository.deleteByConfigId(IDENTIFIER, config.getBid());
    }

    public List<AssignEmpDo> getByConfig(MsgConfigDo config) {
        return getByConfigId(config.getBid());
    }

    public List<AssignEmpDo> getByConfigId(String msgConfig) {
        AssignEmpDo query = (AssignEmpDo) build();
        query.setMsgConfig(msgConfig);
        return repository.selectList(query);
    }
}
