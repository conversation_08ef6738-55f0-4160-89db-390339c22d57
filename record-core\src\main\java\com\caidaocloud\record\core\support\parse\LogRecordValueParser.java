package com.caidaocloud.record.core.support.parse;

import com.caidaocloud.record.core.service.IFunctionService;
import com.google.common.base.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.context.expression.AnnotatedElementKey;
import org.springframework.expression.EvaluationContext;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class LogRecordValueParser implements BeanFactoryAware {

    protected BeanFactory beanFactory;

    private final LogRecordExpressionEvaluator expressionEvaluator = new LogRecordExpressionEvaluator();

    private IFunctionService functionService;

    private static final Pattern pattern = Pattern.compile("\\{\\s*(\\w*)\\s*\\{(.*?)}}");

    private static final Pattern allPattern = Pattern.compile("\\{\\s*(\\w*)\\s*\\{(.*)}}");

    public Map<String, String> processTemplate(Collection<String> templates, Object ret,
                                               Class<?> targetClass, Method method, Object[] args, String errorMsg,
                                               Map<String, String> beforeFunctionNameAndReturnMap) {
        Map<String, String> expressionValues = new HashMap<>();
        EvaluationContext evaluationContext = expressionEvaluator.createEvaluationContext(method, args, targetClass, ret, errorMsg, beanFactory);
        for (String expressionTemplate : templates) {
            if (expressionTemplate.contains("{")) {
                String parseValue = processItemExpress(targetClass, method, beforeFunctionNameAndReturnMap, evaluationContext, expressionTemplate);
                expressionValues.put(expressionTemplate, parseValue);
            } else {
                expressionValues.put(expressionTemplate, expressionTemplate);
            }
        }
        return expressionValues;
    }

    private String processItemExpress(Class<?> targetClass, Method method, Map<String, String> beforeFunctionNameAndReturnMap,
                                    EvaluationContext evaluationContext, String expressionTemplate) {
        Matcher matcher = pattern.matcher(expressionTemplate);
        while (matcher.find()) {
            AnnotatedElementKey annotatedElementKey = new AnnotatedElementKey(method, targetClass);
            String expression = matcher.group(2);
            if (expression.contains("{")) {
                Matcher allMatcher = allPattern.matcher(expressionTemplate);
                if (allMatcher.find()) {
                    expression = allMatcher.group(2);
                    String parseValue = processItemExpress(targetClass, method, beforeFunctionNameAndReturnMap, evaluationContext, expression);
                    String value = expressionEvaluator.parseExpression(parseValue, annotatedElementKey, evaluationContext);
                    String functionReturnValue = getFunctionReturnValue(beforeFunctionNameAndReturnMap, value, matcher.group(1));
                    String expressKey = String.format("{%s{%s}}", allMatcher.group(1), allMatcher.group(2));
                    expressionTemplate = expressionTemplate.replace(expressKey, Strings.nullToEmpty(functionReturnValue));
                }
            } else {
                String value = expressionEvaluator.parseExpression(expression, annotatedElementKey, evaluationContext);
                String functionReturnValue = getFunctionReturnValue(beforeFunctionNameAndReturnMap, value, matcher.group(1));
                String expressKey = String.format("{%s{%s}}", matcher.group(1), matcher.group(2));
                expressionTemplate = expressionTemplate.replace(expressKey, Strings.nullToEmpty(functionReturnValue));
            }
        }
        return expressionTemplate;
    }

    public Map<String, String> processBeforeExecuteFunctionTemplate(Collection<String> templates, Class<?> targetClass, Method method, Object[] args) {
        Map<String, String> functionNameAndReturnValueMap = new HashMap<>();
        EvaluationContext evaluationContext = expressionEvaluator.createEvaluationContext(method, args, targetClass, null, null, beanFactory);

        for (String expressionTemplate : templates) {
            if (expressionTemplate.contains("{")) {
                Matcher matcher = pattern.matcher(expressionTemplate);
                while (matcher.find()) {
                    String expression = matcher.group(2);
                    if (expression.contains("#_ret") || expression.contains("#_errorMsg")) {
                        continue;
                    }
                    AnnotatedElementKey annotatedElementKey = new AnnotatedElementKey(method, targetClass);
                    String functionName = matcher.group(1);
                    if (functionService.beforeFunction(functionName)) {
                        String value = expressionEvaluator.parseExpression(expression, annotatedElementKey, evaluationContext);
                        String functionReturnValue = getFunctionReturnValue(null, value, functionName);
                        functionNameAndReturnValueMap.put(functionName, functionReturnValue);
                    }
                }
            }
        }
        return functionNameAndReturnValueMap;
    }

    private String getFunctionReturnValue(Map<String, String> beforeFunctionNameAndReturnMap, String value, String functionName) {
        String functionReturnValue = "";
        if(beforeFunctionNameAndReturnMap != null){
            functionReturnValue = beforeFunctionNameAndReturnMap.get(functionName);
        }
        if(StringUtils.isEmpty(functionReturnValue)){
            functionReturnValue = functionService.apply(functionName, value);
        }
        return functionReturnValue;
    }

    @Override
    public void setBeanFactory(@NotNull BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }

    public void setFunctionService(IFunctionService functionService) {
        this.functionService = functionService;
    }
}
