package com.caidaocloud.message.service.application.message.send.handler;

import com.caidaocloud.message.service.application.app.dto.AppMsgSender;
import com.caidaocloud.message.service.application.app.enums.MsgAccountType;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.send.dto.MessageContext;
import com.caidaocloud.message.service.interfaces.vo.template.TemplateVo;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 根据模版生产App消息
 */
@Slf4j
@Service
public class AppMsgHandler implements IMsgHandler {

    @Override
    public String getHandlerType() {
        return "3";
    }

    @Override
    public void doMsgHandler(MessageContext context) {
        List<AppMsgSender> appMsgList = new ArrayList<>();
        Map<String, NotifierDto> receiverMap = context.getReceiverMap();

        TemplateVo template = context.getTemplate();
        // 系统通知对象收件人邮箱
        context.getEmpList().forEach(sysEmpId -> {
            NotifierDto notifierDto = receiverMap.get(sysEmpId);
            getAppMsgList(notifierDto, template, appMsgList);
        });

        // 监护人
        List<NotifierDto> otherObjList = context.getOtherObjList();
        if (null != otherObjList || otherObjList.isEmpty()) {
            otherObjList.forEach(notifier -> {
                getAppMsgList(notifier, template, appMsgList);
            });
        }

        context.getMsgDto().setAppMsg(appMsgList);
    }

    protected void getAppMsgList(NotifierDto notifierDto, TemplateVo template, List<AppMsgSender> appMsgList) {
        if (null == notifierDto) {
            log.warn("An empty notification object exists.");
            return;
        }

        String userId = notifierDto.getCompanyEmail();
        if (StringUtil.isNotEmpty(userId)) {
            AppMsgSender appMsgSender = new AppMsgSender().setTitle(template.getTitle())
                    .setContent(template.getContent())
                    .setUserId(userId)
                    .setType(MsgAccountType.email);
            appMsgList.add(appMsgSender);
        }

        userId = notifierDto.getWorkno();
        if (StringUtil.isNotEmpty(userId)) {
            AppMsgSender appMsgSender = new AppMsgSender().setTitle(template.getTitle())
                    .setContent(template.getContent())
                    .setUserId(userId)
                    .setType(MsgAccountType.workno);
            appMsgList.add(appMsgSender);
        }

        userId = Optional.ofNullable(notifierDto.getPhone())
                .map(phone -> phone.getValue()).map(phoneValue -> phoneValue.split("\\+")[0]).orElse(null);
        if (StringUtil.isNotEmpty(userId)) {
            AppMsgSender appMsgSender = new AppMsgSender().setTitle(template.getTitle())
                    .setContent(template.getContent())
                    .setUserId(userId)
                    .setType(MsgAccountType.phone);
            appMsgList.add(appMsgSender);
            return;
        }

    }

}
