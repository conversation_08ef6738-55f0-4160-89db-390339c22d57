package com.caidaocloud.notice;

import com.caidaocloud.message.sdk.enums.ContentTypeEnum;
import com.caidaocloud.message.sdk.enums.NoticeActionEnum;
import com.caidaocloud.message.sdk.enums.NoticeBusinessEnum;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.msg.dto.NoticeMessageDto;
import com.caidaocloud.message.service.application.notice.service.NoticeMessageService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.google.common.collect.Sets;
import org.apache.groovy.util.Maps;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * created by: FoAng
 * create time: 21/8/2024 10:31 上午
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class NoticeMessageTest {

    @Resource
    private NoticeMessageService noticeMessageService;


    @Before
    public void init() {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        userInfo.setUserId(0L);
        userInfo.setEmpId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }

    @Test
    public void saveNoticeMsg() {
        NoticeMessageDto msgDto = new NoticeMessageDto();
        msgDto.setEmpIds(Sets.newHashSet("111111"))
                .setTitle("这是标题")
                .setContent("这是内容")
                .setSummary("这是汇总")
                .setContentType(ContentTypeEnum.PLAIN)
                .setAction(NoticeActionEnum.VIEW)
                .setBusinessType(NoticeBusinessEnum.EVENT)
                .setExt(Maps.of(
                        "funcCode", "111111",
                        "createTime", "121212121"
                ));
        noticeMessageService.sendNoticeMessage(msgDto);
    }
}
