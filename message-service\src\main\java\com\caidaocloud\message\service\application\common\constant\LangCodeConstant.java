package com.caidaocloud.message.service.application.common.constant;

/**
 * LangCodeConstant
 *
 * <AUTHOR>
 * @date 2022/6/7 下午5:44
 */
public class LangCodeConstant {
    // %s 不能为空
    public final static int code_70000 = 70000;
    // SMTP服务器
    public final static int code_70001 = 70001;
    // 用户名
    public final static int code_70002 = 70002;
    // 密码
    public final static int code_70003 = 70003;
    // 发件人昵称
    public final static int code_70004 = 70004;
    // 收件人
    public final static int code_70005 = 70005;
    // 服务异常
    public final static int code_70006 = 70006;
    // 数据不存在
    public final static int code_70007 = 70007;
   // 消息id
    public final static int code_70008 = 70008;
    // 消息名称
    public final static int code_70009 = 70009;
    // 消息种类
    public final static int code_70010 = 70010;
    // 通知对象
    public final static int code_70011 = 70011;
    // 当前有其他任务正在处理，请稍后重试
    public final static int OTHER_TASKS_CURRENTLY_BEING_PROCESSED = 70012;
    // 指定系统人员
    public final static int ASSIGN_EMP = 70013;
    // 指定系统人员最大100人
    public final static int ASSIGN_EMP_EXCEED_100 = 70014;
    // 不能修改启用状态的消息设置
    public final static int CONFIG_CANNOT_EDIT = 70015;
    // 不能删除启用状态的消息设置
    public final static int CONFIG_CANNOT_DELETE = 70016;
    // 发送时间不能早于当前时间
    public final static int SEND_TIME_ERROR = 70017;
    // 发送时间
    public final static int SEND_TIME = 70018;
    // 模板名称重复
    public final static int DUPLICATE_TEMPLATE_NAME = 70019;
    // 消息附件不能大于 10
    public final static int code_70020 = 70020;
    // 消息附件名称过长
    public final static int code_70021 = 70021;
    // 通知规则
    public final static int code_70022 = 70022;
}
