package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.security.config.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/2/21 上午11:53
 * @Version 1.0
 **/
@FeignClient(value = "dingTalkAgencyFeignClient",
        url = "http://api.dingtalk.com",
        configuration = FeignConfiguration.class,
        fallback = DingTalkAgencyFeignClientFallback.class)
public interface DingTalkAgencyFeignClient {


    /*@RequestParam("operatorId") String operatorId,*/


    @PostMapping("/v1.0/todo/users/{unionId}/tasks")
    Map createTask(@RequestBody Map reqMap, @PathVariable("unionId") String unionId,  @RequestParam("access_token") String accessToken);





}
