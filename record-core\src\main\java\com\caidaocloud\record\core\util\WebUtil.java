package com.caidaocloud.record.core.util;

import javax.servlet.http.HttpServletRequest;

/**
 * created by: FoAng
 * create time: 29/5/2024 5:20 下午
 */
public class WebUtil {

    public static String getOperatorPlatform(HttpServletRequest request) {
        String operatorPlatform = "PC";
        if (request != null) {
            String userAgent = request.getHeader("User-Agent");
            boolean isMobile = false;
            if (userAgent != null) {
                isMobile = userAgent.matches(".*(?i)(mobile|android|iphone|ipad).*");
            }
            return isMobile ? "H5" : "PC";
        }
        return operatorPlatform;
    }

    public static String getOperatorSource(HttpServletRequest request) {
        return internalCallRequest(request) ? "接口调用" : "手工操作";
    }

    public static boolean internalCallRequest(HttpServletRequest request) {
        return request.getHeader("Internal-Call-User") != null;
    }
}
