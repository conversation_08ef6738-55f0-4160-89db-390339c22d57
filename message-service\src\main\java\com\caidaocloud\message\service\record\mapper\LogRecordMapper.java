package com.caidaocloud.message.service.record.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidaocloud.message.service.record.bean.LogRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * created by: FoAng
 * create time: 22/5/2024 4:16 下午
 */
public interface LogRecordMapper extends BaseMapper<LogRecord> {

    @Select("${sql}")
    void initRecordTable(@Param("sql") String sql);
}
