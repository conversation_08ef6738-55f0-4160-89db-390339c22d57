package com.caidaocloud.message.sdk.enums;

import com.caidaocloud.util.StringUtil;

/**
 * 入职系统的消息通知
 */
public enum NoticeType {

    /**
     * 电子签通知
     */
    RENEWAL_NOT_SIGN("1", "文件签署--员工未签署提醒", "Document signing - unsigned reminder"),
    SIGN_REMINDER("2", "文件签署--发起签署", "Document signing - initiate signing"),
    INVALID_REMINDER("3", "文件签署--作废文件提醒", "Document signing - reminder of obsolete documents"),
    SIGN_COMPLETE("23", "文件签署--完成提醒", "Document signing - complete"),
    CERT_SIGN_COMPLETE("52", "电子证明-盖章完成提醒", "Electronic proof - Stamp completion reminder"),
    SIGN_URGE("24", "文件签署--催办", "Document signing - urge"),
    SIGN_REVOKE("25", "文件签署--撤回", "Document signing - revoke"),
    SIGN_JOB_DESC_FILE("31", "文件签署-岗位说明书未匹配到文件", "Document signing - Automatic job description file"),
    CONTRACT_MANAGE_NEW("21", "合同管理--新签", "Contract manage - new"),
    CONTRACT_MANAGE_RENEW("22", "合同管理--续签", "Contract manage - Renew"),
    CONTRACT_MANAGE_AMENDMENT("35", "合同管理--改签", "Contract manage - Amendment"),
    CONTRACT_EXPIRE("43", "合同--到期提醒", "Contract manage - expire"),
    CONTRACT_CONTINUE_CONFIRM("53", "合同--续签意向通知", "Contract manage - Intent to Renew"),
    CONTRACT_AUTO_RENEW("71", "合同-自动发起合同续签", "Contract manage - AutoRenew Contract"),
    CONTRACT_AUTO_RENEW_ERROR("72", "合同-自动发起合同续签失败提醒", "Contract manage - AutoRenew Contract error"),
    MANUAL_PUSH("4", "手动推送", "Manual push"),
    WORKFLOW_PUSH("5", "工作流通知", "Workflow notice"),
    CONTRACT_RENEW_END_TIME_CHANGE("39", "合同-续签修改合同结束日期通知", "Contract manage - End time changed when contract renewed"),
    CONTRACT_REFUSE_TO_SIGN("69", "文件签署-员工拒绝签署", "Document signing - Refuse to sign"),

    /**
     * 入职系统通知
     */
    ON_BOARDING_CANDIDATE_SUBMIT("6", "入职--候选人提交材料", "On-boarding Candidate Submit"),
    ON_BOARDING_CANDIDATE_NOT_SUBMIT("7", "入职--候选人未提交材料", "On-boarding Candidate Not Submit"),
    ON_BOARDING_APPROVED("8", "入职--审核通过(候选人)", "On-boarding Approved(candidate)"),
    ON_BOARDING_GO_BACK("9", "入职--退回通知", "On-boarding go back"),
    ON_BOARDING_CONTINUE_WORK("10", "入职--继续入职", "On-boarding Continue to work"),
    ON_BOARDING_COORDINATION("11", "入职--协同通知", "On-boarding coordination"),
    ON_BOARDING_COORDINATION_URGE("12", "入职--协同催办", "On-boarding Collaborative urging"),
    ON_BOARDING_START_ENTRY("13", "入职--发起入职", "On-boarding start Entry"),
    ON_BOARDING_NO_FILE_UPLOAD_REMINDER("14", "入职--未上传文件提醒", "On-boarding No file upload reminder"),
    ON_BOARDING_JOB_DESC_FILE("15", "入职--自动获取岗位说明书未匹配到文件", "On-boarding Automatic job description file"),
    ON_BOARDING_CHANGE_GRADUATION_DATE("16", "入职--变更预计毕业日期通知", "On-boarding change expected graduation date"),
    ON_BOARDING_CHANGE_ENTRY_DATE("17", "入职--变更入职日期通知", "On-boarding change Date of entry"),
    ON_BOARDING_COMPLETION("18", "入职--完成通知", "On-boarding Completion notice"),
    ON_BOARDING_CANCEL("19", "入职--取消入职", "On-boarding Cancel"),
    ON_BOARDING_URGE("20", "入职--催办", "On-boarding Urge"),

    ON_BOARDING_OPERATOR_APPROVAL("26", "入职--审核通过操作人通知", "Operator notification of approval"),
    ON_BOARDING_CONTINUE_ENTRY("27", "入职--继续入职操作人通知", "Notification of continuing employment operator"),

    //迪士尼定制
    ON_BOARDING_TRAINING_DATE_UPDATE("51", "入职——变更入职培训日通知", "Notification of training date update"),

    RESIGNATION_CERTIFICATE_AFTER_UNSIGNED("28", "离职证明--离职日后员工未签署", "Not signed after resignation date"),
    RESIGNATION_CERTIFICATE_AT_RESIGNATION_DATE("29", "离职证明--离职日员工已签署", "Signed before resignation date"),
    RESIGNATION_CERTIFICATE_AFTER_RESIGNATION_DATE("30", "离职证明--离职日后员工已签署", "Signed after resignation date"),
    RESIGNATION_CERTIFICATE_NO_SIGN("42", "离职证明--离职日", "Resignation date"),
    ON_BOARDING_EMAIL_ERROR("32", "入职--公司邮箱生成异常提醒", "On_boarding email error"),
    ON_BOARDING_NO_CONFIRM("33", "入职--候选人未确认提醒", "on_boarding_no_confirm"),
    ON_BOARDING_CERIFICATION_CODE("34", "入职--验证码", "on_boarding_cerification_code"),
    ON_BOARDING_EXPIRE_NO_ENTRY("36", "入职--到期未入职提醒", "on_boarding_expire_no_entry"),
    ON_BOARDING_REFUSE_OFFER("37", "入职--拒绝offer提醒", "on_boarding_refuse_offer"),
    //caidao-2247 新增入职同意入职；
    ON_BOARDING_ACCEPT_OFFER("38", "入职--同意入职", "on_boarding_accept_offer"),
    ON_BOARDING_ACCOUNT_CREATE("40", "入职--账号密码生成", "on_boarding_account_create"),

    EMPLOYEE_EMAIL_ERROR("41", "员工--公司邮箱生成异常提醒", "Employee email error"),
    EMPLOYEE_ACCOUNT_CREATE("45", "员工--账号密码生成", "Employee account create"),
    BECOME_DUE_NO_FEEDBACK("44", "到期未反馈提醒", "become due no feedback"),
    EMP_PROBATION_END_REMIND("60", "转正——试用期到期提醒", "emp probation end notice"),
    EMP_REGULAR_REMIND("59", "转正——转正通知", "emp regular notice"),

    TRANSFER_EFFECTIVE_DATE_EXPIRATION_REMINDER("50", "人事异动--生效日期到期提醒", "transfer effective date expiration reminder"),
    TRANSFER_CHANGE_EMPL_SUPERIOR("61", "人事异动--变更员工上级", "tranfer change empl superior"),
    TRANSFER_CHANGE_DEPT_HEAD("62", "人事异动--变更组织负责人", "tranfer change dept head"),
    RESIGNATION_CHANGE_EMPL_SUPERIOR("63", "离职--变更员工上级", "resignation change empl superior"),
    RESIGNATION_CHANGE_DEPT_HEAD("64", "离职--变更组织负责人", "resignation change dept head"),
    PART_TIME_JOB_LOSS_EFFECTIVE_CHANGE_SUPERIOR("65", "兼岗失效--变更员工上级", "part time job loss effective change superior"),
    PART_TIME_JOB_LOSS_EFFECTIVE_CHANGE_DEPT_HEAD("66", "兼岗失效--变更组织负责人", "part time job loss effective change dept head"),

    /**
     * 考勤通知
     */
    ATTENDANCE_REGISTER_MSG("54", "考勤通知--打卡", "Attendance clock in message reminder"),
    ATTENDANCE_DAILY_REPORT_MSG("55", "考勤通知--考勤日报", "Attendance daily news reminder"),
    ATTENDANCE_WEEKLY_REPORT_MSG("56", "考勤通知--考勤周报", "Weekly attendance report message reminder"),
    ATTENDANCE_MONTH_REPORT_MSG("57", "考勤通知--考勤月报", "Monthly attendance report message reminder"),
    ATTENDANCE_LEAVE_CANCEL_MSG("58", "考勤通知--销假提醒", "Reminder of attendance and leave cancellation message"),
    ATTENDANCE_ABNORMAL_REMINDER_MSG("68", "考勤通知--考勤异常汇总提醒", "Reminder for abnormal attendance results summary message"),
    ATTENDANCE_DETAIL_REMINDER_MSG("70", "考勤通知--考勤明细推送", "Attendance details push"),
    EMP_MANUAL_PUSH("67", "员工--手动推送", "Employee Manual push"),

    CERTIFICATE_EXPIRE_NOTICE("101","证书管理 - 到期提醒","Certificate - expire"),

    /**
     * 报酬通知
     */
    PAYROLL_PAYSLIP_NOTICE("201","工资单提醒","Payslip Reminder")
    ;

    private String index;
    private String name;
    private String enName;

    NoticeType(String index, String name, String enName) {
        this.index = index;
        this.name = name;
        this.enName = enName;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public static NoticeType getEnumByIndex(String value) {
        if (StringUtil.isBlank(value)) {
            return null;
        }

        NoticeType[] values = NoticeType.values();
        for (NoticeType noticeType : values) {
            if (noticeType.index.equals(value)) {
                return noticeType;
            }
        }
        return null;
    }

    public static NoticeType getEnumByValue(String value) {
        if (StringUtil.isBlank(value)) {
            return null;
        }

        NoticeType[] values = NoticeType.values();
        for (NoticeType noticeType : values) {
            String name = noticeType.toString();
            if (name.equals(value)) {
                return noticeType;
            }
        }
        return null;
    }

    public static String getIndexByValue(String value) {
        NoticeType type = getEnumByValue(value);
        if (type == null) {
            return "";
        }
        return type.index;
    }
}
