package com.caidaocloud.message.sdk.event.constant;

public class MQConstant {

    public final static String MESSAGE_SERVICE_EXCHANGE = "message.fac.direct.exchange";

    public final static String EXCHANGE = "message.fac.delayed.exchange";
    // 平台级别的租户消息
    public final static String ROUTING_KEY = "routingKey.message.delayed.platform";

    public final static String QUEUE_PLATFORM_TENANT = "message.queue.platform.tenant";

    public final static String PLATFORM_TENANT_SEPARATOR = "%s_%s";

    public final static String EXCHANGE_NOTICE = "notice.fac.direct.exchange";

    /**
     * 待办消息待办消息节点
     */
    public static final String NOTICE_TASK_ROUTING_KEY = "routingKey.message.notice.task.handle";
}
