package com.caidaocloud.message.service.infrastructure.repository.po;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.service.domain.base.entity.DataEntity;
import lombok.Data;

/**
 * 消息日志po
 *
 * <AUTHOR>
 * @date 2022/6/10
 **/
@Data
public class MessageLogPo extends DataEntity {

    /**
     * 发送类型
     */
    private String type;

    /**
     * 接受者
     */
    private String receiver;

    /**
     * 消息内容
     */
    private String message;

    /**
     * 发送状态
     */
    private EnumSimple status;

    /**
     * 错误日志信息
     */
    private String errorMsg;

    /**
     * 发送运营商渠道
     */
    private String channel;

}
