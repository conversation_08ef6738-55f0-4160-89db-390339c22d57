package com.caidaocloud.message.service.application.email.props;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * created by: FoAng
 * create time: 13/10/2022 4:07 下午
 */
@ConfigurationProperties(prefix = "caidaocloud.msg.email")
@Component
@Data
public class MailAuthProperties {

    /**
     * 是否开启认证
     */
    private boolean auth;

    /**
     * 开启加密扩展
     */
    private boolean starttls;

    /**
     * 是否ssl链接
     */
    private boolean ssl;

}
