package com.caidaocloud.message.service.record.feign;

import com.caidaocloud.message.service.record.config.EnableLogFeignConfig;
import com.caidaocloud.message.service.record.feign.callback.ResourceFeignFallback;
import com.caidaocloud.record.core.beans.AuthResourceUrl;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * created by: FoAng
 * create time: 24/5/2024 11:10 上午
 */
@FeignClient(
        value = "caidaocloud-auth-service",
        configuration = EnableLogFeignConfig.class,
        contextId = "resource-feign-client",
        fallback = ResourceFeignFallback.class
)
public interface ResourceFeign {

    @GetMapping("/api/auth/v1/resource/url/detail")
    Result<AuthResourceUrl> getResourceData(@RequestParam("url") String url);
}
