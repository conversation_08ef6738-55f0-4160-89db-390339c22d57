package com.caidaocloud.record.core.ext;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Date;
import java.util.Optional;

/**
 * created by: FoAng
 * create time: 16/5/2024 4:11 下午
 */
@Slf4j
@Getter
public enum FieldType {

	STRING,
	NUMBER,
	BOOLEAN,
	ENUM,
	DATE,
	TIMESTAMP,
	JSON,
	Address(Address.class),
	Attachment(Attachment.class),
	DictSimple(DictSimple.class),
	EmpSimple(EmpSimple.class),
	EnumSimple(EnumSimple.class),
	JobGradeRange(JobGradeRange.class),
	PhoneSimple(PhoneSimple.class),
	TreeParent(TreeParent.class);

	private Class<?> linkClass;

	FieldType() {}

	FieldType(Class<?> linkClass) {
		this.linkClass = linkClass;
	}

	public static FieldType resolveByName(String name) {
		return Arrays.stream(FieldType.values()).filter(it -> it.name().equals(name)).findFirst()
				.orElseGet(() -> {
					log.warn("UnResolve field Type by name: " + name);
					return FieldType.STRING;
				});
	}

	public static FieldType resolveType(Object data) {
		if (data instanceof ComponentPropertyValue) {
			final String simpleName = data.getClass().getSimpleName();
			return resolveByName(simpleName);
		} else if (data instanceof Number) {
			return FieldType.NUMBER;
		} else if (data instanceof String) {
			return FieldType.STRING;
		} else if (data instanceof Enum) {
			return FieldType.ENUM;
		} else if (data instanceof Date) {
			return FieldType.DATE;
		} else if (data instanceof JSONObject || data instanceof JSONArray) {
			return FieldType.JSON;
		}
		return null;
	}

	public static FieldType resolveDefaultType(Object data) {
		return Optional.ofNullable(resolveType(data)).orElse(FieldType.STRING);
	}
}
