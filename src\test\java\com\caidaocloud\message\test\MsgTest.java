package com.caidaocloud.message.test;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * MsgSendEmailTest
 *
 * <AUTHOR>
 * @date 2022/6/9 下午3:25
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
// @SpringBootTest(classes = )
public class MsgTest {

	// @Test
	// public void ruleList(){
	// 	List<KeyValue> collect = Arrays.stream(NoticeRule.values()).map(type -> {
	// 		KeyValue kv = new KeyValue();
	// 		kv.setText(type.name);
	// 		kv.setValue(type);
	// 		return kv;
	// 	}).collect(Collectors.toList());
	// }
  /*  @Resource
    private TemplateService templateService;
    @Autowired
    TemplateController templateController;
    @Autowired
    MsgConfigController msgConfigController;

    @Autowired
    PlaceholderDomainService placeholderDo;
    @Test
    public void testDoMsg() {
        MsgDto msgDto = new MsgDto();
        msgDto.setChannel(Lists.list("4"));
        msgDto.setTenantId("33");
        msgDto.setUserId(0L);

        EmailMsgDto emailMsgDto = new EmailMsgDto();
        emailMsgDto.setCc("<EMAIL>");
        emailMsgDto.setContent("hello caidao,hello disney");
        emailMsgDto.setTo("<EMAIL>");
        emailMsgDto.setSubject("taoge test");

        msgDto.setEmailMsg(emailMsgDto);
        System.out.println(FastjsonUtil.toJson(msgDto));

        try {
            SecurityUserInfo userInfo = new SecurityUserInfo();
            String tenantId = msgDto.getTenantId();
            userInfo.setTenantId(tenantId);
            Long userId = msgDto.getUserId();
            userInfo.setUserId(userId);
            SecurityUserUtil.setSecurityUserInfo(userInfo);

            UserInfo user = new UserInfo();
            user.setTenantId(tenantId);
            user.doSetUserId(userId);
            UserContext.setCurrentUser(user);

            // msgService.saveOrUpdateObj(msgDto);
        } catch (Exception e){
            log.error("process plain msg err,{}", e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }

    @Before
    public void be(){
        SecurityUserInfo userInfo = new SecurityUserInfo();
        String tenantId = "33";
        userInfo.setTenantId(tenantId);
        Long userId = 0l;
        userInfo.setUserId(userId);
        SecurityUserUtil.setSecurityUserInfo(userInfo);

        UserInfo user = new UserInfo();
        user.setTenantId(tenantId);
        user.doSetUserId(userId);
        UserContext.setCurrentUser(user);
    }

    @Test
    public void find(){
        // System.out.println(msgConfigController.detail("1445190525532171"));
        // MsgConfigDto dto = new MsgConfigDto();
        // dto.setBid("1445190525532171");
        //
        // MsgConfigDto configDto = FastjsonUtil.toObject("{\"bid\":\"1450829861746691\",\"templates\":[{\"bid\":\"1450830136080389\",\"name\":\"短信测试01\",\"content\":\"钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱#员工任职信息.工号#vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv不啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵不啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵不啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵不啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵#员工任职信息.姓名##员工任职信息.岗位##员工任职信息.姓名拼音##员工任职信息.工号##员工任职信息.所属组织#\",\"category\":{\"text\":\"短信模版\",\"value\":\"0\"},\"code\":null,\"attachFile\":{\"names\":[],\"urls\":[]},\"msgConfig\":\"1450829861746691\",\"variable\":[{\"model\":\"entity.hr.EmpWorkInfo\",\"text\":\"#员工任职信息.工号#\",\"prop\":\"empWorkInfo.workno\",\"name\":\"工号\"},{\"model\":\"entity.hr.EmpWorkInfo\",\"text\":\"#员工任职信息.姓名#\",\"prop\":\"empWorkInfo.name\",\"name\":\"姓名\"},{\"model\":\"entity.hr.EmpWorkInfo\",\"text\":\"#员工任职信息.岗位#\",\"prop\":\"empWorkInfo.postTxt\",\"name\":\"岗位\"},{\"model\":\"entity.hr.EmpWorkInfo\",\"text\":\"#员工任职信息.姓名拼音#\",\"prop\":\"empWorkInfo.ext.namePinyin\",\"name\":\"姓名拼音\"},{\"model\":\"entity.hr.EmpWorkInfo\",\"text\":\"#员工任职信息.所属组织#\",\"prop\":\"empWorkInfo.organizeTxt\",\"name\":\"所属组织\"}],\"copyObject\":null,\"copyMail\":null,\"externalMail\":null}],\"msgType\":\"MANUAL_PUSH\",\"name\":\"12312\",\"notifyObject\":[\"ALL_EMP\"],\"condition\":{\"id\":\"1657268928745_745\",\"type\":\"group\",\"relation\":\"and\",\"children\":[{\"id\":\"1657271173820_699\",\"type\":\"single\",\"relation\":null,\"condition\":{\"name\":\"hr#EmpWorkInfo#empType$dictValue\",\"componentType\":\"DICT_SELECTOR\",\"symbol\":\"EQ\",\"value\":\"45\",\"simpleValue\":\"45\"}}]},\"channel\":[\"0\"],\"triggerDate\":1657296000000,\"sendTime\":1657209720}", MsgConfigDto.class);
        // msgConfigController.save(configDto);

        // TemplateDto templateDto = FastjsonUtil.toObject("{\"name\":\"12\",\"title\":\"23\",\"content\":\"<p>2123213<span style=\\\"color: rgb(16, 129, 246);\\\"><strong>#员工任职信息.所属组织#</strong></span></p>\",\"attachFile\":{\"names\":[\"1.png\",\"1.png\",\"测试.xlsx\",\"测试.xlsx\",\"已签订合同数据导入模版 (7).xlsx\",\"已签订合同数据导入模版 (7).xlsx\",\"已签订合同数据导入模版 (7).xlsx\",\"已签订合同数据导入模版 (7).xlsx\",\"已签订合同数据导入模版 (7).xlsx\",\"已签订合同数据导入模版 (7).xlsx\",\"已签订合同数据导入模版 (7).xlsx\"],\"urls\":[\"/11516-33/24b5k3r355ceaadf2cdc-1.png\",\"/11516-33/a2cek0r5b2cbc6d9fdf5-测试.xlsx\",\"/11516-33/3274k7r727c78adbf777-测试.xlsx\",\"/11516-33/0c0dkcr10dc945dced62-已签订合同数据导入模版 (7).xlsx\",\"/11516-33/e24cker405cd9cddf824-已签订合同数据导入模版 (7).xlsx\",\"/11516-33/d799k6r7bdca7dd0a069-已签订合同数据导入模版 (7).xlsx\",\"/11516-33/855ekfr5f9c52ad58434-已签订合同数据导入模版 (7).xlsx\",\"/11516-33/3879kcr187cc8adaf815-已签订合同数据导入模版 (7).xlsx\",\"/11516-33/bbc4k2r179c870d0c2de-已签订合同数据导入模版 (7).xlsx\",\"/11516-33/130dk3r9a8c111dc40b0-已签订合同数据导入模版 (7).xlsx\"]},\"category\":{\"text\":\"邮件模版\",\"value\":\"1\"},\"variable\":[{\"model\":\"entity.hr.EmpWorkInfo\",\"text\":\"#员工任职信息.所属组织#\",\"prop\":\"empWorkInfo.organizeTxt\",\"name\":\"所属组织\"}],\"bid\":\"1442861048092756\"}", TemplateDto.class);
        // templateController.update(templateDto);
        // System.out.println(msgConfigController.list(FastjsonUtil.toObject("{\"pageNo\":1,\"pageSize\":10}", MsgConfigQueryDto.class)));
        MsgConfigDto configDto = FastjsonUtil.toObject("{\"templates\":[],\"msgType\":\"WORKFLOW_PUSH\",\"name\":\"123\",\"channel\":[\"0\",\"1\"],\"sendTime\":\"\",\"triggerDate\":\"\"}\n", MsgConfigDto.class);
        // Result save = msgConfigController.save(configDto);
        // System.out.println(msgConfigController.list(new MsgConfigQueryDto()));
        // System.out.println(msgConfigController.getEnableMsgConfigList("4"));
        // System.out.println(msgConfigController.enable("4"));
        Result<MsgConfigDetailVo> detail = msgConfigController.detail("1478934252804097");
        System.out.println(detail);
        MsgConfigDto dto = ObjectConverter.convert(detail.getData(), MsgConfigDto.class);
        msgConfigController.enable(dto);
        msgConfigController.copy(dto);
        // MsgConfigDto dto = FastjsonUtil.toObject("{\"bid\":\"1445190525532171\",\"templates\":[{\"bid\":\"1452784293410821\",\"name\":\"未签署提醒\",\"content\":\"你好，#员工任职信息.姓名#，手机号：#员工个人信息.手机号#，合同内容：#模版包.所属业务流程.中文##模版包.所属业务流程.英文#，未签署提醒。。。。。。。。。。。。。\",\"category\":{\"text\":\"短信模版\",\"value\":\"0\"},\"code\":\"contractUrage\",\"attachFile\":{\"names\":[],\"urls\":[]},\"msgConfig\":\"1445190525532171\",\"variable\":[],\"copyObject\":null,\"copyMail\":null,\"externalMail\":null}],\"msgType\":\"MANUAL_PUSH\",\"name\":\"测试推送\",\"notifyObject\":[\"GUARDIAN\"],\"condition\":null,\"channel\":[\"0\"],\"triggerDate\":1657555200000,\"sendTime\":1657551600}", MsgConfigDto.class);
        // msgConfigController.update(dto);
        // System.out.println(msgConfigController.copy(dto));
    }

    @Test
    public void save(){
        MsgConfigDto dto = new MsgConfigDto();
        dto.setName("时间测试");
        dto.setMsgType(NoticeType.MANUAL_PUSH);
        dto.setNotifyObject(Lists.list(NoticeTarget.ASSIGN_EMP, NoticeTarget.EVENT_EMP));
        dto.setFunc("0");
        dto.setRound("0");
        dto.setDay(1);
        dto.setSendTime(1656036561);
        dto.setTriggerDate(1654046746495L);
        dto.setLoop(0);
        dto.setChannel(Lists.list(MsgCategory.APP.getCode(), MsgCategory.MSG.getCode()));
        dto.setEmps(Lists.list(FastjsonUtil.toObject("{\"avatar\":null,\"deptId\":\"1435400986410053\",\"deptDesc\":\"OFN-Financial Systems-OC1\",\"name\":\"员工2995\",\"workno\":\"AC2995\",\"enName\":\"2995\",\"empId\":\"1433847523040195\"}", EmpSimple.class)));
        TemplateDto templateDto = new TemplateDto();
        templateDto.setName("测试模板");
        templateDto.setCategory(creatEnum("0"));
        templateDto.setCopyObject(Lists.list(EmailCopyTarget.HRBP,EmailCopyTarget.LEADER));
        templateDto = FastjsonUtil.toObject("{\"name\":\"短信测试01\",\"code\":null,\"content\":\"钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱#员工任职信息.工号#vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv不啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵不啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵不啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵不啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵#员工任职信息.姓名##员工任职信息.岗位##员工任职信息.姓名拼音##员工任职信息.工号##员工任职信息.所属组织#\",\"category\":{\"text\":\"短信模版\",\"value\":\"1\"},\"variable\":[{\"model\":\"entity.hr.EmpWorkInfo\",\"text\":\"#员工任职信息.工号#\",\"prop\":\"empWorkInfo.workno\",\"name\":\"工号\"},{\"model\":\"entity.hr.EmpWorkInfo\",\"text\":\"#员工任职信息.姓名#\",\"prop\":\"empWorkInfo.name\",\"name\":\"姓名\"},{\"model\":\"entity.hr.EmpWorkInfo\",\"text\":\"#员工任职信息.岗位#\",\"prop\":\"empWorkInfo.postTxt\",\"name\":\"岗位\"},{\"model\":\"entity.hr.EmpWorkInfo\",\"text\":\"#员工任职信息.姓名拼音#\",\"prop\":\"empWorkInfo.ext.namePinyin\",\"name\":\"姓名拼音\"},{\"model\":\"entity.hr.EmpWorkInfo\",\"text\":\"#员工任职信息.所属组织#\",\"prop\":\"empWorkInfo.organizeTxt\",\"name\":\"所属组织\"}],\"attachFile\":{\"names\":[],\"urls\":[]},\"bid\":\"1445718425499657\"}", templateDto.getClass());
        templateDto.setBid(null);

        // templateController.save(templateDto);
        // dto.setTemplates(Lists.list(templateDto));
        String s = "{\"bid\":\"1450829720115201\",\"templates\":[{\"bid\":\"1450829721442306\",\"name\":\"短信测试01\",\"content\":\"钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱#员工任职信息.工号#vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv不啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵不啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵不啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵不啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵#员工任职信息.姓名##员工任职信息.岗位##员工任职信息.姓名拼音##员工任职信息.工号##员工任职信息.所属组织#\",\"category\":{\"text\":\"短信模版\",\"value\":\"0\"},\"code\":null,\"attachFile\":{\"names\":[],\"urls\":[]},\"msgConfig\":\"1450829720115201\",\"variable\":[{\"model\":\"entity.hr.EmpWorkInfo\",\"text\":\"#员工任职信息.工号#\",\"prop\":\"empWorkInfo.workno\",\"name\":\"工号\"},{\"model\":\"entity.hr.EmpWorkInfo\",\"text\":\"#员工任职信息.姓名#\",\"prop\":\"empWorkInfo.name\",\"name\":\"姓名\"},{\"model\":\"entity.hr.EmpWorkInfo\",\"text\":\"#员工任职信息.岗位#\",\"prop\":\"empWorkInfo.postTxt\",\"name\":\"岗位\"},{\"model\":\"entity.hr.EmpWorkInfo\",\"text\":\"#员工任职信息.姓名拼音#\",\"prop\":\"empWorkInfo.ext.namePinyin\",\"name\":\"姓名拼音\"},{\"model\":\"entity.hr.EmpWorkInfo\",\"text\":\"#员工任职信息.所属组织#\",\"prop\":\"empWorkInfo.organizeTxt\",\"name\":\"所属组织\"}],\"copyObject\":null,\"copyMail\":null,\"externalMail\":null}],\"msgType\":\"RENEWAL_NOT_SIGN\",\"name\":\"12312\",\"notifyObject\":[\"EVENT_EMP\"],\"condition\":{\"id\":\"1657606341628_326\",\"type\":\"group\",\"relation\":\"and\",\"children\":[{\"id\":\"1657606518115_341\",\"type\":\"single\",\"relation\":null,\"condition\":{\"name\":\"hr#EmpWorkInfo#empType$dictValue\",\"componentType\":\"DICT_SELECTOR\",\"symbol\":\"IN\",\"value\":[\"75644\",\"75686\"],\"simpleValue\":[\"75644\",\"75686\"]}},{\"id\":\"1657606661401_629\",\"type\":\"single\",\"relation\":null,\"condition\":{\"name\":\"hr#EmpWorkInfo#confirmationStatus\",\"componentType\":\"ENUM\",\"symbol\":\"IN\",\"value\":[\"0\",\"1\"],\"simpleValue\":[\"0\",\"1\"]}},{\"id\":\"1657606669819_573\",\"type\":\"single\",\"relation\":null,\"condition\":{\"name\":\"hr#EmpWorkInfo#company\",\"componentType\":\"COMPANY\",\"symbol\":\"IN\",\"value\":[\"1443611200903178\",\"1433765022021647\"],\"simpleValue\":[\"1443611200903178\",\"1433765022021647\"]}},{\"id\":\"1657606680390_244\",\"type\":\"single\",\"relation\":null,\"condition\":{\"name\":\"hr#EmpWorkInfo#organize\",\"componentType\":\"ORG\",\"symbol\":\"IN\",\"value\":[\"1435400791144704\",\"1435400791120124\"],\"simpleValue\":[\"1435400791144704\",\"1435400791120124\"]}},{\"id\":\"1657606691508_644\",\"type\":\"single\",\"relation\":null,\"condition\":{\"name\":\"hr#EmpWorkInfo#workplace\",\"componentType\":\"WORKPLACE\",\"symbol\":\"IN\",\"value\":[\"1445699483639693\",\"1433762454607873\"],\"simpleValue\":[\"1445699483639693\",\"1433762454607873\"]}}]},\"func\":\"0\",\"round\":null,\"day\":null,\"sendTime\":\"\",\"channel\":[\"0\"],\"triggerDate\":\"\"}";
        //  s = "{\"bid\":\"1445159562516484\",\"templates\":[{\"bid\":\"1445159564883973\",\"name\":\"未签署提醒\",\"content\":\"你好，#员工任职信息.姓名#，手机号：#员工个人信息.手机号#，合同内容：#模版包.所属业务流程.中文##模版包.所属业务流程.英文#，未签署提醒。。。。。。。。。。。。。\",\"category\":{\"text\":\"短信模版\",\"value\":\"0\"},\"code\":\"contractUrage\",\"attachFile\":{\"names\":[],\"urls\":[]},\"msgConfig\":\"1445159562516484\",\"variable\":[],\"copyObject\":null,\"copyMail\":null,\"externalMail\":null}],\"msgType\":\"MANUAL_PUSH\",\"name\":\"推送\",\"notifyObject\":[\"LEADER\"],\"condition\":null,\"channel\":[\"0\"],\"triggerDate\":1656518400000,\"sendTime\":1740924000}";
        dto = FastjsonUtil.toObject(s, MsgConfigDto.class);
        msgConfigController.update(dto);
        // dto.setSendTime((int) (System.currentTimeMillis() / 1000+100    ));
        System.out.println(msgConfigController.detail("1450829720115201"));
        // // templateController.removeDoc("1434516873525258");
        // templateController.removeDoc("1437903168632834");
    }

    EnumSimple creatEnum(String value) {
        EnumSimple simple = new EnumSimple();
        simple.setValue(value);
        return simple;
    }

    @Test
    public void testTem() {
        TemplateDto templateDto = new TemplateDto();
        templateDto.setContent("测试");
        templateDto.setName("测试");
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue("1");
        templateDto.setCategory(enumSimple);
        templateDto.setCode("a");
        templateDto.setTitle("测试");
        templateDto.setCopyObject(Lists.list(EmailCopyTarget.HRBP,EmailCopyTarget.LEADER));
        //
        // EmailMsgDto emailMsgDto = new EmailMsgDto();
        // emailMsgDto.setCc("<EMAIL>");
        // emailMsgDto.setContent("hello caidao,hello disney");
        // emailMsgDto.setTo("<EMAIL>");
        // emailMsgDto.setSubject("taoge test");
        //
        // msgDto.setEmailMsg(emailMsgDto);
        // System.out.println(FastjsonUtil.toJson(msgDto));
        String s = "[{\"model\":\"empWorkInfo\",\"text\":\"#员工任职信息.姓名#\",\"prop\":\"name\"},{\"model\":\"Contract\",\"text\":\"#合同信息.开始日期#\",\"prop\":\"startDate\"}]";
        List<TemplateVariableDto> dtoList = JSONObject.parseArray(s, TemplateVariableDto.class);
        templateDto.setVariable(dtoList);
        try {
            String json = "{\"name\":\"邮件测试\",\"title\":\"邮件测试\",\"content\":\"<p>测试<span style=\\\"color: rgb(16, 129, 246);\\\">#员工任职信息.姓名#&nbsp;&nbsp;</span>&nbsp;测试</p>\",\"attachFile\":{\"names\":[\"1.png\"],\"urls\":[\"/11516-33/9e4cker98fc4fdd8c099-1.png\"]},\"category\":{\"text\":\"邮件模版\",\"value\":\"1\"},\"variable\":[{\"model\":\"entity.hr.EmpWorkInfo\",\"text\":\"#员工任职信息.姓名#\",\"prop\":null,\"name\":null}],\"bid\":\"1435210467678209\"}\n";
            TemplateDto dto = FastjsonUtil.toObject(json, templateDto.getClass());
            templateController.update(dto);
*//*
            // log.info("{}", templateController.save(templateDto));
            log.info(FastjsonUtil.toJson(templateDto));
            TemplateQueryDto templateQueryDto = new TemplateQueryDto();
            templateQueryDto.setCategory(creatEnum("1"));
            PageResult<TemplateVo> all = templateController.getList(templateQueryDto);
            log.info("{}", all);
            // all.getData().get(0).setName("name changed");
            // log.info(FastjsonUtil.toJson(all.getData().get(0)));

            // templateController.update(JsonEnhanceUtil.toObject(all.getData().get(0), TemplateDto.class));

            // String s = ;

            // PlaceholderDo test = new PlaceholderDo();
            // test.setCustom(false);
            // test.setModel("entity.hr.EmpPrivateInfo");
            // test.setText("#员工个人信息.手机号#");
            // test.setName("手机号");
            // test.setProp("empPrivateInfo.phone");
            //
            // placeholderDo.save(test);
            Result<PageResult<MsgConfigListVo>> list = msgConfigController.list(new MsgConfigQueryDto());
            log.info("list result \n>>>>>>>>{}", FastjsonUtil.toJson(list));
            String bid = list.getData().getItems().get(0).getBid();
            // msgConfigController.enable(bid);
            Result<MsgConfigDetailVo> detail1 = msgConfigController.detail(bid);

            log.info("detail \nresult>>>>>>>>>>>{}", detail1);

            bid = list.getData().getItems().get(1).getBid();
            // msgConfigController.disable(bid);
            Result<MsgConfigDetailVo> detail2 = msgConfigController.detail(bid);
            log.info("detail \nresult>>>>>>>>>>>{}", detail2);

            System.out.println(detail1.getData().equals(detail2.getData()));*//*

            // msgConfigController.copy(bid);
        } catch (Exception e){
            log.error("process plain msg err,{}", e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }


    }
    @Test
    public void timestamp(){
        // System.out.println(Integer.MAX_VALUE>2655942400);
    }*/
}
