package com.caidaocloud.message.service.record.provider;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.service.record.bean.LogRecord;
import com.caidaocloud.message.service.record.bean.UserAccountInfo;
import com.caidaocloud.message.service.record.dto.LogRecordQueryDto;
import com.caidaocloud.message.service.record.vo.LogRecordVo;

import java.util.List;

/**
 * created by: FoAng
 * create time: 28/5/2024 3:20 下午
 */
public interface RecordRepository {

    void saveRecord(LogRecord message, UserAccountInfo accountInfo);

    PageResult<LogRecordVo> pageRecord(LogRecordQueryDto queryDto);

    List<LogRecordVo> listRecord(LogRecordQueryDto queryDto);

    <T> List<LogRecordVo> transfer(List<T> list);
}
