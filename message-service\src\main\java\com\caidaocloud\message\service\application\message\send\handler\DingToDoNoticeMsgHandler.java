package com.caidaocloud.message.service.application.message.send.handler;

import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.send.dto.MessageContext;
import com.caidaocloud.message.service.application.msg.dto.DingtalkToDoNoticeDto;
import com.caidaocloud.message.service.application.msg.dto.MsgDto;
import com.caidaocloud.message.service.interfaces.vo.template.TemplateVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Description 钉钉待办通知消息
 * @Date 2023/2/27 下午5:27
 * @Version 1.0
 **/
@Slf4j
@Service
public class DingToDoNoticeMsgHandler implements IMsgHandler  {

    @Override
    public String getHandlerType() {
        return "5";
    }

    @Override
    public void doMsgHandler(MessageContext context) {

        Map<String, NotifierDto> receiverMap = context.getReceiverMap();
        List<String> phones = new ArrayList<>();
        context.getEmpList().forEach(sysEmpId -> {
            NotifierDto notifierDto = receiverMap.get(sysEmpId);
            if(null == notifierDto || StringUtil.isEmpty(notifierDto.getPhone())){
                log.warn("createDingToDoNoticeMessage sysEmpId={} is not exist Notifier={}", sysEmpId, FastjsonUtil.toJson(notifierDto));
                return;
            }

            addPhoneList(phones, notifierDto);
        });

        // 处理监护人
        context.getOtherObjList().forEach(otherObj -> {
            if(null == otherObj || StringUtil.isEmpty(otherObj.getPhone())){
                log.warn("createDingToDoNoticeMessage otherObj is not exist other Notifier={}", FastjsonUtil.toJson(otherObj));
                return;
            }

            addPhoneList(phones, otherObj);
        });

        createDingToDoNoticeMessage(context.getMsgDto(), context.getTemplate(), phones);
    }

    public void createDingToDoNoticeMessage(MsgDto msgDto, TemplateVo template, List<String> phoneList) {
        phoneList = phoneList.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(phoneList)) {
            log.warn("The createDingToDoNoticeMessage mobile phone number collection of ding recipients is empty.");
            return;
        }

        // 手机号去重
        distinctList(phoneList);

        DingtalkToDoNoticeDto message = new DingtalkToDoNoticeDto();
        message.setSubject(template.getTitle());
//        message.setUrl(template.getTemplateUrl());
        message.setDescription(template.getContent());
        List<PhoneSimple> phones = phoneList.stream().map(pValue -> {
            PhoneSimple phoneSimple = new PhoneSimple();
            phoneSimple.setValue(pValue);
            return phoneSimple;
        }).collect(Collectors.toList());
        message.setMobiles(phones);
        log.info("createDingToDoNoticeMessage data body=[{}]", FastjsonUtil.toJson(message));
        msgDto.setDingtalkToDoNoticeDto(message);
    }

}

 
    
    
    
    