package com.caidaocloud.message.service.application.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */

public enum MsgFromEnum {

    ONBOARDING("0", "入职", "Onboarding"),

    ESGIN("1", "文件签署","Esgin"),

    CONTRACT("2", "合同签署", "contract"),

    CONTRACTNEW("3", "合同新签", "ContractNew"),

    CONTRACTRENEW("4", "合同续签", "ContractRenew"),

    WORKFLOW("5", "工作流", "workflow"),

    TERMINATION("6", "离职", "termination");

    private String index;
    private String name;
    private String code;

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    MsgFromEnum(String index, String name, String code) {
        this.index = index;
        this.name = name;
        this.code = code;
    }
    public static MsgFromEnum getEnumByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        MsgFromEnum[] values = MsgFromEnum.values();
        for (MsgFromEnum msgFromEnum : values) {
            if (msgFromEnum.code.equals(code)) {
                return msgFromEnum;
            }
        }
        return null;
    }

}
