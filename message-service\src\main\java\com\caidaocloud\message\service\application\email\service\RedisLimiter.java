package com.caidaocloud.message.service.application.email.service;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;

/**
 * redis限流
 * 比如限制邮箱的发送频率
 */
@Service
public class RedisLimiter {
    @Resource
    private RedisTemplate redisTemplate;

    private static final Long SUCCESS_FLAG = 1L;

    /**
     * 判断是否允许访问
     * @param id 这次获取令牌桶的id
     * @param rate 每秒填充速率
     * @param capacity 令牌桶最大容量
     * @param tokens 每次访问消耗几个令牌
     * @return true 允许访问 false 不允许访问
     */
    public boolean isAllowed(String id, int rate, int capacity, int tokens){
        RedisScript<Long> redisScript = new DefaultRedisScript<>(SCRIPT, Long.class);

        Object result = redisTemplate.execute(redisScript,
                getKey(id), rate, capacity,
                Instant.now().getEpochSecond(), tokens);
        return SUCCESS_FLAG.equals(result);
    }

    /**
     * 判断是否允许访问
     * @param id 这次获取令牌桶的id
     * @param rate 每秒填充速率
     * @param capacity 令牌桶最大容量
     * @param tokens 每次访问消耗几个令牌
     * @param maxCount 周期时间内最大访问量
     * @return true 允许访问 false 不允许访问
     */
    public boolean isAllowed(String id, int rate, int capacity, int tokens, int maxCount){
        RedisScript<Long> redisScript = new DefaultRedisScript<>(MAX_COUNT_SCRIPT, Long.class);

        Object result = redisTemplate.execute(redisScript,
                getMaxCountKey(id), rate, capacity,
                Instant.now().getEpochSecond(), tokens, maxCount);
        return SUCCESS_FLAG.equals(result);
    }

    private List<String> getKey(String id){
        String prefix = "limiter:" + id;
        String tokenKey = prefix + ":tokens";
        String timestampKey = prefix + ":timestamp";
        return Arrays.asList(tokenKey, timestampKey);
    }

    private List<String> getMaxCountKey(String id){
        String prefix = "limiter:" + id;
        String tokenKey = prefix + ":tokens";
        String timestampKey = prefix + ":timestamp";
        String countKey = prefix + ":count";
        return Arrays.asList(tokenKey, timestampKey, countKey);
    }

    private static final String MAX_COUNT_SCRIPT = "local tokens_key = KEYS[1]\n" +
        "local timestamp_key = KEYS[2]\n" +
        "local count_key = KEYS[3]\n" +
        "local rate = tonumber(ARGV[1])\n" +
        "local capacity = tonumber(ARGV[2])\n" +
        "local now = tonumber(ARGV[3])\n" +
        "local requested = tonumber(ARGV[4])\n" +
        "local min_max = tonumber(ARGV[5])\n" +
        "local fill_time = capacity/rate\n" +
        "local ttl = math.floor(fill_time*2)\n" +
        "local has_count = tonumber(redis.call('get', count_key))\n" +
        "if has_count == nil then\n" +
        "  has_count = 0\n" +
        "end\n" +
        "if has_count >= min_max then\n" +
        "return 0\n" +
        "end\n" +
        "local last_tokens = tonumber(redis.call('get', tokens_key))\n" +
        "if last_tokens == nil then\n" +
        "  last_tokens = capacity\n" +
        "end\n" +
        "local last_refreshed = tonumber(redis.call('get', timestamp_key))\n" +
        "if last_refreshed == nil then\n" +
        "  last_refreshed = 0\n" +
        "end\n" +
        "local diff_time = math.max(0, now-last_refreshed)\n" +
        "local filled_tokens = math.min(capacity, last_tokens+(diff_time*rate))\n" +
        "local allowed = filled_tokens >= requested\n" +
        "local new_tokens = filled_tokens\n" +
        "local allowed_num = 0\n" +
        "if allowed then\n" +
        "  new_tokens = filled_tokens - requested\n" +
        "  allowed_num = 1\n" +
        "end\n" +
        "if ttl > 0 then\n" +
        "  redis.call('setex', tokens_key, ttl, new_tokens)\n" +
        "  redis.call('setex', timestamp_key, ttl, now)\n" +
        "end\n" +
        "local count_ttl = tonumber(redis.call('ttl',count_key))\n" +
        "if count_ttl < 0 then\n" +
        "  count_ttl = fill_time\n" +
        "end\n" +
        "redis.call('setex', count_key,count_ttl , has_count+1)\n" +
        "return allowed_num\n",
            SCRIPT = "local tokens_key = KEYS[1]\n" +
        "local timestamp_key = KEYS[2]\n" +
        "local rate = tonumber(ARGV[1])\n" +
        "local capacity = tonumber(ARGV[2])\n" +
        "local now = tonumber(ARGV[3])\n" +
        "local requested = tonumber(ARGV[4])\n" +
        "local fill_time = capacity/rate\n" +
        "local ttl = math.floor(fill_time*2)\n" +
        "local last_tokens = tonumber(redis.call('get', tokens_key))\n" +
        "if last_tokens == nil then\n" +
        "  last_tokens = capacity\n" +
        "end\n" +
        "local last_refreshed = tonumber(redis.call('get', timestamp_key))\n" +
        "if last_refreshed == nil then\n" +
        "  last_refreshed = 0\n" +
        "end\n" +
        "local diff_time = math.max(0, now-last_refreshed)\n" +
        "local filled_tokens = math.min(capacity, last_tokens+(diff_time*rate))\n" +
        "local allowed = filled_tokens >= requested\n" +
        "local new_tokens = filled_tokens\n" +
        "local allowed_num = 0\n" +
        "if allowed then\n" +
        "  new_tokens = filled_tokens - requested\n" +
        "  allowed_num = 1\n" +
        "end\n" +
        "if ttl > 0 then\n" +
        "  redis.call('setex', tokens_key, ttl, new_tokens)\n" +
        "  redis.call('setex', timestamp_key, ttl, now)\n" +
        "end\n" +
        "return allowed_num\n";
}