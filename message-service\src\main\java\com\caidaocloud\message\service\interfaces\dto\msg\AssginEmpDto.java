package com.caidaocloud.message.service.interfaces.dto.msg;

import com.caidaocloud.condition.tree.ConditionTree;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.service.interfaces.dto.template.TemplateDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("指定系统人员dto")
public class AssginEmpDto {
    @ApiModelProperty("指定员工对象")
    private EmpSimple emp;
}
