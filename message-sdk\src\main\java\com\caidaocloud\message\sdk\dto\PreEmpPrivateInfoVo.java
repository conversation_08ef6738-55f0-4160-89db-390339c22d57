package com.caidaocloud.message.sdk.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 候选人基本信息VO
 */
@Data
public class PreEmpPrivateInfoVo {
    // 候选人ID
    private String empId;
    // 员工姓名
    private String name;
    // 员工英文名
    private String enName;
    // 性别
    private DictSimple sex;
    // 国籍
    private DictSimple nationality;
    // 民族
    private DictSimple nation;
    // 籍贯
    private String nativePlace;
    // 户口类型
    private DictSimple familyType;
    // 户籍地址
    private String permanentAddress;
    // 出生日期
    private Long birthDate;
    // 年龄
    private Integer divisionAge;
    // 婚姻状态
    private EnumSimple maritalStatus;
    // 生育状态
    private EnumSimple fertilityStatus;
    // 政治面貌
    private DictSimple politicalOutlook;
    // 手机号
    private PhoneSimple phone;
    // 员工个人邮箱
    private String email;
    // 通讯地址
    private String postalAddress;
    // 证件类型
    private EnumSimple cardType;
    // 证件号
    private String cardNo;
    // 证件有效日期
    private Long cardEffectiveDate;
    // 是否残疾
    private Boolean disability;
    // 监护人姓名
    private String guardianName;
    // 监护人手机
    private PhoneSimple guardianPhone;
    // 监护人邮箱
    private String guardianEmail;
    // 扩展字段
    private Map ext = new LinkedHashMap();
}
