package com.caidaocloud.record.core.beans;

import lombok.Builder;
import lombok.Data;


@Data
@Builder
public class LogRecordOps {

	/**
	 * 事件成功模板
	 */
	private String successLogTemplate;

	/**
	 * 失败模板
	 */
	private String failLogTemplate;

	/**
	 * 操作Id
	 */
	private String operatorId;

	/**
	 * 业务单号
	 */
	private String bizNo;

	/**
	 * 操作平台
	 */
	private String platform;

	/**
	 * 操作来源
	 */
	private String source;

	/**
	 * 操作类型
	 */
	private String category;

	/**
	 * 日志记录详情
	 */
	private String detail;

	/**
	 * 菜单记录
	 */
	private String uri;

	/**
	 * 操作菜单
	 */
	private String menu;

	/**
	 * 操作结果状态
	 */
	private String operatorStatus;

	/**
	 * 记录触发条件
	 */
	private String condition;

	/**
	 * 租户ID 登入用
	 */
	private String tenantId;
}
