package com.caidaocloud.message.service.application.template.service.analysis.datasource;

import com.caidaocloud.hr.service.vo.organization.company.org.OrgVo;
import com.caidaocloud.hr.service.vo.organization.post.PostVo;
import com.caidaocloud.message.service.application.feign.HrFeignClient;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class EmpTransferDataReader implements IEmpDataReader {
    @Resource
    private HrFeignClient hrFeignClient;
    @Override
    public String getReaderType() {
        return "entity.hr.EmpTransferRecord";
    }

    @Override
    public void getData(Map<String, Object> map, String identifier, String empId, Map<String, String> ext, int type) {
        Map data = new HashMap();
        if(null != ext && !ext.isEmpty()){
            data.putAll(ext);
        }
        map.put(identifier, data);
        if(StringUtil.isEmpty(empId) || 1 == type){
            return;
        }

        Result<Map> result = hrFeignClient.getEmpTransferRecord(empId);
        if(null == result || !result.isSuccess() || null == result.getData()){
            return;
        }

        Map record = result.getData();
        if(record.isEmpty()){
            return;
        }

        List<Map> propList = (List) record.get("data");
        if(null == propList || propList.isEmpty()){
            return;
        }
        Object eDate = record.get("effectiveDate");
        Long effectiveDate = null == eDate || StringUtil.isEmpty(eDate) ? System.currentTimeMillis() : Long.parseLong(eDate.toString());
        propList.forEach(prop -> {
            String model = (String) prop.get("type"), property = (String) prop.get("property");
            record.put(model + "_" + property, prop);
            if("work".equals(model) && "organize".equals(property)){
                String afterOrganize = (String) prop.get("after");
                if(StringUtil.isEmpty(afterOrganize)){
                    record.put("afterOrganize", new OrgVo());
                    return;
                }

                Result<OrgVo> orgData = hrFeignClient.getOrgById(afterOrganize, effectiveDate);
                record.put("afterOrganize", null == orgData || !orgData.isSuccess() || null == orgData.getData()
                        ? new OrgVo() : orgData.getData());
            } else if("work".equals(model) && "post".equals(property)){
                String afterPost = (String) prop.get("after");
                if(StringUtil.isEmpty(afterPost)){
                    record.put("afterPost", new PostVo());
                    return;
                }

                Result<PostVo> postData = hrFeignClient.getPost(afterPost, effectiveDate);
                record.put("afterPost", null == postData || !postData.isSuccess() || null == postData.getData()
                        ? new PostVo() : postData.getData());
            }
        });
        record.remove("data");
        data.putAll(record);
    }
}
