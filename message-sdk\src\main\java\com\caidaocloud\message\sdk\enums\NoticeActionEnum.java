package com.caidaocloud.message.sdk.enums;

import com.caidaocloud.util.StringUtil;
import lombok.Getter;

import java.util.Arrays;

/**
 * 消息操作类型
 * created by: FoAng
 * create time: 16/8/2024 4:29 下午
 */
@Getter
public enum NoticeActionEnum {

    NONE("无", "NONE"),

    VIEW("查看详情", "VIEW"),

    APPROVAL("审批", "APPROVAL"),

    REDIRECT("重定向", "REDIRECT");

    final String label;

    final String value;

    NoticeActionEnum(String label, String value) {
        this.label = label;
        this.value = value;
    }

    public static NoticeActionEnum of(String value) {
        return Arrays.stream(values()).filter(it -> StringUtil.isNotEmpty(value) && it.value.equals(value))
                .findFirst().orElse(NONE);
    }
}
