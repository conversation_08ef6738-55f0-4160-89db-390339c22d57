package com.caidaocloud.message.service.application.log.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.service.application.common.enums.MsgType;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import lombok.Data;

/**
 * 消息日志
 *
 * <AUTHOR>
 */
@Data
public class MessageLogRecordDto {

    /**
     * 事件人Id(当事人：张三入职)
     */
    private String empId;

    /**
     * 事件人工号
     */
    private String workno;

    /**
     * 事件人姓名
     */
    private String name;

    /**
     * 事件人类型
     * 0:员工；1:候选人；2:外部人员; 3: 角色
     */
    private String type;

    /**
     * 消息流水号
     */
    private String mdc;

    /**
     * 消息来源
     */
    private String msgFrom;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息配置
     */
    private String msgConfig;

    /**
     * 发送途径
     */
    private String channel;


    /**
     * 发送状态
     */
    private EnumSimple status;



}
