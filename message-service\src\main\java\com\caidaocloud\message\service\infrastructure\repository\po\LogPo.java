package com.caidaocloud.message.service.infrastructure.repository.po;

import lombok.Data;
import lombok.experimental.Accessors;
// import org.mongodb.morphia.annotations.*;
//
// @Entity(value = "oplog", noClassnameStored = true)
// @Indexes({
//     @Index(fields = @Field("id"), background = true),
//     @Index(
//         fields = {@Field("menu"), @Field("model")},
//         options = @IndexOptions(background = true, name = "m_m", unique = true, disableValidation = true))
// })
@Data
@Accessors(chain = true)
public class LogPo {
    // @Id
    private String id;
    // ip
    private String ip;
    // 模块
    private String model;
    // url
    private String url;
    // 接口菜单
    private String menu;
    // 新增，删除，修改，查询;操作类型
    private String type;
    // mdc
    private String mdc;
    // 操作结果
    private String result;
    // 操作的类
    private String className;
    // 操作的方法
    private String methodName;
    // 请求的参数
    private String params;
    // 执行状态，成功 true，false 失败
    private boolean success = true;
    // http 响应状态码
    private int status;
    // 接口耗时间
    private Long time;
    private String tenantId;
    private long createTime;
    private String createBy;
    private long updateTime;
    private String updateBy;
}
