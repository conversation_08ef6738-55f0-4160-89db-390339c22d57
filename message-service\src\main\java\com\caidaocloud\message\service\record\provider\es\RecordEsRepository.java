package com.caidaocloud.message.service.record.provider.es;

import com.caidaocloud.dto.FilterElement;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.service.record.bean.LogEsRecord;
import com.caidaocloud.message.service.record.bean.LogRecord;
import com.caidaocloud.message.service.record.bean.UserAccountInfo;
import com.caidaocloud.message.service.record.dto.LogRecordQueryDto;
import com.caidaocloud.message.service.record.provider.RecordRepository;
import com.caidaocloud.message.service.record.vo.LogRecordVo;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.zxp.esclientrhl.repository.PageList;
import org.zxp.esclientrhl.repository.PageSortHighLight;
import org.zxp.esclientrhl.repository.Sort;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 28/5/2024 3:29 下午
 */
@Slf4j
@AllArgsConstructor
public class RecordEsRepository implements RecordRepository {

    private LogEsRepository logEsRepository;

    private CusEsRepository cusEsRepository;

    @Override
    public void saveRecord(LogRecord record, UserAccountInfo accountInfo) {
        try {
            if (cusEsRepository.createEsIndex()) {
                LogEsRecord esRecord = BeanUtil.convert(record, LogEsRecord.class);
                esRecord.setOperatorInfo(accountInfo);
                logEsRepository.save(esRecord);
            }
        } catch (Exception e) {
            log.error("[record] save to es repository error, msg:{}", e.getMessage(), e);
        }
    }

    @Override
    public PageResult<LogRecordVo> pageRecord(LogRecordQueryDto queryDto) {
        int currentPage = queryDto.getPageNo();
        int pageSize = queryDto.getPageSize();
        PageSortHighLight psh = new PageSortHighLight(currentPage, pageSize);
        String sorter = "createTime";
        Sort.Order order = new Sort.Order(SortOrder.DESC, sorter);
        psh.setSort(new Sort(order));
        PageList<LogEsRecord> pageList;
        try {
            pageList = logEsRepository.search(buildQuery(queryDto), psh);
            return new PageResult<>(transfer(pageList.getList()), pageList.getCurrentPage(), pageList.getPageSize(), (int) pageList.getTotalElements());
//            return new Page<LogRecordVo>(pageList.getCurrentPage(), pageList.getPageSize(), pageList.getTotalElements())
//                    .setRecords(transfer(pageList.getList()));
        } catch (Exception e) {
            log.error("[record] es query error", e);
        }
        return new PageResult<>(new ArrayList<>(), queryDto.getPageNo(), queryDto.getPageSize(), 0);
    }

    @Override
    public List<LogRecordVo> listRecord(LogRecordQueryDto queryDto) {
        try {
            return transfer(logEsRepository.search(buildQuery(queryDto)));
        } catch (Exception e) {
            log.error("[record] es query list error", e);
        }
        return Lists.newArrayList();
    }

    @Override
    public <T> List<LogRecordVo> transfer(List<T> list) {
        return list.stream().map(it -> {
            UserAccountInfo accountInfo = FastjsonUtil.convertObject(FastjsonUtil.convertObject(it, Map.class).get("operatorInfo"), UserAccountInfo.class);
            LogRecordVo vo = FastjsonUtil.convertObject(it, LogRecordVo.class);
            vo.setOperatorAccount(accountInfo.getAccount());
            vo.setOperatorEmail(accountInfo.getEmail());
            vo.setOperatorMobile(accountInfo.getMobNum());
            vo.setOperatorName(accountInfo.getUserName());
            vo.setOperatorWorkNo(accountInfo.getWorkNo());
            return vo;
        }).collect(Collectors.toList());
    }


    private QueryBuilder buildQuery(LogRecordQueryDto queryDto) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (StringUtil.isNotEmpty(queryDto.getKeyword())) {
            BoolQueryBuilder keywordQuery = QueryBuilders.boolQuery();
            keywordQuery.should(QueryBuilders.matchPhraseQuery("operatorInfo.userName", queryDto.getKeyword()))
                    .should(QueryBuilders.matchPhraseQuery("operatorInfo.account", queryDto.getKeyword()))
                    .should(QueryBuilders.matchPhraseQuery("operatorInfo.workNo", queryDto.getKeyword()))
                    .minimumShouldMatch(1);
            queryBuilder.must(keywordQuery);
        }

        if (StringUtil.isNotEmpty(queryDto.getMenu())) {
            QueryBuilder menuQuery = QueryBuilders.matchPhraseQuery("menu", queryDto.getMenu());
            queryBuilder.must(menuQuery);
        }

        if (queryDto.getStartTime() != null && queryDto.getEndTime() != null) {
            QueryBuilder timeQuery = QueryBuilders.rangeQuery("createTime")
                    .from(queryDto.getStartTime().getTime()).to(queryDto.getEndTime().getTime());
            queryBuilder.must(timeQuery);
        }

        List<FilterElement> filters = queryDto.getFilters();
        if (filters != null) {
            for (FilterElement filter : filters) {
                if (filter.getValue() != null) {
                    if ("operatorPlatform".equals(filter.getProp())) {
                        String[] split = String.valueOf(filter.getValue()).split(",");
                        QueryBuilder platformQuery = QueryBuilders.termsQuery("operatorPlatform", split);
                        queryBuilder.must(platformQuery);
                    }
                    if ("action".equals(filter.getProp())) {
                        BoolQueryBuilder keywordQuery = QueryBuilders.boolQuery();
                        keywordQuery.should(QueryBuilders.matchPhraseQuery("action", filter.getValue()))
                                .should(QueryBuilders.matchPhraseQuery("detail", filter.getValue()))
                                .minimumShouldMatch(1);
                        queryBuilder.must(keywordQuery);
                    }
                    // 状态筛选
                    if ("status".equals(filter.getProp())) {
                        queryBuilder.must(QueryBuilders.termQuery("status", filter.getValue()));
                    }
                }
            }
        }

        if (StringUtil.isNotEmpty(queryDto.getCategory())) {
            QueryBuilder menuQuery = QueryBuilders.termQuery("category", queryDto.getCategory());
            queryBuilder.must(menuQuery);
        }

        return queryBuilder;
    }
}
