package com.caidaocloud.message.service.application.msg.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Description 钉钉待办任务与审批任务关联表
 * @Date 2023/3/1 下午4:54
 * @Version 1.0
 **/
@Data
@Accessors(chain = true)
public class DingWorkflowNoticeDto {

    /**
     * 钉钉待办任务与审批任务关联表的主键ID
     */
    private String id;


    /**
     * 租户id
     */
    private String tenantId;


    /**
     * 才到流程业务key
     */
    private String businessKey;

    /**
     * 才到流程任务id
     */
    private String taskId;

    /**
     * 第三方id 钉钉任务id
     */
    private String thirdId;

    /**
     * 类型 钉钉:1 企业微信:2 飞书:3
     */
    private Integer type;

}

 
    
    
    
    