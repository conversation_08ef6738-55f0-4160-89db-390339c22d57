package com.caidaocloud.message.service.application.notice.service;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.sdk.enums.*;
import com.caidaocloud.message.service.application.common.constant.LangCodeConstant;
import com.caidaocloud.message.service.application.msg.dto.NoticeMessageDto;
import com.caidaocloud.message.service.application.notice.dto.NoticeMsgDto;
import com.caidaocloud.message.service.domain.notice.entity.NoticeMessageDo;
import com.caidaocloud.message.service.domain.notice.service.NoticeMessageDomainService;
import com.caidaocloud.message.service.domain.notice.sse.SseEmitterFactory;
import com.caidaocloud.message.service.infrastructure.repository.po.NoticeMessagePo;
import com.caidaocloud.message.service.infrastructure.repository.po.NoticeTaskOperateDto;
import com.caidaocloud.message.service.infrastructure.utils.LangUtil;
import com.caidaocloud.message.service.interfaces.vo.msg.*;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 15/8/2024 3:06 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class NoticeMessageService {

    private SseEmitterFactory sseEmitterFactory;

    private NoticeMessageDomainService noticeMessageDomainService;

    public PageResult<NoticeMessageVo> page(NoticeMsgDto dto) {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        PageResult<NoticeMessagePo> pageResult = noticeMessageDomainService.pageByExtData(userInfo.getEmpId(), dto);
        return new PageResult<>(pageResult.getItems().stream().map(this::convertNoticeVo)
                .collect(Collectors.toList()), pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    public NoticeUnReadCountVo getUnReadCount() {
        Map<String, Integer> typeCount = noticeMessageDomainService.getTypeUnReadCount();
        NoticeUnReadCountVo countVo = FastjsonUtil.convertObject(typeCount, NoticeUnReadCountVo.class);
        Map<String, Integer> businessCount = noticeMessageDomainService.getBusinessUnReadCount();
        NoticeBusinessCountVo businessCountVo = FastjsonUtil.convertObject(businessCount, NoticeBusinessCountVo.class);
        countVo.setBusiness(businessCountVo);
        return countVo;
    }

    public NoticeBusinessCountVo getBusinessUnReadCount() {
        Map<String, Integer> businessCount = noticeMessageDomainService.getBusinessUnReadCount();
        return FastjsonUtil.convertObject(businessCount, NoticeBusinessCountVo.class);
    }

    public void deleteById(String bid) {
        noticeMessageDomainService.delete(bid);
    }

    public NoticeDetailVo detailVo(String bid) {
        NoticeMessagePo messageDo = noticeMessageDomainService.getById(bid);
        PreCheck.preCheckArgument(messageDo == null, "数据不存在");
        markReadAction(Lists.newArrayList(messageDo.getBid()), null, true);
        return convertNoticeDetailVo(messageDo);
    }

    public void markReadAction(List<String> bids, String businessType, boolean markRead) {
        PreCheck.preCheckArgument(CollectionUtils.isEmpty(bids) && StringUtil.isEmpty(businessType), LangUtil.getMsg(LangCodeConstant.code_70007));
        noticeMessageDomainService.markReadAction(bids, businessType, markRead);
    }

    public void markReadAction(String businessId, boolean markRead) {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        NoticeMsgDto dto = new NoticeMsgDto();
        dto.setBusinessId(businessId);
        PageResult<NoticeMessagePo> pageResult = noticeMessageDomainService.pageByExtData(userInfo.getEmpId(), dto);
        if (pageResult != null && !CollectionUtils.isEmpty(pageResult.getItems())) {
            List<String> bids = pageResult.getItems().stream().map(NoticeMessagePo::getBid).collect(Collectors.toList());
            markReadAction(bids, null, markRead);
        }
    }

    public void persistAllEsData() {
        noticeMessageDomainService.persistAllEsData();
    }

    /**
     * 发送系统消息
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendNoticeMessage(NoticeMessageDto dto) {
        List<NoticeMessageDo> noticeMessageDos = buildNoticeData(dto);
        for (NoticeMessageDo noticeMessageDo : noticeMessageDos) {
            noticeMessageDomainService.save(noticeMessageDo);
            SseEventVo sseEventVo = new SseEventVo();
            sseEventVo.setEventType(SseEventEnum.FETCH);
            sseEventVo.setContent(FastjsonUtil.toJson(noticeMessageDo));
            sendSseNotice(Long.valueOf(noticeMessageDo.getReceiver().getEmpId()),
                    sseEventVo, true);
        }
    }

    public NoticeMessageVo convertNoticeVo(NoticeMessagePo messageDo) {
        NoticeMessageVo messageVo = ObjectConverter.convert(messageDo, NoticeMessageVo.class);
        messageVo.setType(NoticeMsgTypeEnum.of(messageDo.getType().getValue()));
        messageVo.setBusinessType(NoticeBusinessEnum.of(messageDo.getBusinessType()));
        messageVo.setContentType(ContentTypeEnum.of(messageDo.getContentType().getValue()));
        messageVo.setAction(NoticeActionEnum.of(messageDo.getAction().getValue()));
        return messageVo;
    }

    public NoticeDetailVo convertNoticeDetailVo(NoticeMessagePo messageDo) {
        NoticeDetailVo detailVo = ObjectConverter.convert(convertNoticeVo(messageDo), NoticeDetailVo.class);
        detailVo.setContent(messageDo.getContent());
        detailVo.setBusinessId(messageDo.getBusinessId());
        detailVo.setExt(Optional.ofNullable(messageDo.getExt()).map(it -> FastjsonUtil.toObject(it,
                new TypeReference<Map<String, Object>>(){})).orElse(Maps.newHashMap()));
        return detailVo;
    }

    public List<NoticeMessageDo> buildNoticeData(NoticeMessageDto dto) {
        Set<String> empIds = dto.getEmpIds();
        if (empIds != null && !empIds.isEmpty()) {
            return empIds.stream().map(it -> buildNoticeData(it, dto)).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    public NoticeMessageDo buildNoticeData(String empId, NoticeMessageDto dto) {
        NoticeMessageDo itemDo = ObjectConverter.convert(dto, NoticeMessageDo.class);
        // 系统消息类型
        EnumSimple typeEnum = new EnumSimple();
        typeEnum.setValue(Optional.ofNullable(dto.getType()).map(NoticeMsgTypeEnum::getValue).orElse(null));
        itemDo.setType(typeEnum);
        // 业务类型
        itemDo.setBusinessType(Optional.ofNullable(dto.getBusinessType()).map(NoticeBusinessEnum::getValue).orElse(null));
        // 文本类型
        EnumSimple contentEnum = new EnumSimple();
        contentEnum.setValue(Optional.ofNullable(dto.getContentType()).map(ContentTypeEnum::getValue).orElse(null));
        itemDo.setContentType(contentEnum);
        // 操作类型
        EnumSimple actionEnum = new EnumSimple();
        actionEnum.setValue(Optional.ofNullable(dto.getAction()).map(NoticeActionEnum::getValue).orElse(null));
        itemDo.setAction(actionEnum);
        itemDo.setMarkRead(false);
        EmpSimple empSimple = new EmpSimple();
        empSimple.setEmpId(empId);
        itemDo.setReceiver(empSimple);
        itemDo.setExt(FastjsonUtil.toJson(dto.getExt()));
        return itemDo;
    }

    /**
     * 标记待办事件消息
     * @param noticeTaskOperateDto
     */
    public void markOperateNotice(NoticeTaskOperateDto noticeTaskOperateDto) {
        NoticeTaskOperateDto.TransferNoticeEvent transferEvent = noticeTaskOperateDto.getTransferNoticeEvent();
        String businessKey = noticeTaskOperateDto.getBusinessKey();
        if (StringUtil.isEmpty(businessKey)) {
            log.error("[notice] businessKey can't find, do nothing");
            return;
        }
        String nodeId = noticeTaskOperateDto.getNodeId();
        boolean endEvent = noticeTaskOperateDto.isEndEvent();
        List<NoticeMessagePo> noticeList = noticeMessageDomainService.listTodoByBusinessId(businessKey);
        if (CollectionUtils.isEmpty(noticeList)) {
            log.info("[notice] not found target businessKey not read todo msg, businessKey:{}", businessKey);
            return;
        }
        List<String> noticeBids = noticeList.stream().filter(it -> {
            Map<String, Object> extMap = FastjsonUtil.toObject(it.getExt(), new TypeReference<Map<String, Object>>(){});
            String itemNodeId = Objects.toString(extMap.get("nodeId"));
            return endEvent || StringUtils.isEmpty(nodeId) || itemNodeId.equals(nodeId);
        }).map(NoticeMessagePo::getBid).collect(Collectors.toList());
        if (transferEvent != null && transferEvent.getTransferTo() != null) {
            String operateEmpId = transferEvent.getTransferTo();
            List<String> retainBids = noticeList.stream().filter(it -> it.getReceiver().getEmpId().equals(operateEmpId))
                    .map(NoticeMessagePo::getBid).collect(Collectors.toList());
            noticeBids.removeAll(retainBids);
        }
        if (CollectionUtils.isEmpty(noticeBids)) {
            log.warn("[notice] filter notice bids is empty");
            return;
        }
        markReadAction(noticeBids, null, true);
    }

    /**
     * 发送sse消息
     * @param empId
     * @param message
     */
    private void sendSseNotice(Long empId, Object message, boolean transaction) {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        String tenantId = userInfo.getTenantId();
        String clientIdPrefix = SseEmitterFactory.getSseClientIdPrefix(tenantId, empId);
        if (TransactionSynchronizationManager.isSynchronizationActive() && transaction) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    sendSseNotice(empId, message, false);
                }
            });
        } else {
            ConcurrentHashMap<String, SseEmitter> clients = sseEmitterFactory.getTenantClients(tenantId);
            if (!clients.isEmpty()) {
                Map<String, SseEmitter> userSseEmitters = clients.keySet().stream()
                        .filter(it -> it.startsWith(String.valueOf(clientIdPrefix)))
                        .collect(Collectors.toMap(Function.identity(), clients::get));
                userSseEmitters.forEach((key, value) -> {
                    sseEmitterFactory.sendMessage(tenantId, key, message);
                });
            }
        }
    }
}
