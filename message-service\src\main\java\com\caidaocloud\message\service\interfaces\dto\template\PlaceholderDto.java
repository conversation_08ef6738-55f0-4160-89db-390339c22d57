package com.caidaocloud.message.service.interfaces.dto.template;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import lombok.Data;

@Data
public class PlaceholderDto {
    private String bid;

    /**
     * 占位符的显示名称
     */
    private String text;

    /**
     * 占位符的列表字段名
     */
    private String name;

    /**
     * 占位符来源与哪个模型
     */
    private String model;

    /**
     * 分类
     */
    private String category;

    /**
     * 占位符的key
     */
    private String prop;

    /**
     * 是否是自定义字段
     */
    private boolean custom;

    /**
     * 字段占位替换后的显示类型：0 默认原生展示;1 yyyy-MM-dd 展示;2 yyyy-MM-dd HH:mm:ss;
     * 3 布尔类型，是和否
     */
    private EnumSimple varType;
}
