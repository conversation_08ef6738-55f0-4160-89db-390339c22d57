package com.caidaocloud.message.service.record.provider.db;

import com.baomidou.mybatisplus.annotation.DbType;
import com.caidaocloud.message.service.record.properties.LogProviderProperties;
import com.caidaocloud.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;

import javax.sql.DataSource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;

/**
 * created by: FoAng
 * create time: 22/5/2024 5:29 下午
 */
@Slf4j
@AllArgsConstructor
public class TableInitService {

    private DataSource dataSource;

    private LogProviderProperties logRecordProperties;

    private IRecordDbService recordService;

    /**
     * 初始化创建table
     * @param tenantId 租户ID
     */
    public void initRecordTable(String tenantId) {
        if (checkTableExist(tenantId)) {
            log.info("logRecord table has already initialized");
        } else {
            initTable(tenantId);
        }
    }

    private void initTable(String tenantId) {
        String type = logRecordProperties.getDbType();
        DbType dbType = DbType.getDbType(type);
        String dbScriptPath = String.format("/script/%s", dbType == DbType.MYSQL ? "init_mysql.sql" : "init_postgresql.sql");
        try {
            ClassPathResource pathResource = new ClassPathResource(dbScriptPath);
            String scriptSql = IOUtils.toString(pathResource.getInputStream(), StandardCharsets.UTF_8.name());
            if (StringUtil.isNotEmpty(scriptSql)) {
                this.recordService.initRecordTable(scriptSql.replaceAll("\\$tenantId", tenantId));
            }
        } catch (IOException e) {
            log.error("init record table error, {}", e.getMessage(), e);
        }
    }

    @SneakyThrows
    public boolean checkTableExist(String tenantId) {
        Connection connection = dataSource.getConnection();
        DatabaseMetaData metaData = connection.getMetaData();
        ResultSet resultSet = metaData.getTables(null, logRecordProperties.getSchema(),
                String.format("%s_%s", "log_record", tenantId), null);
        return resultSet.next();
    }

}
