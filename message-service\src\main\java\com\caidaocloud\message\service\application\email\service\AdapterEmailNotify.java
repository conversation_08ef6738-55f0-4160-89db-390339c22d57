package com.caidaocloud.message.service.application.email.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.message.service.application.email.dto.MailMsgSender;
import com.caidaocloud.message.service.application.feign.EmailAdapterFeignClient;
import com.caidaocloud.message.service.domain.email.dto.EmailTemplateBatchDto;
import com.caidaocloud.message.service.domain.email.dto.EmailTemplateDto;
import com.caidaocloud.message.service.domain.email.dto.SameEmailTemplateDto;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * SmtpEmailNotify
 *
 * <AUTHOR>
 * @date 2022/6/6 下午3:26
 */
@Slf4j
@Service
public class AdapterEmailNotify implements EmailNotify {
    @Resource
    private EmailAdapterFeignClient emailFeignClient;
    @Override
    public String getEmailType() {
        return "adapteremail";
    }

    @Override
    public boolean sendMsg(MailMsgSender sender) {
        SameEmailTemplateDto sameEmail = ObjectConverter.convert(sender, SameEmailTemplateDto.class);
        sameEmail.setTos(Arrays.asList(sender.getTo().split(";")));
        Result<?> response = emailFeignClient.sameEmailBatchSend(sameEmail);
        return Optional.ofNullable(response).map(it -> it.isSuccess() ? true : null)
                .orElseThrow(() -> new ServerException("邮件发送失败"));
    }

    @Override
    public boolean batchSendMsg(List<MailMsgSender> senderList) {
        EmailTemplateBatchDto dto = new EmailTemplateBatchDto();
        List<EmailTemplateDto> emailTemplateDtos = ObjectConverter.convertList(senderList, EmailTemplateDto.class);
        for(EmailTemplateDto dto1:emailTemplateDtos){
            dto.add(dto1);
        }
        Result<?> response = emailFeignClient.emailBatchSend(dto);
        return Optional.ofNullable(response).map(it -> it.isSuccess() ? true : null)
                .orElseThrow(() -> new ServerException("邮件发送失败"));
    }
}
