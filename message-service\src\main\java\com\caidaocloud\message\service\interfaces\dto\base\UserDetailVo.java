package com.caidaocloud.message.service.interfaces.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/5/24
 */
@Data
@AllArgsConstructor
public class UserDetailVo {
	@ApiModelProperty("租户的PC端Logo")
	private String pcTenantLogo;
	@ApiModelProperty("租户的App端Logo")
	private String appTenantLogo;
	@ApiModelProperty("公司名称")
	private String companyName;
	@ApiModelProperty("公司code")
	private String companyCode;
	@ApiModelProperty("启用模块")
	private String licencedModules;
	@ApiModelProperty("当前登录用户绑定的员工id")
	private String empId;
	@ApiModelProperty("当前登录用户绑定的员工姓名")
	private String name;
	private String orgId;
	@ApiModelProperty("当前登录用户绑定的工号")
	private String workno;
	@ApiModelProperty("当前登录用户绑定的英文名")
	private String enName;
	@ApiModelProperty("岗位名称")
	private String postName;
	@ApiModelProperty("员工头像")
	private String headPortrait;
}
