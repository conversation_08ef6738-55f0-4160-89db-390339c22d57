package com.caidaocloud.message.service.interfaces.vo.msg;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Objects;

public class MsgImportDto implements Serializable {
    @Excel(name = "姓名")
    private String name;
    @Excel(name = "公司邮箱")
    private String companyEmail;
    @Excel(name = "工号")
    private String workno;
    private String empId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCompanyEmail() {
        return companyEmail;
    }

    public void setCompanyEmail(String companyEmail) {
        this.companyEmail = companyEmail;
    }

    public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }

    public String getWorkno() {
        return workno;
    }

    public void setWorkno(String workno) {
        this.workno = workno;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MsgImportDto that = (MsgImportDto) o;
        return Objects.equals(name, that.name) && Objects.equals(workno, that.workno);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, workno);
    }
}
