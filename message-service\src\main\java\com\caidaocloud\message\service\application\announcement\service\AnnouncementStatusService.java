package com.caidaocloud.message.service.application.announcement.service;

import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.message.service.domain.announcement.entity.Announcement;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementActive;
import com.caidaocloud.message.service.domain.announcement.enums.AnnouncementStatus;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementActiveRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementContentRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementReceiveRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementRepository;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.ObjectConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

import javax.annotation.Resource;

/**
 * 公告状态管理服务
 *
 * <AUTHOR> Zhou
 * @date 2025/7/11
 */
@Slf4j
@Service
public class AnnouncementStatusService {

    @Resource
    private  AnnouncementRepository announcementRepository;
    @Resource
    private  AnnouncementActiveRepository announcementActiveRepository;

    /**
     * 发布公告
     * 
     * @param announcementId 公告ID
     * @return 是否成功
     */
    @PaasTransactional
    public boolean publishAnnouncement(String announcementId) {
        if (!StringUtils.hasText(announcementId)) {
            throw new IllegalArgumentException("公告ID不能为空");
        }

        log.info("开始发布公告，ID：{}", announcementId);

        // 1. 查询公告信息
        Announcement announcement = announcementRepository.selectById(announcementId);
        if (announcement == null) {
            throw new IllegalArgumentException("公告不存在，ID：" + announcementId);
        }

        // 2. 检查当前状态
        if (announcement.isPublished()) {
            throw new IllegalStateException("公告已发布，无需重复发布");
        }

        // 3. 更新公告状态为已发布，记录发布时间
        announcement.setStatus(AnnouncementStatus.PUBLISHED.toEnumSimple());
        announcement.setReleaseTime(System.currentTimeMillis());
        announcementRepository.updateById(announcement);


        // 4. 若当前时间大于生效时间，copy公告到已生效公告中
        if (announcement.isEffective()) {
            copyToActiveAnnouncement(announcement);
        }

        log.info("公告发布完成，ID：{}", announcementId);
        return true;
    }

    /**
     * 失效公告
     *
     * @param announcementId 公告ID
     * @return 是否成功
     */
    @PaasTransactional
    public boolean expireAnnouncement(String announcementId) {
        if (!StringUtils.hasText(announcementId)) {
            throw new IllegalArgumentException("公告ID不能为空");
        }

        log.info("开始失效公告，ID：{}", announcementId);

        // 1. 查询公告信息
        Announcement announcement = announcementRepository.selectById(announcementId);
        if (announcement == null) {
            throw new IllegalArgumentException("公告不存在，ID：" + announcementId);
        }

        // 2. 更新公告状态为失效
        announcement.setStatus(AnnouncementStatus.EXPIRED.toEnumSimple());
        announcementRepository.updateById(announcement);

        log.info("公告状态更新为失效，ID：{}", announcementId);

        // 3. 删除已生效公告中的数据
        announcementActiveRepository.deleteById(announcementId);

        log.info("公告失效完成，ID：{}", announcementId);
        return true;
    }

    /**
     * 撤回公告
     *
     * @param announcementId 公告ID
     * @return 是否成功
     */
    @PaasTransactional
    public boolean withdrawAnnouncement(String announcementId) {
        if (!StringUtils.hasText(announcementId)) {
            throw new IllegalArgumentException("公告ID不能为空");
        }

        log.info("开始撤回公告，ID：{}", announcementId);

        // 1. 查询公告信息
        Announcement announcement = announcementRepository.selectById(announcementId);
        if (announcement == null) {
            throw new IllegalArgumentException("公告不存在，ID：" + announcementId);
        }

        // 2. 检查当前状态
        if (!announcement.isPublished()) {
            throw new IllegalStateException("只有已发布的公告才能撤回");
        }

        // 3. 检查是否已生效
        if (announcement.isEffective()) {
            throw new IllegalStateException("公告已生效，无法撤回");
        }

        // 4. 更新公告状态为未发布
        announcement.setStatus(AnnouncementStatus.UNPUBLISHED.toEnumSimple());
        announcement.setReleaseTime(null); // 清除发布时间
        announcementRepository.updateById(announcement);

        log.info("公告撤回完成，状态更新为未发布，ID：{}", announcementId);
        return true;
    }

    /**
     * 复制公告到已生效公告表
     * 
     * @param announcement 原公告
     */
    private void copyToActiveAnnouncement(Announcement announcement) {
        // 检查是否已存在
        AnnouncementActive existingActive = announcementActiveRepository
                .selectById(announcement.getBid());
        if (existingActive != null) {
            log.info("生效公告已存在，跳过复制，原公告ID：{}", announcement.getBid());
            return;
        }

        // 创建生效公告
        AnnouncementActive activeAnnouncement = ObjectConverter.convert(announcement, AnnouncementActive.class);

        // 保存生效公告
        announcementActiveRepository.insert(activeAnnouncement);
        log.info("生效公告创建成功，原公告ID：{}，生效公告ID：{}", announcement.getBid(), activeAnnouncement.getBid());
    }


    /**
     * 批量处理已发布且生效的公告
     * 
     * @return 处理数量
     */
        @PaasTransactional

    public int processPublishedAndEffectiveAnnouncements() {
        log.info("开始批量处理已发布且生效的公告");

        long currentTime = DateUtil.getCurrentTimestamp();
        int processedCount = 0;

        // 查询已发布且生效时间小于等于当前时间的公告
        List<Announcement> announcements = announcementRepository.findPublishedAndEffectiveAnnouncements(currentTime);

        for (Announcement announcement : announcements) {
            try {
                // 检查是否已存在于生效公告表中
                    copyToActiveAnnouncement(announcement);
                    processedCount++;
                    log.info("公告已复制到生效公告表，ID：{}", announcement.getBid());
            } catch (Exception e) {
                log.error("处理生效公告失败，ID：{}，错误：{}", announcement.getBid(), e.getMessage(), e);
            }
        }

        log.info("批量处理已发布且生效的公告完成，处理数量：{}", processedCount);
        return processedCount;
    }

    /**
     * 批量处理已发布且失效的公告
     *
     * @return 处理数量
     */
        @PaasTransactional

    public int processPublishedAndExpiredAnnouncements() {
        log.info("开始批量处理已发布且失效的公告");

        long currentTime = DateUtil.getCurrentTimestamp();
        int processedCount = 0;

        // 查询已发布且过期时间小于等于当前时间的公告
        List<Announcement> announcements = announcementRepository.findPublishedAndExpiredAnnouncements(currentTime);

        for (Announcement announcement : announcements) {
            try {
                // 更新公告状态为失效
                announcement.setStatus(AnnouncementStatus.EXPIRED.toEnumSimple());
                announcementRepository.updateById(announcement);
                // 删除已生效公告中的数据
                announcementActiveRepository.deleteById(announcement.getBid());
                processedCount++;
                log.info("公告已失效并删除生效数据，ID：{}", announcement.getBid());
            } catch (Exception e) {
                log.error("处理失效公告失败，ID：{}，错误：{}", announcement.getBid(), e.getMessage(), e);
            }
        }

        log.info("批量处理已发布且失效的公告完成，处理数量：{}", processedCount);
        return processedCount;
    }
}
