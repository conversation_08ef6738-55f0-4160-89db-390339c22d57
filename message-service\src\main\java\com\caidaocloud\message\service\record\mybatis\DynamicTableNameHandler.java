package com.caidaocloud.message.service.record.mybatis;

import com.baomidou.mybatisplus.extension.plugins.handler.TableNameHandler;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.message.service.record.util.UserContextUtil;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Optional;

/**
 * created by: FoAng
 * create time: 22/5/2024 3:48 下午
 */
@Slf4j
public class DynamicTableNameHandler implements TableNameHandler {

    private final List<String> tableNames = Lists.newArrayList("log_record");

    @Override
    public String dynamicTableName(String sql, String tableName) {
        String threadTenantId = RequestDataHelper.getRequestData("tenantId");
        if (tableNames.contains(tableName) && !tableName.startsWith("CREATE TABLE")) {
            String suffix = Optional.ofNullable(threadTenantId).orElseGet(() -> {
                ISessionService userService = SpringUtil.getBean(ISessionService.class);
                String tenantId;
                try {
                    tenantId = userService.getTenantId();
                } catch (Exception e) {
                    tenantId = UserContextUtil.getTenantId();
                    if (StringUtils.isBlank(tenantId)) {
                        log.error("getTenantId fail, {}", e.getMessage(), e);
                        throw new ServerException("not found tenantId");
                    }
                }
                if (StringUtils.isBlank(tenantId)) {
                    tenantId = UserContextUtil.getTenantId();
                }
                return tenantId;
            });
            Assert.notNull(suffix, "tenantId should not be empty");
            return String.format("%s_%s", tableName, suffix);
        }
        return tableName;
    }
}
