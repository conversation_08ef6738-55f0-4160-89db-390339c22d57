package com.caidaocloud.message.service.record.provider;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * created by: FoAng
 * create time: 28/5/2024 3:42 下午
 */
@Slf4j
@Component
public class RecordRepositoryFactory {

    @Resource
    private Map<String, RecordRepository> repositoryMap;

    public RecordRepository getRepository(String provider) {
        return repositoryMap.get(provider);
    }

}
