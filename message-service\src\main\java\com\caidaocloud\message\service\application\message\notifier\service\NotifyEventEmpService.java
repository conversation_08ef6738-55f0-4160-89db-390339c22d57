package com.caidaocloud.message.service.application.message.notifier.service;

import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 查询事件人员的手机号、邮箱
 */
@Service
public class NotifyEventEmpService implements INotifyTargetService{

    @Override
    public String getNotifyTarget() {
        return NoticeTarget.EVENT_EMP.toString();
    }

    @Override
    public void getNotifier(List<NotifierDto> notifier, NotifyObjectDto obj) {
        // 查询候选人信息

    }
}
