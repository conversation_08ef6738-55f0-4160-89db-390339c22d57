package com.caidaocloud.message.test;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.message.send.sms.DingMessageNotifyApi;
import com.caidaocloud.message.service.application.msg.dto.DingtalkMsgDto;
import com.caidaocloud.message.service.application.msg.dto.MsgDto;
import com.caidaocloud.message.service.application.msg.event.ContractConsumer;
import com.caidaocloud.message.service.application.msg.event.MsgSubscribe;
import com.caidaocloud.message.service.application.msg.service.MsgService;
import com.caidaocloud.message.service.application.template.service.analysis.TemplateAnalysisService;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.domain.msg.entity.MsgConfigDo;
import com.caidaocloud.message.service.domain.msg.service.MsgConfigDomainService;
import com.caidaocloud.message.service.domain.template.entity.TemplateDo;
import com.caidaocloud.message.service.domain.template.service.TemplateDomainService;
import com.caidaocloud.message.service.interfaces.vo.template.TemplateVo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class MsgServiceTest {
    @Resource
    private MsgService msgService;
    @Resource
    private TemplateAnalysisService templateAnalysisService;
    @Resource
    private MsgConfigDomainService domainService;
    @Resource
    private TemplateDomainService templateDomainService;
    @Resource
    private ContractConsumer contractConsumer;
    @Resource
    private DingMessageNotifyApi dingMessageNotifyApi;
    @Resource
    private MsgSubscribe msgSubscribe;



    @Test
    public void sendTest(){
        String json = "{\"channel\":\",,,4\",\"emailMsg\":{\"content\":\"亲爱的杜芊芊,<br/><br/>您的&nbsp;合同续签 流程已被审核通过，请点击<a href=\\\"https://qa.magicchops.shanghaidisneyresort.com/contractlist?pageNo=1&status=REQUIRED\\\" target=\\\"_blank\\\">此处</a>阅读并签署相关文件以完成您的&nbsp;合同续签 流程。<br/><br/>如果您有任何疑问，请随时与您的直线经理联系。感谢您对我们工作的配合。<br/><br/>顺致敬意<br/><br/>人力资源运营团队<br/><br/>*本邮件由系统自动生成。如有任何疑问，请联系人力资源运营团队。*<br/><br/>Dear&nbsp;Dora,<br/><br/>Your&nbsp;Contract renew has been accepted.Please click <a href=\\\"https://qa.magicchops.shanghaidisneyresort.com/contractlist?pageNo=1&status=REQUIRED\\\" target=\\\"_blank\\\">here</a> to review and sign the document(s) to complete your&nbsp;Contract renew process.<br/><br/>Please feel free to contact your line manager if you have any questions.Thank you for your cooperation!<br/><br/>BestRegards,<br/><br/>Global HR Operations<br/><br/>*This is a system-generated email.If you have any concerns or questions,please contact the Global HR Operation steam*\",\"subject\":\"qa请尽快完成文件签署/Need your immediate action 请在线完成&nbsp;合同续签 流程/Please complete&nbsp;Contract renew process online\",\"to\":\"<EMAIL>\"},\"tenantId\":\"33\",\"userId\":0}";

        System.out.println(json);
        MsgDto msgDto = FastjsonUtil.toObject(json, MsgDto.class);

//        msgService.doMsg(msgDto);
    }

    @Test
    public void sendMsgSubscribeTest(){
        String json = "{\"appMsg\":[{\"content\":\"<p>钉钉测试222222内容，20230228</p>\",\"createTime\":1677556318337,\"msgType\":\"text\",\"title\":\"钉钉测试20230228\",\"type\":\"workno\",\"userId\":\"CI32224\"}],\"channel\":[\"0\",\"1\",\"2\",\"4\"],\"dingTalkMsg\":{\"content\":\"<p>钉钉测试222222内容，20230228</p>\",\"mobiles\":[\"18332559937\"],\"subject\":\"钉钉测试20230228\",\"templateType\":\"4\"},\"emailMsg\":{\"content\":\"<p>钉钉测试222222内容，20230228</p>\",\"subject\":\"钉钉测试20230228\",\"to\":\"<EMAIL>\"},\"message\":{\"content\":\"<p>钉钉测试222222内容，20230228</p>\",\"mobile\":[]},\"tenantId\":\"8\"}";

        System.out.println(json);
//        MsgDto msgDto = FastjsonUtil.toObject(json, MsgDto.class);
//        msgSubscribe.process(json, null);
//        msgService.doMsg(msgDto);
    }


    @Test
    public void sendMsgSubscribeTest222(){
        String json = "{\"appMsg\":[{\"content\":\"<p>钉钉测试222222内容，20230228</p>\",\"createTime\":1677556318337,\"msgType\":\"text\",\"title\":\"钉钉测试20230228\",\"type\":\"workno\",\"userId\":\"CI32224\"}],\"channel\":[\"4\",\"5\"],\"dingTalkMsg\":{\"content\":\"# 这是支持markdown的文本\",\"mobiles\":[\"18332559937\"],\"subject\":\"钉钉测试20230228\",\"templateType\":\"4\"}, \"dingtalkToDoNoticeDto\":{\"description\":\"<p>钉钉测试222222内容，20230228</p>\",\"mobiles\":[\"18332559937\"],\"subject\":\"钉钉测试20230228\"},\"emailMsg\":{\"content\":\"<p>钉钉测试222222内容，20230228</p>\",\"subject\":\"钉钉测20230228\",\"to\":\"<EMAIL>\"},\"message\":{\"content\":\"<p>钉钉测试222222内容，20230228</p>\",\"mobile\":[]},\"tenantId\":\"8\"}";

        System.out.println(json);
//        MsgDto msgDto = FastjsonUtil.toObject(json, MsgDto.class);
//        msgSubscribe.process(json, null);
//        msgService.doMsg(msgDto);
    }

    @Test
    public void sendMsgSubscribeTest333(){
        String json = "{\"channel\":[\"1\"],\"emailMsg\":{\"cc\":\"<EMAIL>;11111\",\"content\":\"<p># &nbsp; 钉钉测试内容bbbbbb，钉钉测试20230302周四结点2,tqeqeqeqeqweqeq</p>\",\"subject\":\"钉钉测试20230302周四结点2qeqeqeqeqe\",\"to\":\"<EMAIL>\"},\"tenantId\":\"8\"}";

        System.out.println(json);
//        MsgDto msgDto = FastjsonUtil.toObject(json, MsgDto.class);
//        msgSubscribe.process(json, null);
//        msgService.doMsg(msgDto);
    }


    @Test
    public void sendDingTest(){
        String json = "{\n" +
                "    \"channel\":\"4\",\n" +
                "    \"dingTalkMsg\":{\n" +
                "        \"content\":\"亲爱的ambition 您的合同续签 流程已被审核通过。20230606\",\n" +
                "        \"subject\":\"钉钉主题qa请尽快完成文件签署tests--20230606\",\n" +
                "        \"templateType\":\"1\",\n" +
                "        \"url\":\"dingtalk://dingtalkclient/action/openapp?corpid=dinge4c7302c39d27de6bc961a6cb783455b&amp;container_type=work_platform&amp;app_id=0_2388290964&amp;redirect_type=jump&amp;redirect_url=http://58.33.27.66:20001/h5/authentication\",\n" +
                "        \"affixName\":\"jvm.png\",\n" +
                "        \"mobiles\":[{\"value\":\"18332559937\"}],\n" +
                "        \"affix\":\"/caidao-pa/259dtfgdb9cdb5ddab4b-jvm.png\"\n" +
                "    },\n" +
                "    \"tenantId\":\"8\",\n" +
                "    \"userId\":0\n" +
                "}";

        System.out.println(json);
        MsgDto msgDto = FastjsonUtil.toObject(json, MsgDto.class);

        msgService.doMsg(msgDto);
    }

    @Test
    public void sendDingLinkTest(){
        String json = "{\n" +
                "    \"channel\":\"4\",\n" +
                "    \"dingTalkMsg\":{\n" +
                "        \"content\":\"亲爱的ambition 您的合同续签 流程已被审核通过。20230606\",\n" +
                "        \"subject\":\"钉钉主题qa请尽快完成文件签署tests--20230606\",\n" +
                "        \"templateType\":\"1\",\n" +
                "        \"url\":\"dingtalk://dingtalkclient/action/openapp?corpid=dinge4c7302c39d27de6bc961a6cb783455b&amp;container_type=work_platform&amp;app_id=0_2388290964&amp;redirect_type=jump&amp;redirect_url=http://58.33.27.66:20001/h5/authentication\",\n" +
                "        \"affixName\":\"jvm.png\",\n" +
                "        \"mobiles\":[{\"value\":\"18332559937\"}],\n" +
                "        \"affix\":\"/caidao-pa/259dtfgdb9cdb5ddab4b-jvm.png\"\n" +
                "    },\n" +
                "    \"tenantId\":\"8\",\n" +
                "    \"userId\":0\n" +
                "}";

        System.out.println(json);
        MsgDto msgDto = FastjsonUtil.toObject(json, MsgDto.class);

        msgService.doMsg(msgDto);
    }


    @Test
    public void sendDingTextTest(){
        String json = "{\"channel\":[\"4\"],\"dingTalkMsg\":{\"content\":\"模版名称测试测测his\",\"mobiles\":[\"***********\"],\"subject\":\"\",\"templateType\":\"2\"},\"msgConfig\":\"1607223969232921\",\"tenantId\":\"8\",\"userId\":200}";

        System.out.println(json);
        MsgDto msgDto = FastjsonUtil.toObject(json, MsgDto.class);

        msgService.doMsg(msgDto);
    }

    @Test
    public void saveMessageLogRecordTest(){

        String message = "{\"channel\":[\"1\",\"5\"],\"dingtalkToDoNoticeDto\":{\"businessKey\":\"1776898846038017_TRANSFER-1728046758090753\",\"description\":\"<p> &nbsp;Dear<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"李艾静\\\">李艾静</span>，                                                                <br>         &nbsp;有一条人员异动流程任务等待您处理。                                                                <br>         &nbsp;本流程到达您那需要尽快处理，否则会影响该流程生效日期。                                                                <br>        <br>         &nbsp;异动员工工号：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"CI16866111\\\">CI16866111</span><br>                                                <br>         &nbsp;异动员工姓名：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"岳异动\\\">岳异动</span><br><br> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;调整前组织：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"供应链CCC组\\\">供应链CCC组</span><br><br> &nbsp; &nbsp; &nbsp; &nbsp; 调整前直接汇报对象：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"岳大\\\">岳大</span><br><br> &nbsp; &nbsp; &nbsp; &nbsp; 调整后组织：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"\\\"></span><br><br> &nbsp; &nbsp; &nbsp; &nbsp; 调整后直接汇报对象：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"何雅婷\\\">何雅婷</span><br><br> &nbsp; &nbsp; &nbsp; &nbsp; 调整生效日期：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"2023-10-11\\\">2023-10-11</span><br><br>         &nbsp;请<a href=\\\"https://hcm-test.ciwork.cn/module/workflow/backlog\\\" target=\\\"_blank\\\">点击这里</a>进入工作流系统处理。<br></p>\",\"dingCaidaoId\":\"1776900351973543\",\"mobiles\":[{\"value\":\"13772560447/+86/CN\"}],\"subject\":\"审批提醒: 员工异动审批确认-CI16866111，岳异动\",\"taskId\":\"9f48a8cc-689c-11ee-ba07-fa163ea69f3b\"},\"emailMsg\":{\"content\":\"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\",\"subject\":\"审批提醒: 员工异动审批确认-CI16866111，岳异动\",\"to\":\"<EMAIL>\"},\"tenantId\":\"8\"}";
        MsgDto msgDto = FastjsonUtil.toObject(message, MsgDto.class);

        msgService.saveMessageLogRecord(message, msgDto);
    }


    @Test
    public void sendDingTextConsumerTest(){
        String json = "{\"createTime\":1676430720991,\"ext\":{\"empId\":\"1607796668717182\"},\"msgConfig\":\"1607796330174474\",\"msgFrom\":\"OnBoarding\",\"subjects\":[\"1607796668717182\"],\"tenantId\":\"8\",\"type\":1,\"userId\":200}";

        System.out.println(json);
        contractConsumer.process(json);

    }

    @Test
    public void sendDingTextewerTest(){
        String json = "{\"content\":\"Dear，您的有一条入职消息通知发起人：对应招聘顾问姓名 &nbsp; &nbsp;\\\n" +
                " &nbsp; &nbsp;发起时间：对应提交入职流程日期和时间就否：何雅婷21601啦啦啦\",\"mobiles\":[{\\\"value\\\":\\\"18332559937\\\"}],\"subject\":\"链接翁本\",\"templateType\":\"1\",\"url\":\"http://49.4.7.126:82/module/admin/message/setting/edit\"}";

//        json = "发起人：对应招聘顾问姓名    \\n    发起时间：对应提交入职流程日期和时间一行    \\\\n    消息通";
//        json = "{\"content\":\"发起人：对应招聘顾问姓名    \\n    发起时间：对应提交入职流程日期和时间\",\"mobiles\":[{\"value\":\"18332559937\"}],\"subject\":\"链接文本\",\"templateType\":\"4\",\"url\":\"http://49.4.7.126:82/module/admin/message/setting/edit\"}";
//        json = "{\"content\":\"消息通消息通知验证tttt   \n    消息通知验证验证222\",\"mobiles\":[{\"value\":\"18332559937\"},{\"value\":\"15861823917\"}],\"subject\":\"取消呃呃呃呃呃呃入职\",\"templateType\":\"4\"}";
        System.out.println(json);
        DingtalkMsgDto msgDto = FastjsonUtil.toObject(json, DingtalkMsgDto.class);
        dingMessageNotifyApi.sendDingMessage(new MsgDto());

    }


    @Test
    public void testKV(){
//        MsgConfigDo data = domainService.getById("1502542095620594");
//        List<TemplateDo> templateList = templateDomainService.getByConfig(data);
//        List<TemplateVo> templateVos = TemplateVo.toVoList(templateList);
//
//        templateAnalysisService.analysisTemplateInfo(templateVos.get(0), "1503156034574337", null);
    }

    @Before
    public void initUser(){
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        userInfo.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);

        UserInfo user = new UserInfo();
        user.setTenantId(String.valueOf(11));
        user.doSetUserId(0L);
        UserContext.setCurrentUser(user);
    }
}
