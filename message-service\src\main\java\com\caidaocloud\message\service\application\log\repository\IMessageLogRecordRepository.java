package com.caidaocloud.message.service.application.log.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import com.caidaocloud.message.service.application.log.dto.MessageLogRecordDto;
import com.caidaocloud.message.service.application.msg.dto.MsgDto;
import com.caidaocloud.message.service.domain.msg.entity.MessageLogRecordDo;
import com.caidaocloud.message.service.interfaces.dto.msg.MessageLogRecordQueryDto;

/**
 * 消息日志
 *
 * <AUTHOR>
 */
public interface IMessageLogRecordRepository {

    /**
     * 保存
     *
     * @param logDto
     * @return
     */
    String saveMessageLogRecord(MessageLogRecordDto logDto);

    String updateMessageLogRecordStatus(String mdc, SendStatusEnum sendStatusEnum);

    String updateMessageLogRecordContent(String mdc, String content);

    String updateMessageLogRecord(MsgDto msgDto, SendStatusEnum sendStatusEnum);

    PageResult<MessageLogRecordDo> selectPage(MessageLogRecordQueryDto recordDto);

    MessageLogRecordDo getMessageLogRecordByBid(Long id);

}
