package com.caidaocloud.message.service.application.message.notifier.service;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.feign.HrFeignClient;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 *
 * <AUTHOR>
 * @date 2023/3/22
 */
@SpringBootTest(classes = MessageApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class NotifyWorkflowApproverServiceTests {
	@Autowired
	private HrFeignClient hrFeignClient;

	private static final String preIdSameWorkPlace = "1622065038637059";
	private static final String preIdOtherWorkPlace = "1628293789046785";
	private static final String empIdSameWorkPlace = "1631945824491522";
	private static final String empIdOtherWorkPlace = "1598865565308929";
	private static final String settingBiD = "1616558989064200";

	@Before
	public void  bf(){
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("8");
		userInfo.setUserId(0L);
		userInfo.setEmpId(0L);
		SecurityUserUtil.setSecurityUserInfo(userInfo);
	}

	@Test
	public void subListTest() {
		List<Integer> list = new ArrayList<>();
		for (int i = 0; i <= 10; i++) {
			list.add(i);
		}
		System.out.println(list);
		System.out.println(list.subList(1, list.size() - 1));
	}

	@Test
	public void workflowApproverFeign(){

		Result<String> preFound = hrFeignClient.getApprovers(preIdSameWorkPlace,
				settingBiD, true);
		Result<String> preNotFound = hrFeignClient.getApprovers(preIdOtherWorkPlace,
				settingBiD, true);
		Result<String> empFound = hrFeignClient.getApprovers(empIdSameWorkPlace,
				settingBiD, false);
		Result<String> empNotFound = hrFeignClient.getApprovers(empIdOtherWorkPlace,
				settingBiD, false);
		Assert.assertTrue("候选人查询工作地失败",StringUtils.isNotEmpty(preFound.getData()));
		Assert.assertTrue("候选人查询工作地返回数据错误",StringUtils.isEmpty(preNotFound.getData()));

		Assert.assertTrue("员工查询工作地失败",StringUtils.isNotEmpty(empFound.getData()));
		Assert.assertTrue("员工查询工作地返回数据错误",StringUtils.isEmpty(empNotFound.getData()));
	}
}