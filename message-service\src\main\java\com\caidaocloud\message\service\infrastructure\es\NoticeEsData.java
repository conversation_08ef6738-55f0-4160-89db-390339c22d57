package com.caidaocloud.message.service.infrastructure.es;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.record.core.annotation.DynamicIndex;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.zxp.esclientrhl.annotation.ESID;
import org.zxp.esclientrhl.annotation.ESMapping;
import org.zxp.esclientrhl.annotation.ESMetaData;
import org.zxp.esclientrhl.enums.DataType;

import java.io.Serializable;
import java.util.Map;

/**
 * notice支持es搜索
 * created by: FoAng
 * create time: 15/8/2024 4:52 下午
 */
@Data
@DynamicIndex
@Accessors(chain = true)
@ESMetaData(indexName = "es_notice", number_of_shards = 5,number_of_replicas = 0, autoCreateIndex = false)
public class NoticeEsData implements Serializable {

    @ESID
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键ID")
    private Long id;

    @ESMapping(datatype = DataType.keyword_type, nested_class = EmpSimple.class)
    private EmpSimple receiver;

    @ESMapping(datatype = DataType.keyword_type)
    private String title;

    @ESMapping(datatype = DataType.keyword_type)
    private String summary;

    @ESMapping(datatype = DataType.nested_type, nested_class = Map.class)
    private Map<String, Object> ext;

    @ESMapping(datatype = DataType.text_type)
    private String content;

    @ESMapping(datatype = DataType.keyword_type)
    private String type;

    @ESMapping(datatype = DataType.keyword_type)
    private String businessType;
}
