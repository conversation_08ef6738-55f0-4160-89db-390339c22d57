package com.caidaocloud;

import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.msg.dto.TemplateRefreshMessageDto;
import com.caidaocloud.message.service.application.msg.event.CommonMsgSubscribe;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class TemplateRefreshMessageTest {
    @Resource
    private CommonMsgSubscribe commonMsgSubscribe;
    @Test
    public void testRelRefresh(){
        TemplateRefreshMessageDto trm = new TemplateRefreshMessageDto();
        trm.setTenantId("66");
        trm.setUserId("0");
        trm.setEmpId("1502417336096769");

        String trmJson = FastjsonUtil.toJson(trm);
        log.info("testRelRefresh json data ={}", trmJson);
//        commonMsgSubscribe.process(trmJson);
    }
}
