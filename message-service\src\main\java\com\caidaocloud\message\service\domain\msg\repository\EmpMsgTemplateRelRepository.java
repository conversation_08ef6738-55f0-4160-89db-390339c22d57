package com.caidaocloud.message.service.domain.msg.repository;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;
import com.caidaocloud.message.service.domain.msg.entity.EmpMsgTemplateRelDo;

import java.util.List;

public interface EmpMsgTemplateRelRepository extends BaseRepository<EmpMsgTemplateRelDo> {
    List<EmpMsgTemplateRelDo> getListByEmpId(String empId);

    List<EmpMsgTemplateRelDo> getListByEmpList(List<String> empList);

    void deleteByEmpIdAndTemplateId(String template, List<String> empList);

    void batchInsert(List<EmpMsgTemplateRelDo> dataList);

    void deleteTemplateId(String template);

    List<String> getListByEmpList(String msgConfig, BasePage page);

    List<String> getEmpListByEmpIds(String msgConfig, BasePage page, List<String> empIdList);
}
