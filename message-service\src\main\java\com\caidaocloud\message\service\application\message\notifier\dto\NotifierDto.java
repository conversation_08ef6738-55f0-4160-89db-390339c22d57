package com.caidaocloud.message.service.application.message.notifier.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class NotifierDto {
    /**
     * 通知人 empId
     * 外部人员 empId 为空
     */
    private String empId;

    /**
     * 通知人手机号
     */
    private PhoneSimple phone;

    /**
     * 通知人个人邮箱
     */
    private String email;

    /**
     * 通知人公司邮箱
     */
    private String companyEmail;

    /**
     * 通知人工号
     * 外部人员 workno 为空
     */
    private String workno;

    /**
     * 姓名
     */
    private String name;
}
