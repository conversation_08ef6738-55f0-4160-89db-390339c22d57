package com.caidaocloud.message.service.application.message.send.dto;

import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.message.service.application.msg.dto.MsgDto;
import com.caidaocloud.message.service.interfaces.vo.template.TemplateVo;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class MessageContext {
    /**
     * 监护人等通知对象,未关联员工信息
     */
    private List<NotifierDto> otherObjList;

    /**
     * 消息模版
     */
    private TemplateVo template;

    /**
     * 消息类型
     */
    private NoticeType noticeType;

    /**
     * 通知员工对象
     */
    private Set<String> empList;

    /**
     * 系统内接收人员
     */
    private Map<String, NotifierDto> receiverMap;

    /**
     * 消息体封装
     */
    private MsgDto msgDto;

    /**
     * 事件接收人员信息
     */
    private NotifyObjectDto notify;

    /**
     * 当前事件人员
     */
    private String empId;

    /**
     * 扩展字段
     */
    private Map<String, Object> ext;
}
