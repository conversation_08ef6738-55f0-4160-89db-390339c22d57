package com.caidaocloud.message.test;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.email.service.RedisLimiter;
import com.caidaocloud.message.service.application.message.send.MessageSendService;
import com.caidaocloud.message.service.application.msg.event.ContractEventEntity;
import com.caidaocloud.message.service.application.msg.event.MsgSubscribe;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class MessageConsumerTest {
    @Resource
    private MessageSendService messageSendService;
    @Resource
    private MsgSubscribe msgSubscribe;
    @Test
    public void sendTest(){
        String message = "{\"createTime\":1676434207604,\"ext\":{\"empId\":\"1621959005501441\"},\"msgConfig\":\"1622022495188994\",\"msgFrom\":\"OnBoarding\",\"subjects\":[\"1621959005501441\"],\"tenantId\":\"8\",\"type\":1,\"userId\":200}";
        message = "{\"bid\":\"0\",\"channel\":[\"1\"],\"emailMsg\":{\"cc\":\"<EMAIL>\",\"content\":\"主岗下级：\\na、b\\n兼岗下级：\\n1、3\\n\",\"subject\":\"发生异动，请及时关注下属领导信息！\",\"to\":\"<EMAIL>\"},\"mdc\":\"1681979011491\",\"msgConfig\":\"1652549732718956\",\"msgFrom\":\"CareerAdapter\",\"userId\":0}";
        message = "{\"createTime\":1676434207604,\"ext\":{\"empId\":\"1801198268872705\"},\"msgConfig\":\"1801923861174283\",\"msgFrom\":\"HR\",\"subjects\":[\"1801198268872705\"],\"tenantId\":\"8\",\"type\":0,\"userId\":200}";

        log.info("-----------------test mq receive msg info:{}", message);
        try {
            ContractEventEntity entity = FastjsonUtil.toObject(message, ContractEventEntity.class);
            String tenantId = entity.getTenantId();
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(tenantId);
            final Long userId = entity.getUserId();
            userInfo.setEmpId(0L);
            userInfo.setUserId(userId);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            UserInfo user = new UserInfo();
            user.setTenantId(tenantId);
            user.setStaffId(0L);
            user.doSetUserId(userInfo.getUserId());
            UserContext.setCurrentUser(user);
            messageSendService.doConsumeMessage(entity);
        } catch (Exception e) {
            log.error("process message={} err,{}", message, e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }

    @Test
    public void sendTxtTest(){
        String message = "{\"tenantId\":\"8\",\"bid\":\"0\",\"channel\":[\"1\"],\"emailMsg\":{\"cc\":\"<EMAIL>\",\"content\":\"主岗下级：\\na、b\\n兼岗下级：\\n1、3\\n\",\"subject\":\"发生异动，请及时关注下属领导信息！\",\"to\":\"<EMAIL>\"},\"mdc\":\"1681979011491\",\"msgConfig\":\"1651180395624450\",\"msgFrom\":\"CareerAdapter\",\"userId\":0}";
        msgSubscribe.process(message);
    }

    @Test
    public void isAllowedTest(){
        boolean allowed = SpringUtil.getBean(RedisLimiter.class).isAllowed("aaaa_bbbccdd", 5, 5, 1);
        System.out.println(allowed);
    }
}
