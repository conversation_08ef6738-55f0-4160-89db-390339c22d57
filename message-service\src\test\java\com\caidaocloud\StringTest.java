package com.caidaocloud;

import com.google.common.collect.Maps;
import lombok.var;
import org.apache.commons.text.StringSubstitutor;
import org.checkerframework.checker.nullness.qual.Nullable;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class StringTest {
    public static void test(){
        String [] userids = new String[]{"a1","123"};
        List<Integer> empIds = Arrays.stream(userids).map(u -> {
            try {
                return Integer.valueOf(u);
            } catch (Exception e){
                System.out.println("---" + u);
            }
            return 0;
        }).filter(uid -> 0 != uid).distinct().collect(Collectors.toList());
        System.out.println(empIds);
    }

    public static void main(String[] args) {
        test();

        String jon = "151/+86/CN";
        System.out.println(jon.substring(0, jon.indexOf("/")));
        jon = "155";
        if(jon.indexOf("/") > 0){
            System.out.println(jon.substring(0, jon.indexOf("/")));
        }


        String str = "哈哈哈#123#呵呵呵";
        HashMap<String, String> valueMap = Maps.newHashMap();
        valueMap.put("123", "2222");
        StringSubstitutor sub = new StringSubstitutor(valueMap);
        sub.setVariablePrefix("#");
        sub.setVariableSuffix("#");
        String replace = sub.replace(str);
        System.out.println(replace);

        String str2 = "<p>您好，</p><p>您尚未完成入职材料的提交或相应的操作，请<a href=\"http://qa.magicjourney.shanghaidisneyresort.com/onboardingH5/login\" target=\"_blank\">点击此处</a> ，根据系统提示完成操作。<a href=\"https://qa.magicjourney.shanghaidisneyresort.com/module/hr/employee/onboarding/flow?businessLineId={businessLineId}&stepLabelId={stepLabelId}&empId={empId}\" target=\"_blank\">超链接测试</a></p><p>如有任何疑问，请联系SHDR<EMAIL>。</p><p><em> </em></p><p><em>顺致敬意</em></p><p><em>上海迪士尼度假区</em></p><p><em>人力资源团队</em></p><p><br></p><p>Hello,</p><p>You have not completed the submission of the onboarding materials or the corresponding operation. Please<u> </u><a href=\"http://qa.magicjourney.shanghaidisneyresort.com/onboardingH5/login\" target=\"_blank\"><u>click Here</u> </a> to complete the operation according to the system prompt.</p><p>If you have any questions, please <NAME_EMAIL>.</p><p><em> </em></p><p><em>Best Regards,</em></p><p><em>Shanghai Disney Resort</em></p><p><em>Human Resources</em></p><p><br></p>";
        Map<String, String> extMap = Maps.newHashMap();
        extMap.put("businessLineId", "1");
        extMap.put("stepLabelId", "2");
        extMap.put("empId", "3");
        var sub2 = new StringSubstitutor(extMap);
        sub2.setVariablePrefix("{");
        sub2.setVariableSuffix("}");
        System.out.println(sub2.replace(str2));

    }

}
