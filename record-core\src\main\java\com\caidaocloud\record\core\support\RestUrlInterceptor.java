package com.caidaocloud.record.core.support;

import com.caidaocloud.record.core.beans.AuthResourceUrl;
import com.caidaocloud.record.core.beans.LogRecordData;
import com.caidaocloud.record.core.beans.Operator;
import com.caidaocloud.record.core.configure.LogRecordProperties;
import com.caidaocloud.record.core.enums.ResourceCategoryEnum;
import com.caidaocloud.record.core.service.IDispatchRecordService;
import com.caidaocloud.record.core.service.IOperatorGetService;
import com.caidaocloud.record.core.service.IUrlResourceService;
import com.caidaocloud.record.core.util.WebUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * created by: FoAng
 * create time: 29/5/2024 10:22 上午
 */
@Slf4j
@AllArgsConstructor
public class RestUrlInterceptor implements HandlerInterceptor {

    private IUrlResourceService urlResourceService;

    private IDispatchRecordService dispatchRecordService;

    private IOperatorGetService operatorGetService;

    private AnnotationAttributes annotationAttributes;

    private LogRecordProperties logRecordProperties;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        LogRecordData recordData = new LogRecordData();
        recordData.setUri(requestURI);
        Operator operator = operatorGetService.getUser();
        recordData.setServiceName(annotationAttributes != null ? annotationAttributes.getString("name")
                : "");
        recordData.setOperator(operator.getOperatorId());
        recordData.setTenantId(operator.getTenantId());
        recordData.setEmpId(operator.getEmpId());
        recordData.setOperatorPlatform(WebUtil.getOperatorPlatform(request));
        recordData.setOperatorSource(WebUtil.getOperatorSource(request));
        request.setAttribute("recordInfo", loadUrlResource(recordData));
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        LogRecordData recordData = (LogRecordData) request.getAttribute("recordInfo");
        int requestStatus = response.getStatus();
        recordData.setStatus(requestStatus >= 200 && requestStatus < 300);
        recordData.setCreateTime(new Date());
        List<String> excludeUrls = urlResourceService.getAllExcludeUrls();
        if (!excludeUrls.contains(recordData.getUri()) && !WebUtil.internalCallRequest(request)) {
            if (logRecordProperties.isMatchAll() || urlResourceService.getRegisterUrls().contains(recordData.getUri())) {
                try {
                    dispatchRecordService.dispatchLogRecord(recordData);
                } catch (Exception e) {
                    log.error("[record] save record error, msg:{}", e.getMessage(), e);
                }
            } else {
                log.info("[record] uri filter by rules.....");
            }
        }
    }

    private LogRecordData loadUrlResource(LogRecordData recordData) {
        final String uri = recordData.getUri();
        List<AuthResourceUrl> urlList = urlResourceService.urlResources();
        AuthResourceUrl resourceUrl = urlList.stream().filter(it -> it.getUrl().equals(uri)).findFirst().orElse(null);
        if (resourceUrl != null) {
            final String resourceMenu;
            if (resourceUrl.getCategory() != ResourceCategoryEnum.MENU) {
                resourceMenu =  String.join("-", resourceUrl.getParentName(), resourceUrl.getName());
            } else {
                resourceMenu = resourceUrl.getParentName();
            }
            recordData.setMenu(resourceMenu);
            recordData.setCategory(Optional.ofNullable(recordData.getCategory())
                    .orElseGet(() -> ResourceCategoryEnum.parseResourceCategory(resourceUrl.getCategory(),
                            resourceUrl.getResourceAction())));
        }
        return recordData;
    }

    private Map<String, String> getRequestParams(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        Map<String, String> requestParams = new HashMap<>();
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            requestParams.put(entry.getKey(), entry.getValue()[0]);
        }
        return requestParams;
    }

    public void init() {
        // 进行包扫描、扫描所有注解并且获取url,进行排除掉
        // 增加logUrlRecord注解，支持部分url
    }
}
