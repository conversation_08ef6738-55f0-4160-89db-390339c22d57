<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>caidao-message-service</artifactId>
        <groupId>com.caidaocloud</groupId>
        <version>1.1.5-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>message-service</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>hr-core</artifactId>
            <version>2.0.1-SNAPSHOT</version>
            <!--<exclusions>
                <exclusion>
                    <groupId>com.caidaocloud</groupId>
                    <artifactId>paas-sdk</artifactId>
                </exclusion>
            </exclusions>-->
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>message-sdk</artifactId>
        </dependency>
        <!--<dependency>
            <groupId>com.caidaocloud.masterdata</groupId>
            <artifactId>masterdata-core</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>-->
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>caidaocloud-oss-service</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.jettison</groupId>
                    <artifactId>jettison</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--<dependency>-->
            <!--<groupId>com.dingtalk.open</groupId>-->
            <!--<artifactId>taobao-sdk-java-auto</artifactId>-->
            <!--<version>1479188381469-20210616</version>-->
            <!--<scope>system</scope>-->
            <!--<systemPath>${project.basedir}/lib/taobao-sdk-java-auto_1479188381469-20210616.jar</systemPath>-->
        <!--</dependency>-->

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
            <version>2.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
            <version>1.5.39</version>
        </dependency>
        <!-- aliyun sms SDK -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.5.3</version>
        </dependency>
        <dependency>
            <groupId>com.vladsch.flexmark</groupId>
            <artifactId>flexmark-all</artifactId>
            <version>0.62.2</version>
        </dependency>
    
    
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>caidao-resource</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>caidaocloud-commons</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
    
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>galaxy-service-security</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>
    
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.4.3.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.freemarker</groupId>
                    <artifactId>freemarker</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>1.2</version>
        </dependency>
    
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
    
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
    
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-core</artifactId>
            <version>8.1.0</version>
        </dependency>
    
        <!--elasticsearch-->
        <dependency>
            <groupId>cn.zxporz</groupId>
            <artifactId>esclientrhl</artifactId>
            <version>7.0.2</version>
        </dependency>
    
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>record-core</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>