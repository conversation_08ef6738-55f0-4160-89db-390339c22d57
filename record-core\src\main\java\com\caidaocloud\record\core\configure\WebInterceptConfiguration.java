package com.caidaocloud.record.core.configure;

import com.caidaocloud.record.core.annotation.EnableLogRecord;
import com.caidaocloud.record.core.service.IDispatchRecordService;
import com.caidaocloud.record.core.service.IOperatorGetService;
import com.caidaocloud.record.core.service.IUrlResourceService;
import com.caidaocloud.record.core.support.RestUrlInterceptor;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportAware;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * 网关请求地址拦截注解
 * created by: FoAng
 * create time: 28/5/2024 5:32 下午
 */
@Configuration
@ConditionalOnWebApplication
@AutoConfigureAfter(LogRecordProxyAutoConfiguration.class)
@ConditionalOnProperty(name = "log.record.enable", havingValue = "true", matchIfMissing = true)
@ConditionalOnExpression("'${log.record.mode}'.equals('gateway') || '${log.record.mode}'.equals('mixed')")
public class WebInterceptConfiguration implements WebMvcConfigurer, ImportAware {

    @Resource
    private IUrlResourceService urlGetService;

    @Resource
    private IOperatorGetService operatorGetService;

    @Resource
    private IDispatchRecordService dispatchRecordService;

    @Resource
    private LogRecordProperties logRecordProperties;


    private AnnotationAttributes enableLogRecord;

    @Bean
    public RestUrlInterceptor resetUrlInterceptor() {
        return new RestUrlInterceptor(urlGetService, dispatchRecordService, operatorGetService,
                enableLogRecord, logRecordProperties);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(resetUrlInterceptor());
    }

    @Override
    public void setImportMetadata(AnnotationMetadata annotationMetadata) {
        AnnotationAttributes attributes = AnnotationAttributes.fromMap(
        annotationMetadata.getAnnotationAttributes(EnableLogRecord.class.getName(), false));
        if (attributes != null) {
            enableLogRecord = attributes;
        }
    }
}
