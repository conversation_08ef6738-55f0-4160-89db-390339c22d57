package com.caidaocloud.message.service.record.consumer;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.hrpaas.paas.common.dto.KvDto;
import com.caidaocloud.message.service.record.feign.PaasFeign;
import com.caidaocloud.message.service.record.properties.LogProviderProperties;
import com.caidaocloud.message.service.record.provider.db.TableInitService;
import com.caidaocloud.message.service.record.provider.es.CusEsRepository;
import com.caidaocloud.message.service.record.util.UserContextUtil;
import com.caidaocloud.mq.rabbitmq.ConsumerGenerate;
import com.caidaocloud.mq.rabbitmq.consumer.DynamicConsumer;
import com.caidaocloud.mq.rabbitmq.factory.ExchangeType;
import com.caidaocloud.record.core.constants.MsgConstant;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * created by: FoAng
 * create time: 22/5/2024 3:19 下午
 */
@Slf4j
@Component
public class MsgConsumerFactory {

    private final ConcurrentMap<String, DynamicConsumer> concurrentMap = new ConcurrentHashMap<>();

    @Resource
    private CusEsRepository cusEsRepository;

    @Resource
    private PaasFeign paasFeign;

    @Resource
    private CacheService cacheService;

    @Resource
    private ConsumerGenerate consumerGenerate;

    @Resource
    private LogRecordConsumer logRecordConsumer;

    @Resource
    private LogProviderProperties providerProperties;

    @PostConstruct
    public void initDynamicMsgConsumer() {
        Result<String> kvCache = paasFeign.getKv(MsgConstant.CACHE_PAAS_KV_LOG_RECORD);
        if (kvCache.hasPresent() && !StringUtils.isEmpty(kvCache.getData())) {
            List<String> tenantIds = FastjsonUtil.toList(kvCache.getData(), String.class);
            log.info("init dynamic msg consumers : {}", kvCache.getData());
            tenantIds.forEach(it -> {
                DynamicConsumer recordConsumer;
                try {
                    recordConsumer = consumerGenerate.genConsumer(ExchangeType.DIRECT,
                            logRecordConsumer, MsgConstant.EXCHANGE_KEY_RECORD_EVENT,
                            String.format("%s_%s", MsgConstant.QUEUE_KEY_RECORD_EVENT, it),
                            String.format("%s_%s", MsgConstant.ROUTING_KEY_RECORD_EVENT, it),
                            false, true, true);
                    recordConsumer.start();
                    concurrentMap.putIfAbsent(it, recordConsumer);
                } catch (Exception e) {
                    log.error("create dynamic consume error: {}", e.getMessage(), e);
                }

            });
            cacheService.cacheValue(MsgConstant.CACHE_PAAS_KV_LOG_RECORD, FastjsonUtil.toJson(tenantIds));
            return;
        }
        log.info("unResolve kv config from paas, please retry....");
    }

    public boolean logRecordStatus() {
        String tenantId = UserContextUtil.getTenantId();
        String cacheContent = cacheService.getValue(MsgConstant.CACHE_PAAS_KV_LOG_RECORD);
        return Optional.ofNullable(cacheContent).map(it -> {
            List<String> tenantIds = FastjsonUtil.toList(cacheContent, String.class);
            return tenantIds.contains(tenantId);
        }).orElse(false);
    }

    /**
     *
     * 关闭消息监听
     * @param tenantId 当前租户
     */
    public void stopRecordConsumer(String tenantId) {
        synchronized (concurrentMap) {
            DynamicConsumer consumer = Optional.ofNullable(concurrentMap.get(tenantId)).orElse(null);
            if (consumer != null) {
                consumer.stop();
                log.info("stop record consumer success...");
            }
            updateCacheData(tenantId, false);
        }
    }


    public void startRecordConsumer(String tenantId) {
        synchronized (concurrentMap) {
            DynamicConsumer dynamicConsumer = Optional.ofNullable(concurrentMap.get(tenantId))
                    .orElseGet(() -> {
                        try {
                            DynamicConsumer recordConsumer = consumerGenerate.genConsumer(ExchangeType.DIRECT,
                                    logRecordConsumer, MsgConstant.EXCHANGE_KEY_RECORD_EVENT,
                                    String.format("%s_%s", MsgConstant.QUEUE_KEY_RECORD_EVENT, tenantId),
                                    String.format("%s_%s", MsgConstant.ROUTING_KEY_RECORD_EVENT, tenantId),
                                    false, true, true);
                            concurrentMap.putIfAbsent(tenantId, recordConsumer);
                            return recordConsumer;
                        } catch (Exception e) {
                            log.error("startRecordConsumer error with create dynamic consumer, {}", e.getMessage(), e);
                        }
                        return null;
                    });
            if (dynamicConsumer != null) {
                initStorageProvider(tenantId);
                dynamicConsumer.start();
                updateCacheData(tenantId, true);
            }
        }
    }

    private void initStorageProvider(String tenantId) {
        String provider = providerProperties.getProvider();
        if (provider.equals("db")) {
            TableInitService tableHelper = SpringUtil.getBean(TableInitService.class);
            tableHelper.initRecordTable(tenantId);
        } else {
            cusEsRepository.createEsIndex(tenantId);
        }
    }

    private void updateCacheData(String tenantId, boolean start) {
        String cacheTenantIds = cacheService.getValue(MsgConstant.CACHE_PAAS_KV_LOG_RECORD);
        List<String> tenantList = Optional.ofNullable(cacheTenantIds).map(it -> FastjsonUtil.toList(it, String.class))
                .orElse(Lists.newArrayList());
        if (start) {
            if (!tenantList.contains(tenantId)) {
                tenantList.add(tenantId);
            }
        } else {
            tenantList.remove(tenantId);
        }
        cacheService.cacheValue(MsgConstant.CACHE_PAAS_KV_LOG_RECORD, FastjsonUtil.toJson(tenantList));
        KvDto kvDto = new KvDto();
        kvDto.setProperty(MsgConstant.CACHE_PAAS_KV_LOG_RECORD);
        kvDto.setContent(FastjsonUtil.toJson(tenantList));
        paasFeign.saveKv(kvDto);
    }
}
