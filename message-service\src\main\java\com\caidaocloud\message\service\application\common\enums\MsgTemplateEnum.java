package com.caidaocloud.message.service.application.common.enums;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 消息模版类型枚举
 *
 * <AUTHOR>
 * @date 2022/6/15
 **/
public enum MsgTemplateEnum {

    SMS("0", "短信模版", "sms template"),
    EMAIL("1", "邮件模版","emial template"),
    SYSTEM("2", "系统模版", "system template"),
    APP("3", "app模版", "app template"),
    DING_TALK("4", "钉钉消息通知模版", "ding_talk template"),
    DING_TALK_TODO("5", "钉钉待办通知模版", "ding_talk_todo template");

    private String index;
    private String name;
    private String enName;

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    MsgTemplateEnum(String index, String name, String enName) {
        this.index = index;
        this.name = name;
        this.enName = enName;
    }

    public static MsgTemplateEnum getEnumByIndex(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        MsgTemplateEnum[] values = MsgTemplateEnum.values();
        for (MsgTemplateEnum msgTemplateEnum : values) {
            if (msgTemplateEnum.index.equals(value)) {
                return msgTemplateEnum;
            }
        }
        return null;
    }

}
