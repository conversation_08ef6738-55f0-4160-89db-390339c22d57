package com.caidaocloud.message.service.record.controller;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.message.service.record.consumer.MsgConsumerFactory;
import com.caidaocloud.message.service.record.dto.LogRecordQueryDto;
import com.caidaocloud.message.service.record.service.RecordService;
import com.caidaocloud.message.service.record.util.UserContextUtil;
import com.caidaocloud.message.service.record.vo.LogRecordVo;
import com.caidaocloud.record.core.beans.LogRecordData;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * created by: FoAng
 * create time: 22/5/2024 3:19 下午
 */
@Api(value = "/api/log/record/v1", description = "日志操作", tags = "v0.6")
@RestController
@RequestMapping("/api/log/record/v1")
@AllArgsConstructor
public class LogRecordController {

    private RecordService recordService;

    private MsgConsumerFactory msgConsumerFactory;

    @PostMapping("/status")
    @ApiOperation("获取日志配置状态")
    public Result<Boolean> status() {
        return Result.ok(msgConsumerFactory.logRecordStatus());
    }

    @PostMapping("/enable")
    @ApiOperation("开启日志记录")
    public Result<?> enableRecord() {
        String tenantId = UserContextUtil.getTenantId();
        PreCheck.preCheckNotNull(tenantId, "参数错误");
        msgConsumerFactory.startRecordConsumer(tenantId);
        return Result.ok(true);
    }

    @PostMapping("/disable")
    @ApiOperation("关闭日志记录")
    public Result<?> disableRecord() {
        String tenantId = UserContextUtil.getTenantId();
        PreCheck.preCheckNotNull(tenantId, "参数错误");
        msgConsumerFactory.stopRecordConsumer(tenantId);
        return Result.ok(true);
    }

    @ApiOperation(value = "保存操作日志", hidden = true)
    @PostMapping("/save")
    public Result<?> saveRecord(@RequestBody LogRecordData recordData) {
        recordService.saveRecordData(recordData);
        return Result.ok();
    }

    @PostMapping("/page")
    @ApiOperation("日志分页")
    public Result<PageResult<LogRecordVo>> page(@RequestBody LogRecordQueryDto dto) {
        return Result.ok(recordService.pageRecord(dto));
    }

    @PostMapping("/export")
    @ApiOperation("导出")
    public void export(@RequestBody LogRecordQueryDto dto, HttpServletResponse response) {
        recordService.export(dto, response);
    }
}
