package com.caidaocloud;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.common.constant.LangCodeConstant;
import com.caidaocloud.message.service.application.msg.service.EmailConfigService;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.infrastructure.utils.LangUtil;
import com.caidaocloud.message.service.interfaces.dto.msg.EmailConfigDto;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.StringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * created by: FoAng
 * create time: 13/10/2022 2:26 下午
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class SmtpMailTest {

    @Resource
    private EmailConfigService emailConfigService;

    private void checkConfig(EmailConfigDto config){
        // 不启用邮件
        if (config.getStatus() != null && "0".equals(config.getStatus().getValue())) {
            return;
        }

        if(null != config.getEmailModel() && "1".equals(config.getEmailModel().getValue())){
            // 适配模式无需校验
            return;
        }

        String lang = LangUtil.getMsg(LangCodeConstant.code_70000);
        String msg = String.format(lang, LangUtil.getMsg(LangCodeConstant.code_70001));
        PreCheck.preCheckArgument(StringUtil.isBlank(config.getEmailSmtp()), msg);

        msg = String.format(lang, LangUtil.getMsg(LangCodeConstant.code_70002));
        PreCheck.preCheckArgument(StringUtil.isBlank(config.getEmailFrom()), msg);

        msg = String.format(lang, LangUtil.getMsg(LangCodeConstant.code_70003));
        PreCheck.preCheckArgument(StringUtil.isBlank(config.getEmailPwd()), msg);

        msg = String.format(lang, LangUtil.getMsg(LangCodeConstant.code_70004));
        PreCheck.preCheckArgument(StringUtil.isBlank(config.getEmailNick()), msg);
    }

    private EmailConfigDto generateMailConfigDto() {
        EmailConfigDto configDto = new EmailConfigDto();
        /*configDto.setEmailFrom("<EMAIL>");
        configDto.setEmailSmtp("mailhostint.roche.com");
        configDto.setEmailNick("hello");
        configDto.setEmailPort(25);
        configDto.setTestMail("<EMAIL>");*/

        configDto.setEmailFrom("<EMAIL>");
        configDto.setEmailPwd("!@#123qwe");
        configDto.setEmailSmtp("smtp.mxhichina.com");
        configDto.setEmailNick("hello11");
        configDto.setEmailPort(-1);
        configDto.setEnableSsl(true);
        configDto.setTestMail("<EMAIL>");

        EnumSimple mode = new EnumSimple();
        mode.setValue("0");
        configDto.setEmailModel(mode);

        return configDto;
    }

    @Before
    public void initUser(){
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("8");
        userInfo.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);

        UserInfo user = new UserInfo();
        user.setTenantId(String.valueOf(8));
        user.doSetUserId(0L);
        UserContext.setCurrentUser(user);
    }

    @Test
    @SneakyThrows
    public void sendSmtpMail() {
        //EmailConfigDto configDto = generateMailConfigDto();
        EmailConfigDto configDto = careerMailConfigDto();
        checkConfig(configDto);
        configDto.setTestMail("<EMAIL>");
        emailConfigService.sendTestEmail(configDto);

        configDto.setTestMail("<EMAIL>");
        for (int i = 1; i < 250; i++) {
            emailConfigService.sendTestEmail(configDto);
            if(i % 5 == 0){
                Thread.sleep(3000L);
            }
        }

        configDto.setTestMail("<EMAIL>");
        emailConfigService.sendTestEmail(configDto);
    }

    private EmailConfigDto careerMailConfigDto() {
        EmailConfigDto configDto = new EmailConfigDto();
        configDto.setEmailFrom("<EMAIL>");
        configDto.setEmailSmtp("smtp.qiye.aliyun.com");
        configDto.setEmailNick("hello");
        configDto.setEmailPwd("pass@word1");
        configDto.setEmailPort(465);
        configDto.setEnableSsl(true);
        configDto.setTestMail("<EMAIL>");
        EnumSimple mode = new EnumSimple();
        mode.setValue("0");
        configDto.setEmailModel(mode);

        return configDto;
    }
}
