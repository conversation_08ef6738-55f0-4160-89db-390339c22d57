package com.caidaocloud.message.service.interfaces.vo.msg;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/15
 **/
@Data
@ApiModel("消息配置列表vo")
@Slf4j
public class MsgConfigListVo {

    @ApiModelProperty("业务id")
    private String bid;

    @ApiModelProperty("通知名称名称")
    private String name;

    @ApiModelProperty("通知名称名称多语言")
    private Map<String,Object> i18nName;


    @ApiModelProperty("通知类型")
    private String msgType;

    @ApiModelProperty("通知类型value")
    private String msgTypeValue;

    @ApiModelProperty("通知对象")
    private String notifyObject;

    @ApiModelProperty("通知方式")
    private String func;

    // @ApiModelProperty("通知轮次")
    // private String round;

    @ApiModelProperty("通知天数")
    private String day;

    @ApiModelProperty("发送途径")
    private String channel;

    @ApiModelProperty("状态")
    private EnumSimple status;

}
