package com.caidaocloud.message.service.application.email.dto;

import com.caidaocloud.message.service.application.email.service.SendMail;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

@Data
@Slf4j
public class HtmlMailDto implements Serializable {
    //附件地址
    private String affix;
    private String affixName;
    private String smtp = "";
    private String user = "";
    private String password = "";
    private String from = "";
    private String to = "";
    private String subject = "";
    private String content = "";
    private String nick = "";
    private String port = "465";

    public boolean send()throws Exception {
        // 创建一个授权验证对象
        SendMail.sendMessage(smtp, user, from, password, port,nick, to, subject, content, affix, affixName, "");
        return true;
    }

    public boolean send2(EmailTmplateMessage template) throws Exception {
        SendMail.sendMessage2(smtp, password, port, from, nick, template);
        return true;
    }

    public void sendBatchEmail(EmailTmplateMessage[] emails)throws Exception {
        if (emails != null && emails.length > 0) {
            for (int i = 0; i < emails.length; i++) {
                send2(emails[i]);
            }
        }
    }

    public void setAffix(String affix, String affixName) {
        this.affix = affix;
        this.affixName = affixName;
    }
}
