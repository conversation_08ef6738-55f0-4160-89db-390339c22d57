<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    
    <parent>
        <groupId>com.caidaocloud</groupId>
        <artifactId>caidaocloud-parent</artifactId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>
    
    <modelVersion>4.0.0</modelVersion>
    <artifactId>record-core</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>
    
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <totallylazy.version>2.286</totallylazy.version>
    </properties>
    
    <dependencies>
    
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
            <version>5.1.2.RELEASE</version>
        </dependency>
    
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>caidaocloud-commons</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>galaxy-service-security</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <version>3.4.5</version>
        </dependency>
    
        <dependency>
            <groupId>com.googlecode.totallylazy</groupId>
            <artifactId>totallylazy</artifactId>
            <version>${totallylazy.version}</version>
        </dependency>
    
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>galaxy-service-mq</artifactId>
            <scope>provided</scope>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>metadata-sdk</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
    
        <dependency>
            <groupId>net.dreamlu</groupId>
            <artifactId>mica-auto</artifactId>
            <version>1.1.0</version>
            <scope>provided</scope>
        </dependency>
    
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <executions>
                    <execution>
                        <id>default-deploy</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>deploy</goal>
                        </goals>
                        <configuration>
                            <skip>false</skip>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>