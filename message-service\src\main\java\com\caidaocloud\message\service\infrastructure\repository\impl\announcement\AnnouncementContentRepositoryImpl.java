package com.caidaocloud.message.service.infrastructure.repository.impl.announcement;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementContent;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementContentRepository;
import com.caidaocloud.message.service.domain.base.repository.BaseRepositoryImpl;
import org.springframework.stereotype.Repository;

/**
 * 公告内容Repository实现类
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Repository
public class AnnouncementContentRepositoryImpl extends BaseRepositoryImpl<AnnouncementContent> implements AnnouncementContentRepository {
    
    @Override
    public AnnouncementContent findByAnnouncementId(String announcementId) {
        DataFilter filter = getBaseFilter()
                .andEq("announcementId", announcementId);
        
        PageResult<AnnouncementContent> result = DataQuery.identifier(AnnouncementContent.identifier)
                .decrypt()
                .specifyLanguage()
                .queryInvisible()
                .filter(filter, AnnouncementContent.class);
        
        if (result != null && result.getItems() != null && !result.getItems().isEmpty()) {
            return result.getItems().get(0);
        }
        return null;
    }
    
    @Override
    public int deleteByAnnouncementId(String announcementId) {
        DataFilter filter = getBaseFilter()
                .andEq("announcementId", announcementId);
        
        DataDelete.identifier(AnnouncementContent.identifier).batchDelete(filter);
        return 1;
    }
}
