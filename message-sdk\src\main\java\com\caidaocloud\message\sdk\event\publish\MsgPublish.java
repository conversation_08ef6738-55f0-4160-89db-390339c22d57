package com.caidaocloud.message.sdk.event.publish;

import com.caidaocloud.message.sdk.dto.MsgNoticeDto;
import com.caidaocloud.message.sdk.event.constant.MQConstant;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 发送消息通知MQ到消息中心
 */
@Slf4j
@Service
public class MsgPublish {
    @Resource
    private MqMessageProducer<CommonMessage> producer;

    public void msgNoticePublish(MsgNoticeDto message, Integer delayTime) {
        CommonMessage msgContent = new CommonMessage();
        msgContent.setBody(FastjsonUtil.toJson(message));
        msgContent.setExchange(MQConstant.EXCHANGE);
        msgContent.setRoutingKey(String.format(MQConstant.PLATFORM_TENANT_SEPARATOR, MQConstant.ROUTING_KEY, message.getTenantId()));

        log.info("tenant delayed platform publish time={} body={}", System.currentTimeMillis(), msgContent.getBody());
        producer.convertAndSend(msgContent, delayTime);
    }

    public static class CommonMessage extends RabbitBaseMessage {

    }
}
