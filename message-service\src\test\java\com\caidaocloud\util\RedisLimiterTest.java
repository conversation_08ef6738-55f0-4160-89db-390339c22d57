package com.caidaocloud.util;

import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.email.service.RedisLimiter;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class RedisLimiterTest {
    @Resource
    private RedisLimiter redisLimiter;
    @Test
    public void testLimiter(){
        for (int i = 1; i < 50; i ++){
            Thread thread = new Thread(() -> {
                // 每分钟内只允许5次邮件发送
                boolean allowed = redisLimiter.isAllowed("<EMAIL>", 1, 60, 12);
                System.out.println(Thread.currentThread().getName() + "：" + allowed);
            });
            thread.setName("testLimiter_" + i);
            thread.start();
            if(i % 5 == 0){
                try {
                    System.out.println("休息一分钟,重置限流");
                    Thread.sleep(60000L);
                } catch (Exception e){

                }
            }
        }
        System.out.println("------------");
        try {
            Thread.sleep(70000L);
        } catch (Exception e){

        }
    }
}
