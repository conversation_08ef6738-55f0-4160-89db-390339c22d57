package com.caidaocloud.message.service.application.common.service;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class MetadataService {
    @Resource
    private MetadataOperatorService metadataOperatorService;

    public MetadataVo getMetadata(String identifier){
        MetadataVo metadata = metadataOperatorService.load(identifier);
        return metadata;
    }

    public List<MetadataPropertyVo> getMetadataProperty(String identifier){
        MetadataVo metadata = getMetadata(identifier);

        return getMetadataProperty(metadata);
    }

    public List<MetadataPropertyVo> getMetadataProperty(MetadataVo metadata){
        List<MetadataPropertyVo> allProperties = Optional.ofNullable(metadata).map(MetadataVo::getStandardProperties).orElse(Lists.newArrayList());
        allProperties.addAll(Optional.ofNullable(metadata).map(MetadataVo::getInheritedStandardProperties).orElse(Lists.newArrayList()));
        return allProperties;
    }

    public MetadataPropertyVo getPropertyDef(String identifier, String prop){
        List<MetadataPropertyVo> propList = getMetadataProperty(identifier);
        PreCheck.preCheckArgument(null == propList || propList.isEmpty(), "模型或属性不存在");

        return propList.stream().filter(metadataPropertyVo -> metadataPropertyVo.getProperty().equals(prop))
                .findFirst().orElseThrow(() -> new ServerException("模型或属性不存在"));
    }
}
