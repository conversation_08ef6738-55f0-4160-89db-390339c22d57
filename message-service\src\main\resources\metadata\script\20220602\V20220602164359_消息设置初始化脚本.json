[{"action": "create", "entityDef": {"identifier": "entity.message.MsgConfig", "name": "消息设置", "owner": "message", "label": "name", "timelineEnabled": false, "standardProperties": [{"property": "name", "name": "通知名称", "dataType": "String", "widgetType": "text", "rules": [{"type": "required", "value": "true"}, {"type": "max<PERSON><PERSON><PERSON>", "value": 30}]}, {"property": "msgType", "name": "通知类型", "dataType": "Enum", "widgetType": "enum", "visible": true, "modifiable": false, "defaultValue": {"value": 1, "type": "Enum"}, "enumDef": [{"value": 1, "display": "未签署提醒"}, {"value": 2, "display": "电子合同签署提醒"}, {"value": 3, "display": "电子合同作废提醒"}, {"value": 4, "display": "手动推送"}]}, {"property": "notifyObject", "name": "通知对象", "dataType": "String", "widgetType": "text"}, {"property": "condition", "name": "匹配条件", "dataType": "String", "widgetType": "text", "rules": [{"type": "max<PERSON><PERSON><PERSON>", "value": 3000}]}, {"property": "func", "name": "通知方式", "dataType": "Enum", "widgetType": "enum", "visible": true, "modifiable": false, "defaultValue": {"value": 0, "type": "Enum"}, "enumDef": [{"value": 0, "display": "单次通知"}, {"value": 1, "display": "循环通知"}]}, {"property": "round", "name": "通知轮次", "dataType": "Enum", "widgetType": "enum", "visible": true, "modifiable": false, "defaultValue": {"value": 0, "type": "Enum"}, "enumDef": [{"value": 0, "display": "提前"}, {"value": 1, "display": "当天"}, {"value": 2, "display": "延后"}]}, {"property": "day", "name": "通知天数", "dataType": "Integer", "widgetType": "int"}, {"property": "sendTime", "name": "发送时间", "dataType": "Integer", "widgetType": "int"}, {"property": "loop", "name": "通知周期", "dataType": "Integer", "widgetType": "int"}, {"property": "channel", "name": "发送途径", "dataType": "String", "widgetType": "text"}, {"property": "status", "name": "状态", "dataType": "Enum", "widgetType": "enum", "visible": true, "modifiable": false, "defaultValue": {"value": 0, "type": "Enum"}, "enumDef": [{"value": 0, "display": "未启用"}, {"value": 1, "display": "已启用"}, {"value": 2, "display": "已停用"}]}, {"property": "templateBid", "name": "绑定消息模版bid", "dataType": "String", "widgetType": "text"}]}}]