package com.caidaocloud.message.service.application.message.notifier.service;

import java.util.List;

import javax.annotation.Resource;

import com.caidaocloud.hr.service.dto.EmpWorkInfoDto;
import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.service.application.feign.MasterdataFeignClientV2;
import com.caidaocloud.message.service.application.feign.UserServiceFeignClent;
import com.caidaocloud.message.service.application.feign.dto.UserDetailInfoVo;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDataDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.message.service.application.message.notifier.input.NotifierDataFactory;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

/**
 * 发起人通知对象
 */
@Slf4j
@Service
public class NotifyCommonInitiatorService implements INotifyTargetService {
    @Resource
    private UserServiceFeignClent userServiceFeignClent;
    @Resource
    private MasterdataFeignClientV2 masterdataFeignClientV2;

    @Override
    public String getNotifyTarget() {
        return NoticeTarget.COMMON_INITIATOR.getIndex();
    }

    @Override
    public void getNotifier(List<NotifierDto> notifier, NotifyObjectDto obj) {
        Long userId = SecurityUserUtil.getSecurityUserInfo().getUserId();
        UserDetailInfoVo userDetailInfoVo = userServiceFeignClent.getUserById(userId).getData();
        if (null == userDetailInfoVo) {
            log.warn("userId:{} is not exist", userId);
            return;
        }
        if (null == userDetailInfoVo.getEmpId()) {
            log.warn("userId:{} is not bound to employee", userId);
            return;
        }
        EmpWorkInfoVo empWorkInfoDto = masterdataFeignClientV2.loadEmpWorkInfo(String.valueOf(userDetailInfoVo.getEmpId()), System.currentTimeMillis())
                .getData();
        if (empWorkInfoDto == null) {
            log.warn("workno:{} is not exist empInfo,user id:{}", userDetailInfoVo.getEmpId(), userDetailInfoVo.getUserId());
        }

        notifier.add(convert(ObjectConverter.convert(empWorkInfoDto, EmpWorkInfoDto.class)));
    }

}
