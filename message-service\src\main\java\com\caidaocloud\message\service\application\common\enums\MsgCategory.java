package com.caidaocloud.message.service.application.common.enums;

/**
 * <AUTHOR>
 * @date 2022/6/15
 */
public enum MsgCategory {
    MSG("0","短信模板","短信"),
    EMAIL("1","邮件模版","邮件"),
    SYS("2","系统模版","系统"),
    APP("3","APP模版","APP"),
    DINGDING("4","钉钉模板 ","钉钉"),
    DINGTODO("5","钉钉待办","钉钉待办");

    public String getCode() {
        return code;
    }

    public String getText() {
        return text;
    }

    public String getAlias() {
        return alias;
    }

    private final String code;
    private final String text;
    private final String alias;


    MsgCategory(String code, String text, String alias) {
        this.code = code;
        this.text = text;
        this.alias = alias;
    }

    public static MsgCategory getByCode(String code) {
        if (code == null) {
            return null;
        }

        for (MsgCategory e : MsgCategory.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }


    public static String getAliasByCode(String code) {
        MsgCategory e;
        if ((e = getByCode(code)) == null) {
            return "";
        }
        return e.getAlias();
    }


}
