package com.caidaocloud.message.service.domain.template.enums;

public enum TemplateCategory {
    SMS("0", "短信模版", "SMS Template"),
    EMAIL("1", "邮件模版", "EMAIL Template"),
    SYS("2", "系统模版", "System Template"),
    APP("3", "APP模版", "App Template");

    private String index;
    private String name;
    private String enName;

    TemplateCategory(String index, String name, String enName){
        this.index = index;
        this.name = name;
        this.enName = enName;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }
}
