package com.caidaocloud.message.service.application.msg.event;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.service.application.msg.cron.ConditionTaskService;
import com.caidaocloud.message.service.application.msg.dto.TemplateRefreshMessageDto;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.interfaces.dto.msg.MsgConfigQueryDto;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class CommonMsgSubscribe {
    @Resource
    private ConditionTaskService conditionTaskService;

    @RabbitHandler
    @RabbitListener(
        bindings = @QueueBinding(
            value = @Queue(value = "caidaocloud.message.template.refresh.queue", durable = "true"),
            exchange = @Exchange(value = "caidaocloud.message.fac.direct.exchange"),
            key = {"routingKey.message.template.refresh"}
        )
    )
    public void process(String message) {
        log.info("Subscribe msg config template refresh Message={}", message);
        MsgConfigQueryDto page = new MsgConfigQueryDto();
        page.setPageNo(1);
        page.setPageSize(300);
        page.setFilterEmptyCondition(true);
        page.setStatus("1");
        try {
            TemplateRefreshMessageDto msg = FastjsonUtil.toObject(message, TemplateRefreshMessageDto.class);
            String tenantId = msg.getTenantId();
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(msg.getTenantId());
            final String userId = msg.getUserId();
            userInfo.setEmpId(0L);
            userInfo.setUserId(Long.valueOf(userId));
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            UserInfo user = new UserInfo();
            user.setTenantId(msg.getTenantId());
            user.setStaffId(0L);
            user.doSetUserId(userInfo.getUserId());
            UserContext.setCurrentUser(user);
            if(StringUtil.isNotEmpty(msg.getBid())){
                conditionTaskService.genEmpTemplateRelationship(msg.getBid(), msg.getProgress());
            } else if(StringUtil.isNotEmpty(msg.getEmpId())){
                // 刷新单员工数据
                page.setEmpId(msg.getEmpId());
                conditionTaskService.doMsgTemplate(tenantId, msg.getProgress(), page);
            } else {
                conditionTaskService.doMsgTemplate(tenantId, msg.getProgress(), page);
            }
        } catch (Exception ex) {
            log.error("process template refresh Message err,{}", ex.getMessage(), ex);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }
}
