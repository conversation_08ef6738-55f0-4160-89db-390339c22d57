package com.caidaocloud.message.service.application.msg.service;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.message.service.application.app.service.AppNotifyFactory;
import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import com.caidaocloud.message.service.application.email.dto.MailMsgSender;
import com.caidaocloud.message.service.application.email.service.EmailNotifyFactory;
import com.caidaocloud.message.service.application.log.dto.MessageLogRecordDto;
import com.caidaocloud.message.service.application.log.repository.IMessageLogRecordRepository;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.message.service.application.message.notifier.service.NotifyTargetFactory;
import com.caidaocloud.message.service.application.message.send.sms.DingMessageNotifyApi;
import com.caidaocloud.message.service.application.message.send.sms.SmsMessageNotifyFactory;
import com.caidaocloud.message.service.application.msg.dto.DingtalkMsgDto;
import com.caidaocloud.message.service.application.msg.dto.DingtalkToDoNoticeDto;
import com.caidaocloud.message.service.application.msg.dto.MsgDto;
import com.caidaocloud.message.service.application.notice.service.NoticeMessageService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * MsgService
 *
 * <AUTHOR>
 * @date 2022/6/9 下午2:54
 */
@Slf4j
@Service
public class MsgService {

    @Value("${caidaocloud.msg.dingtalk.cropId:}")
    private String CROP_ID;

    @Value("${caidaocloud.msg.dingtalk.appKey:}")
    private String APP_KEY ;

    @Value("${caidaocloud.msg.dingtalk.appSecret:}")
    private String APP_SECRET ;

    @Resource
    private MsgConfigService msgConfigService;
    @Resource
    private DingMessageNotifyApi dingMessageNotifyApi;
    @Resource
    private IMessageLogRecordRepository iMessageLogRecordRepository;
    @Resource
    private NoticeMessageService noticeMessageService;

    public static final String DING_STATUS = "1";

    public static final String DELETE_DING_DING_NOTICE = "2";

    public MessageLogRecordDto saveMessageLogRecord(String message, MsgDto msgDto) {
        MessageLogRecordDto recordDto = new MessageLogRecordDto();
        recordDto.setEmpId("0");
        recordDto.setName("工作流");
        recordDto.setType("0");
        recordDto.setMdc(StringUtils.isNotBlank(msgDto.getMdc()) ? msgDto.getMdc() : SnowUtil.nextId());
        recordDto.setMsgFrom("workflow");
        recordDto.setMsgConfig(msgDto.getMsgConfig());
        doDingMsgRecord(recordDto, msgDto);
        doDingTodoRecord(recordDto, msgDto);
        recordDto.setContent(FastjsonUtil.toJson(msgDto));
        recordDto.setChannel(StringUtils.join(msgDto.getChannel(), ","));
        EnumSimple status = new EnumSimple();
        status.setText(SendStatusEnum.SUCESS.txt);
        status.setValue(SendStatusEnum.SUCESS.status);
        recordDto.setStatus(status);
        iMessageLogRecordRepository.saveMessageLogRecord(recordDto);
        return recordDto;
    }

    private void doDingMsgRecord(MessageLogRecordDto recordDto, MsgDto msgDto){
        DingtalkMsgDto dingTalkMsg = msgDto.getDingTalkMsg();
        if(null == dingTalkMsg || null == dingTalkMsg.getEmpIds() || dingTalkMsg.getEmpIds().isEmpty()){
            return;
        }

        NotifyObjectDto notify = new NotifyObjectDto();
        notify.setDataTime(System.currentTimeMillis());
        notify.setType(0);
        notify.setEmpIds(dingTalkMsg.getEmpIds());
        List<NotifierDto> distinctNotifier = NotifyTargetFactory
                .getDistinctNotifier(Lists.newArrayList(NoticeTarget.ASSIGN_EMP.toString()), notify);
        if(null == distinctNotifier || distinctNotifier.isEmpty()){
            return;
        }

        NotifierDto notifierDto = distinctNotifier.get(0);
        recordDto.setEmpId(notifierDto.getEmpId());
        recordDto.setName(notifierDto.getName());
        recordDto.setWorkno(notifierDto.getWorkno());
        distinctNotifier.forEach(n -> dingTalkMsg.addMobile(n.getPhone()));
    }

    private void doDingTodoRecord(MessageLogRecordDto recordDto, MsgDto msgDto){
        DingtalkToDoNoticeDto dingNotice = msgDto.getDingtalkToDoNoticeDto();
        if(null == dingNotice || null == dingNotice.getEmpIds() || dingNotice.getEmpIds().isEmpty()){
            return;
        }

        NotifyObjectDto notify = new NotifyObjectDto();
        notify.setDataTime(System.currentTimeMillis());
        notify.setType(0);
        notify.setEmpIds(dingNotice.getEmpIds());
        List<NotifierDto> distinctNotifier = NotifyTargetFactory
                .getDistinctNotifier(Lists.newArrayList(NoticeTarget.ASSIGN_EMP.toString()), notify);
        if(null == distinctNotifier || distinctNotifier.isEmpty()){
            return;
        }

        NotifierDto notifierDto = distinctNotifier.get(0);
        recordDto.setEmpId(notifierDto.getEmpId());
        recordDto.setName(notifierDto.getName());
        recordDto.setWorkno(notifierDto.getWorkno());
        distinctNotifier.forEach(n -> dingNotice.addMobile(n.getPhone()));
    }

    /**
     * 消息处理
     *
     * @param msgDto
     */
    public void doMsg(MsgDto msgDto) {
        log.info("MsgService doMsg msgDto={}", FastjsonUtil.toJson(msgDto));
        //更新钉钉待办任务状态消息：更新成已完成
        if (DING_STATUS.equals(msgDto.getDingStatus())) {
            try {
                dingMessageNotifyApi.updateDingTaskStatus(msgDto.getDingtalkToDoNoticeDto());
            } catch (Exception e) {
                log.error("doMsg updateDingTaskStatus dingtalkToDoNoticeDto ={}", msgDto.getDingtalkToDoNoticeDto());
            }
            return;
        }
        if (DELETE_DING_DING_NOTICE.equals(msgDto.getDingStatus())) {
            try {
                dingMessageNotifyApi.deleteDingTaskStatus(msgDto.getDingtalkToDoNoticeDto());
            } catch (Exception e) {
                log.error("doMsg deleteDingTaskStatus dingtalkToDoNoticeDto ={}", msgDto.getDingtalkToDoNoticeDto());
            }
            return;
        }

        if (null == msgDto || StringUtil.isEmpty(msgDto.getChannel())) {
            return;
        }

        // 检查消息配置是否已启用
        Boolean msgConfigStatus = msgConfigService.checkMsgConfigEnable(msgDto.getTenantId(), msgDto.getMsgConfig());
        if (!msgConfigStatus) {
            log.info("doMsg msgConfigStatus is not enable msgConfig={}", msgDto.getMsgConfig());
            return;
        }

        /**
         * 根据消息发送渠道发送各个渠道的消息
         * 这里可以采用责任链模式
         */
        List<String> channelList = msgDto.getChannel();
        boolean sendSmsMessageStatus = true;
        boolean sendEmailStatus = true;
        boolean sendDingtalkStatus = true;
        boolean sendDingtalkToDoStatus = true;
        for (String channel : channelList) {
            if(",,,4".equals(channel)){
                channel = "1";
            }
            switch (channel) {
                case "0":
                    // 短信
                    sendSmsMessageStatus = sendSmsMessage(msgDto);
                    break;
                case "1":
                    // 邮件通知
                    sendEmailStatus =  sendEmail(msgDto);
                    break;
                case "2":
                    // 系统通知
                    sendEmailStatus = sendNoticeMsg(msgDto);
                    break;
                case "3":
                    // app 通知
                    AppNotifyFactory.getService("adapterapp").batchSendMsg(msgDto.getAppMsg());
                    break;
                case "4":
                    // 钉钉消息通知
                    sendDingtalkStatus =  sendDingtalkMessage(msgDto);
                    break;
                case "5":
                    // 钉钉待办通知
                    sendDingtalkToDoStatus = sendDingtalkToDoNoticeMessage(msgDto);
                    break;
                default:
                    break;
            }
        }

        //发送成功，更新消息记录状态
        if (sendSmsMessageStatus && sendEmailStatus && sendDingtalkStatus && sendDingtalkToDoStatus) {
            iMessageLogRecordRepository.updateMessageLogRecordStatus(msgDto.getMdc(), SendStatusEnum.SUCESS);
        } else {
            iMessageLogRecordRepository.updateMessageLogRecordStatus(msgDto.getMdc(), SendStatusEnum.FAIL);
        }
    }

    /**
     * 发送钉钉待办通知
     * @param msgDto
     */
    private boolean sendDingtalkToDoNoticeMessage(MsgDto msgDto) {
        boolean sendStatus = true;
        try {
            sendStatus = dingMessageNotifyApi.sendDingtalkToDoNoticeMessage(msgDto.getDingtalkToDoNoticeDto());
        } catch (Exception e){
            log.error("sendDingtalkToDoNoticeMessage error,{}", e.getMessage(), e);
            sendStatus = false;
            //发送失败更新发送状态
            msgDto.getDingtalkToDoNoticeDto().setStatus(SendStatusEnum.FAIL);
            iMessageLogRecordRepository.updateMessageLogRecordContent(msgDto.getMdc(), FastjsonUtil.toJson(msgDto));

        }
        return sendStatus;
    }

    public boolean sendEmail(MsgDto msgDto) {
        boolean sendStatus = true;
        try {
            // 这里先默认邮件发送模式为适配模式
            MailMsgSender sender = ObjectConverter.convert(msgDto.getEmailMsg(), MailMsgSender.class);
            sender.setTenantId(msgDto.getTenantId());

            log.info("sender email msg start, sender = {}.", FastjsonUtil.toJson(sender));
            // 邮件发送渠道
            EmailNotifyFactory.getEmailNotify().send(sender);
        } catch (Exception e){
            log.error("sender email msg err,{}", e.getMessage(), e);
            sendStatus = false;
            //发送失败更新发送状态
            msgDto.getEmailMsg().setStatus(SendStatusEnum.FAIL);
            iMessageLogRecordRepository.updateMessageLogRecordContent(msgDto.getMdc(), FastjsonUtil.toJson(msgDto));
        }
        log.info("sender email msg success.");
        return sendStatus;
    }


    /**
     * 发送系统消息
     * @param msgDto
     * @return
     */
    public boolean sendNoticeMsg(MsgDto msgDto) {
        boolean sendStatus = true;
        try {
            if (msgDto.getNoticeMsg() != null) {
                noticeMessageService.sendNoticeMessage(msgDto.getNoticeMsg());
            }
        } catch (Exception e){
            log.error("sender system notice err,{}", e.getMessage(), e);
            sendStatus = false;
            msgDto.getNoticeMsg().setStatus(SendStatusEnum.FAIL);
            iMessageLogRecordRepository.updateMessageLogRecordContent(msgDto.getMdc(), FastjsonUtil.toJson(msgDto));
        }
        return sendStatus;
    }


    public boolean sendSmsMessage(MsgDto msgDto) {
        boolean sendStatus = true;
        try {
            SmsMessageNotifyFactory.getSmsService().sendMessage(msgDto.getMessage());
        } catch (Exception e){
            log.error("sender sms msg err,{}", e.getMessage(), e);
            sendStatus = false;
            //发送失败更新发送状态
            msgDto.getMessage().setStatus(SendStatusEnum.FAIL);
            iMessageLogRecordRepository.updateMessageLogRecordContent(msgDto.getMdc(), FastjsonUtil.toJson(msgDto));
        }
        return sendStatus;
    }

    public boolean sendDingtalkMessage(MsgDto msgDto) {
        boolean sendStatus = true;
        try {
            sendStatus = dingMessageNotifyApi.sendDingMessage(msgDto);
        } catch (Exception e){
            log.error("sendDingtalkMessage error,{}", e.getMessage(), e);
            sendStatus = false;
            //发送失败更新发送状态
            msgDto.getDingTalkMsg().setStatus(SendStatusEnum.FAIL);
            iMessageLogRecordRepository.updateMessageLogRecordContent(msgDto.getMdc(), FastjsonUtil.toJson(msgDto));

        }
        return sendStatus;
    }
}
