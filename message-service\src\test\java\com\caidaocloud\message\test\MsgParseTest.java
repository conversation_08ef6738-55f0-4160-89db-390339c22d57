package com.caidaocloud.message.test;

import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.msg.service.MsgConfigService;
import com.caidaocloud.message.service.application.template.service.analysis.TemplateAnalysisService;
import com.caidaocloud.message.service.interfaces.vo.msg.MsgConfigDetailVo;
import com.caidaocloud.message.service.interfaces.vo.template.TemplateVo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;
import java.util.List;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class MsgParseTest {

    @Autowired
    private MsgConfigService msgConfigService;

    @Autowired
    private TemplateAnalysisService templateAnalysisService;

    @Test
    public void testParse() {
        try {
            SecurityUserInfo securityUserInfo = new SecurityUserInfo();
            securityUserInfo.setTenantId("33");
            securityUserInfo.setUserId(0L);
            securityUserInfo.setEmpId(0L);
            securityUserInfo.setIsAdmin(false);
            SecurityUserUtil.setSecurityUserInfo(securityUserInfo);

//            MsgConfigDetailVo msgConfig = msgConfigService.getDetail("1559757953063034");
//            List<TemplateVo> templates = msgConfig.getTemplates();
            HashMap<String, String> extMap = Maps.newHashMap();
            extMap.put("empId", "1559816135531729");
            extMap.put("stepId", "11111111");
            extMap.put("businessLineId", "1482606521128964");
//            for (TemplateVo template : templates) {
//                templateAnalysisService.analysisTemplateInfo(template, "1559816135531729", extMap);
//            }
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

}
