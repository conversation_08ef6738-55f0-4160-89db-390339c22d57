package com.caidaocloud.message.service.domain.msg.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import lombok.Data;


/**
 * 消息记录详情
 * <AUTHOR>
 */
@Data
public class MessageLogRecordDetailVo {


    /**
     * 收件人
     */
    private String receiver;

    /**
     * 消息类型
     */
    private String channel;

    /**
     * 消息标题
     */
    private String subject;

    /**
     * 发送状态
     */
    private EnumSimple status;

    /**
     * 发送时间
     */
    private Long sendTime;

}
