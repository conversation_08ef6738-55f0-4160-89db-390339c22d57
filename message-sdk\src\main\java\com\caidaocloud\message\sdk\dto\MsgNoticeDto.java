package com.caidaocloud.message.sdk.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class MsgNoticeDto {
    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 用于追踪消息来源于某个业务或模块
     */
    private String msgFrom = "OnBoarding";

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 批量消息的批次id,默认可以为空，一期可以不用处理
     */
    private String mdc;

    /**
     * 消息配置的 id
     */
    private String msgConfig;

    /**
     * 事件主体人员 empid 集合
     */
    private List<String> subjects;

    // 通知对象类型; 0:员工；1:候选人；2:外部人员; 3: 角色
    private int type = 0;

    /**
     * 发送时间
     */
    private long createTime;

    /**
     * 其他字段信息
     */
    private Map<String, String> ext;
}
