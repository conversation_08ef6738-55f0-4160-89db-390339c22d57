package com.caidaocloud.record.core.annotation;

import java.lang.annotation.*;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface LogRecordAnnotation {

	/**
	 * 成功
	 */
    String success();

	/**
	 * 失败
	 */
	String fail() default "";

	/**
	 * 操作人
	 */
	String operator() default "";

	/**
	 * 操作菜单请求地址
	 */
    String uri() default "";

	/**
	 * 业务单号
	 */
	String bizNo() default "";

	/**
	 * 操作来源，默认手工操作、可能存在接口调用
	 */
	String source() default "手工操作";

	/**
	 * 操作平台
	 */
	String platform() default "";

	/**
	 * 操作菜单
	 */
	String menu() default "";

	/**
	 * 业务分类、操作类型，新增、编辑、删除等类型
	 */
    String category() default "";

	/**
	 * 详情参数字段
	 */
	String detail() default "";

	/**
	 * 操作状态
	 */
	String operatorStatus() default "";

	/**
	 * 条件
	 */
    String condition() default "";

	/**
	 * 租户ID 登入用
	 * @return
	 */
	String tenantId() default "0";
}
