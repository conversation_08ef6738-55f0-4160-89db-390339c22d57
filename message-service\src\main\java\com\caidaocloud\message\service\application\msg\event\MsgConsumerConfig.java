package com.caidaocloud.message.service.application.msg.event;

import com.caidaocloud.mq.rabbitmq.ConsumerGenerate;
import com.caidaocloud.mq.rabbitmq.consumer.DynamicConsumer;
import com.caidaocloud.mq.rabbitmq.factory.ExchangeType;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
public class MsgConsumerConfig {
    @Resource
    private ConsumerGenerate consumerGenerate;
    @Resource
    private EmailLimiterConsumer emailLimiterConsumer;
    @Resource
    private ContractMsgSubscribe contractMsgSubscribe;

    @PostConstruct
    public void init() throws Exception {
        log.info("msg consumer init start......");
        try {
            // 基于延迟插件的延迟队列
            DynamicConsumer dynamicConsumer = consumerGenerate.genConsumer(ExchangeType.DELAY,
                emailLimiterConsumer, "message.limiter.exchange",
                "caidaocloud.message.limiter.delayed.email.queue",
                "routingKey.message.limiter.delayed.email",
                false, true, true);
            dynamicConsumer.start();
        } catch (Exception e) {
            log.error("msg consumer init err,{}", e.getMessage(), e);
        }
        log.info("msg consumer init end......");
    }

    @RabbitHandler
    @RabbitListener(
        bindings = @QueueBinding(
            value = @Queue(value = "caidaocloud.message.tenant.init.fanout.queue", durable = "true"),
            exchange = @Exchange(value = "maintenance.tenant.init.fanout.exchange", type = "fanout"),
            key = {"routingKey.maintenance.tenant.init"}
        )
    )
    public void tenantActivation(String message) {
        // 租户开通 Tenant activation
        log.info("Tenant activation message={}", message);
        String tenantId = null;
        try {
            Map map = FastjsonUtil.toObject(message, Map.class);
            tenantId = (String) map.get("tenantId");
        } catch (Exception e){
            log.error("Tenant activation message Parsing failed. err={}", e);
        }

        if(StringUtil.isEmpty(tenantId)){
            return;
        }

        contractMsgSubscribe.initQueue(tenantId);
    }
}
