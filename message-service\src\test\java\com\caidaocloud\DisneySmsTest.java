package com.caidaocloud;

import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.service.application.sms.dto.SmsMessageDto;
import com.caidaocloud.security.token.TokenGenerator;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;

public class DisneySmsTest {

    public static void main(String[] args) {
        System.out.println(NoticeTarget.EVENT_EMP.toString());

        String token = TokenGenerator.getToken("333", "55", 3);
        System.out.println(token);

        testMessage();
    }

    public static void testMessage(){
        SmsMessageDto messageDto = new SmsMessageDto();
        messageDto.setContent("1111");
        SmsMessageDto.MobileDto mobileDto = new SmsMessageDto.MobileDto();
        mobileDto.setMobile("15921206712");
        mobileDto.setCountryCode("86");
        messageDto.setMobile(Lists.newArrayList(mobileDto));
        String json = FastjsonUtil.toJson(messageDto);
        System.out.println(json);
        SmsMessageDto messageDto1 = FastjsonUtil.toObject(json, SmsMessageDto.class);
        System.out.println(messageDto1);
    }

}
