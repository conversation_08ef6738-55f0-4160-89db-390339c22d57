[{"action": "create", "entityDef": {"identifier": "entity.message.LogInfoMessage", "name": "消息记录日志", "owner": "message", "label": "logInfoMessage", "timelineEnabled": false, "standardProperties": [{"property": "type", "name": "类型", "dataType": "String", "widgetType": "text", "rules": [{"type": "required", "value": "true"}]}, {"property": "receiver", "name": "接受者", "dataType": "String", "widgetType": "text", "rules": [{"type": "required", "value": "true"}]}, {"property": "message", "name": "内容", "dataType": "String", "widgetType": "text", "rules": [{"type": "required", "value": "true"}, {"type": "max<PERSON><PERSON><PERSON>", "value": "2000"}]}, {"property": "status", "name": "状态", "dataType": "Enum", "widgetType": "enum", "visible": true, "modifiable": false, "defaultValue": {"value": 0, "type": "Enum"}, "enumDef": [{"value": 0, "display": "失败"}, {"value": 1, "display": "成功"}]}, {"property": "errorMsg", "name": "错误信息", "dataType": "String", "widgetType": "text", "rules": [{"type": "max<PERSON><PERSON><PERSON>", "value": "2000"}]}, {"property": "channel", "name": "渠道", "dataType": "String", "widgetType": "text", "visible": true, "modifiable": false}]}}]