package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.hr.service.vo.EmpPrivateInfoVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.message.service.interfaces.dto.base.UserDetailVo;
import com.caidaocloud.message.service.interfaces.dto.emp.EmpWorkInfoDto;
import com.caidaocloud.web.Result;

import org.springframework.stereotype.Component;

@Component
public class MasterdataFeignClientV2Fallback implements MasterdataFeignClientV2{
    @Override
    public Result<EmpWorkInfoDto> loadEmpHrbp(String empId, Long dataTime) {
        return null;
    }

    @Override
    public Result<EmpWorkInfoDto> loadEmpLeader(String empId, Long dataTime) {
        return null;
    }

    @Override
    public Result<EmpWorkInfoDto> loadEmpOrgLeader(String empId, Long dataTime) {
        return null;
    }

    @Override
    public Result<EmpPrivateInfoVo> loadPrivateInfo(String empId) {
        return null;
    }

    @Override
    public Result<EmpWorkInfoDto> loadEmpInfoByWorkno(String workno, Long datetime) {
        return null;
    }

    @Override
    public Result<EmpWorkInfoVo> loadEmpWorkInfo(String empId, Long datetime) {
        return null;
    }

    @Override
    public Result<UserDetailVo> loadCurrentUserDetail() {
        return Result.fail();
    }
}
