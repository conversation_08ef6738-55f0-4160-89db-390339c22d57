package com.caidaocloud.message.service.application.message.notifier.service;

import com.caidaocloud.hr.service.dto.EmpBasicInfoDto;
import com.caidaocloud.hr.service.dto.EmpPrivateInfoDto;
import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.hr.service.vo.EmpPrivateInfoVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.hr.service.dto.EmpWorkInfoDto;
import com.caidaocloud.message.service.application.message.notifier.input.NotifierDataFactory;
import com.google.common.collect.Maps;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * 通知对象接口
 * 方便通知对象后续的扩展
 */
public interface INotifyTargetService {
    ConcurrentMap<String, INotifyTargetService> notifier = Maps.newConcurrentMap();

    /**
     * 注册实现
     */
    @PostConstruct
    default void register() {
        String type = getNotifyTarget();
        notifier.put(type, this);
    }

    /**
     * 注册标识
     */
    String getNotifyTarget();

    /**
     * 获取通知人
     */
    default void getNotifier(List<NotifierDto> notifier, NotifyObjectDto notifyObj) {}

    /**
     * 批量获取通知人
     */
    default void getNotifierList(List<NotifierDto> notifier, List<NotifyObjectDto> objList) {
    }

    default List<NotifierDto> convertList(List<EmpWorkInfoDto> list){
        List<NotifierDto> collect = list.stream().map(emp -> convert(emp)).collect(Collectors.toList());
        return collect;
    }

    default List<NotifierDto> listConvert(List<EmpPrivateInfoVo> list){
        List<NotifierDto> collect = list.stream().map(emp -> convert(emp)).collect(Collectors.toList());
        return collect;
    }

    default NotifierDto convert(EmpWorkInfoDto empWorkInfo){
        if(null == empWorkInfo){
            return null;
        }

        NotifierDto notifierDto = new NotifierDto();
        notifierDto.setEmpId(empWorkInfo.getEmpId()).setWorkno(empWorkInfo.getWorkno())
                .setCompanyEmail(empWorkInfo.getCompanyEmail());
        return notifierDto;
    }

    default NotifierDto convert(EmpBasicInfoDto empBasicInfo){
        if(null == empBasicInfo){
            return null;
        }

        NotifierDto notifierDto = new NotifierDto();
        notifierDto.setEmpId(empBasicInfo.getEmpId()).setWorkno(empBasicInfo.getWorkno())
                .setEmail(empBasicInfo.getCompanyEmail())
                .setPhone(empBasicInfo.getPhone());
        return notifierDto;
    }

    default NotifierDto convert(EmpPrivateInfoVo empPrivateInfo){
        if(null == empPrivateInfo){
            return null;
        }

        NotifierDto notifierDto = new NotifierDto();
        notifierDto.setEmpId(empPrivateInfo.getEmpId())
                .setEmail(empPrivateInfo.getGuardianEmail())
                .setPhone(empPrivateInfo.getGuardianPhone());
        return notifierDto;
    }

    default NotifierDto convert(EmpPrivateInfoDto empPrivateInfo){
        if(null == empPrivateInfo){
            return null;
        }

        NotifierDto notifierDto = new NotifierDto();
        notifierDto.setEmpId(empPrivateInfo.getEmpId())
                .setEmail(empPrivateInfo.getGuardianEmail())
                .setPhone(empPrivateInfo.getGuardianPhone());
        return notifierDto;
    }

    default List<NotifierDto> convertEmpDataList(List<EmpDataVo> list){
        List<NotifierDto> collect = list.stream().map(emp -> {
            NotifierDto notifier = new NotifierDto();
            EmpWorkInfoVo empWorkInfo = emp.getEmpWorkInfo();
            notifier.setEmpId(empWorkInfo.getEmpId());
            notifier.setCompanyEmail(empWorkInfo.getCompanyEmail());
            notifier.setWorkno(empWorkInfo.getWorkno());
            notifier.setName(empWorkInfo.getName());
            EmpPrivateInfoVo empPrivateInfo = emp.getEmpPrivateInfo();
            notifier.setEmail(empPrivateInfo.getEmail());
            notifier.setPhone(empPrivateInfo.getPhone());
            return notifier;
        }).collect(Collectors.toList());
        return collect;
    }

    default void loadEmpDataList(List<NotifierDto> dataList, NotifyObjectDto obj){
        List<EmpDataVo> empDataList = NotifierDataFactory
                .getNotifierService().getEmpDataByEmpIds(obj.getEmpIds(), obj.getDataTime());
        if(null == empDataList || empDataList.isEmpty()){
            return;
        }

        dataList.addAll(convertEmpDataList(empDataList));
    }

}
