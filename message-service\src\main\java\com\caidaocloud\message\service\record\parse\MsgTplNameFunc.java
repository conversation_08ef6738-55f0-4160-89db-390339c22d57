package com.caidaocloud.message.service.record.parse;

import com.caidaocloud.message.service.application.template.service.TemplateService;
import com.caidaocloud.message.service.domain.template.entity.TemplateDo;
import com.caidaocloud.record.core.service.IParseFunction;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * created by: FoAng
 * create time: 29/7/2024 2:22 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class MsgTplNameFunc implements IParseFunction {

    private TemplateService templateService;

    @Override
    public String functionName() {
        return "tplName";
    }

    @Override
    public boolean executeBefore() {
        return true;
    }

    @Override
    public String apply(String bid) {
        return Optional.ofNullable(bid).map(it -> {
            TemplateDo templateDo = templateService.getById(bid);
            return templateDo != null ? templateDo.getName() : null;
        }).orElse("");
    }
}
