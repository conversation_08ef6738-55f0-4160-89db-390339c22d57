package com.caidaocloud.message.service.application.msg.event;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.sdk.event.constant.MQConstant;
import com.caidaocloud.message.service.application.notice.service.NoticeMessageService;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.infrastructure.repository.po.NoticeTaskOperateDto;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

/**
 * 工作流审批联动更新待办消息
 * created by: FoAng
 * create time: 26/12/2024 5:25 下午
 */
@Slf4j
@Component
@AllArgsConstructor
public class TaskOperateNoticeSubscribe {

    private NoticeMessageService noticeMessageService;

    @RabbitHandler
    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = "caidaocloud.message.notice.task.queue", durable = "true"),
                    exchange = @Exchange(MQConstant.MESSAGE_SERVICE_EXCHANGE),
                    key = {MQConstant.NOTICE_TASK_ROUTING_KEY}
            )
    )
    public void process(String message) {
        log.info("[TaskOperateNoticeSubscribe] received message: {}", message);
        NoticeTaskOperateDto operateDto = FastjsonUtil.toObject(message, NoticeTaskOperateDto.class);
        if (StringUtils.isEmpty(operateDto.getTenantId())) {
            log.warn("[TaskOperateNoticeSubscribe] tenantId is not found, please check business operate");
            return;
        }
        try {
            String tenantId = operateDto.getTenantId();
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(tenantId);
            userInfo.setEmpId(0L);
            userInfo.setUserId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            UserInfo user = new UserInfo();
            user.setTenantId(tenantId);
            user.setStaffId(0L);
            user.doSetUserId(userInfo.getUserId());
            UserContext.setCurrentUser(user);
            noticeMessageService.markOperateNotice(operateDto);
        } catch (Exception e) {
            log.error("[TaskOperateNoticeSubscribe] consumer task operate msg error, {}", e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }
}
