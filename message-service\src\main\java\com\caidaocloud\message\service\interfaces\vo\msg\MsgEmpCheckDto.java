package com.caidaocloud.message.service.interfaces.vo.msg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@ApiModel("校验上传发起签署人员信息")
@Accessors(chain = true)
public class MsgEmpCheckDto {
    @ApiModelProperty("公司邮箱")
    private String companyEmail;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("工号")
    private String workno;
    @ApiModelProperty("员工ID")
    private String empId;
}
