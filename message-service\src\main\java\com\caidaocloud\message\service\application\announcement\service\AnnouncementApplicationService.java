package com.caidaocloud.message.service.application.announcement.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AbstractData;
import com.caidaocloud.message.service.application.announcement.factory.AnnouncementFactory;
import com.caidaocloud.message.service.application.announcement.dto.AnnouncementDto;
import com.caidaocloud.message.service.application.announcement.dto.AnnouncementPageDto;
import com.caidaocloud.message.service.application.feign.UserServiceFeignClent;
import com.caidaocloud.message.service.application.feign.dto.UserDetailInfoVo;
import com.caidaocloud.message.service.domain.announcement.entity.Announcement;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementContent;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementReceive;
import com.caidaocloud.message.service.domain.announcement.enums.AnnouncementStatus;
import com.caidaocloud.message.service.domain.announcement.enums.ReceiveType;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementContentRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementReceiveRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementRepository;
import com.caidaocloud.message.service.interfaces.vo.announcement.AnnouncementPageVo;
import com.caidaocloud.message.service.interfaces.vo.announcement.AnnouncementVo;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Sequences;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

/**
 * 公告应用服务类
 *
 * <AUTHOR> Zhou
 * @date 2025/7/11
 */
@Slf4j
@Service
public class AnnouncementApplicationService {

    @Resource
    private  AnnouncementRepository announcementRepository;
    @Resource
    private  AnnouncementContentRepository announcementContentRepository;
    @Resource
    private  AnnouncementReceiveRepository announcementReceiveRepository;
    @Resource
    private UserServiceFeignClent userServiceFeignClent;

    /**
     * 创建公告
     * @param dto 公告DTO
     * @return 公告ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String createAnnouncement(AnnouncementDto dto) {
        log.info("开始创建公告，名称：{}", dto.getName());
        
        // 1. 转换并保存公告基本信息
        Announcement announcement = AnnouncementFactory.createAnnouncement(dto);
        announcement = announcementRepository.insert(announcement);
        
        String announcementId = announcement.getBid();

        // 2. 保存公告内容
        if (StringUtils.hasText(dto.getContent()) || dto.getAttachment() != null) {
            AnnouncementContent content = AnnouncementFactory.createContent(dto, announcementId);
            announcementContentRepository.insert(content);
        }
        
        // 3. 保存公告接收者信息
        if (!CollectionUtils.isEmpty(dto.getReceiveValue())) {
            List<AnnouncementReceive> receiveList = AnnouncementFactory.createReceiver(dto, announcementId);
            announcementReceiveRepository.batchInsert(receiveList);
        }
        
        log.info("公告创建完成，ID：{}", announcementId);
        return announcementId;
    }
    
    /**
     * 更新公告
     * @param dto 公告DTO
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAnnouncement(AnnouncementDto dto) {
        if (!StringUtils.hasText(dto.getBid())) {
            throw new IllegalArgumentException("公告ID不能为空");
        }
        
        log.info("开始更新公告，ID：{}", dto.getBid());
        
        // 1. 更新公告基本信息
        Announcement announcement = AnnouncementFactory.createAnnouncement(dto);
        announcement.setBid(dto.getBid());
        announcementRepository.updateById(announcement);

        // 2. 删除并重新保存公告内容
        if (StringUtils.hasText(dto.getContent()) || dto.getAttachment() != null) {
            AnnouncementContent content = AnnouncementFactory.createContent(dto, dto.getBid());
            announcementContentRepository.updateById(content);
        }
        
        // 3. 删除并重新保存公告接收者信息
        announcementReceiveRepository.deleteByAnnouncementId(dto.getBid());
        if (!CollectionUtils.isEmpty(dto.getReceiveValue())) {
            List<AnnouncementReceive> receiveList = AnnouncementFactory.createReceiver(dto, dto.getBid());
            announcementReceiveRepository.batchInsert(receiveList);
        }
        
        log.info("公告更新完成，ID：{}", dto.getBid());
        return true;
    }
    
    /**
     * 删除公告
     * @param announcementId 公告ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAnnouncement(String announcementId) {
        if (!StringUtils.hasText(announcementId)) {
            throw new IllegalArgumentException("公告ID不能为空");
        }
        
        log.info("开始删除公告，ID：{}", announcementId);
        
        announcementReceiveRepository.deleteByAnnouncementId(announcementId);
        announcementContentRepository.deleteById(announcementId);
        announcementRepository.deleteById(announcementId);


        log.info("公告删除完成，ID：{}", announcementId);
        return true;
    }
    
    /**
     * 根据ID查询公告详情
     * @param announcementId 公告ID
     * @return 公告DTO
     */
    public AnnouncementVo detail(String announcementId) {
        if (!StringUtils.hasText(announcementId)) {
            throw new IllegalArgumentException("公告ID不能为空");
        }
        
        log.info("查询公告详情，ID：{}", announcementId);
        
        // 1. 查询公告基本信息
        Announcement announcement = announcementRepository.selectById(announcementId);
        if (announcement == null) {
            log.warn("公告不存在，ID：{}", announcementId);
            return null;
        }
        
        // 2. 查询公告内容
        AnnouncementContent content = announcementContentRepository.findByAnnouncementId(announcementId);
        if (content != null) {
            announcement.setContent(content);
        }
        
        // 3. 查询公告接收者信息
        List<AnnouncementReceive> receiveList = announcementReceiveRepository.findByAnnouncementId(announcementId);
        announcement.setReceiveList(receiveList);
        
        // 4. 转换为DTO
        AnnouncementDto dto = AnnouncementFactory.entityToDto(announcement);
        log.info("公告详情查询成功，ID：{}", announcementId);

        return ObjectConverter.convert(dto, AnnouncementVo.class);
    }

    public PageResult<AnnouncementPageVo> page(AnnouncementPageDto announcementPageDto) {
        PageResult<Announcement> page = announcementRepository.selectPage(announcementPageDto);
        List<Long> createUserList = Sequences.sequence(page.getItems()).filter(d -> d.getCreateBy() != null)
                .map(d -> Long.valueOf(d.getCreateBy()))
                .toList();
        List<UserDetailInfoVo> userDetailInfoVoList = createUserList.isEmpty() ? new ArrayList<>() : userServiceFeignClent.getUserByIds(createUserList)
                .getData();
        List<AnnouncementPageVo> list = Sequences.sequence(page.getItems()).map(a -> {
            AnnouncementPageVo vo = ObjectConverter.convert(a, AnnouncementPageVo.class);
            vo.setType(a.getType().getValue());
            vo.setReceiveType(ReceiveType.fromValue(Integer.parseInt(a.getReceiveType().getValue())));
            vo.setStatus(AnnouncementStatus.fromValue(Integer.parseInt(a.getStatus().getValue())));
            if (a.getCreateBy()!=null)
            vo.setCreateBy(Sequences.sequence(userDetailInfoVoList)
                    .find(u -> u.getUserId().equals(Long.valueOf(a.getCreateBy()))).map(UserDetailInfoVo::getUserName)
                    .getOrElse(""));
            return vo;
        }).toList();
        return new PageResult<>(list, page.getPageNo(), page.getPageSize(), page.getTotal());
    }
}
