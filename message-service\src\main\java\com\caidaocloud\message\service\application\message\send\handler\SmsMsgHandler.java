package com.caidaocloud.message.service.application.message.send.handler;

import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.send.dto.MessageContext;
import com.caidaocloud.message.service.application.msg.dto.MsgDto;
import com.caidaocloud.message.service.application.sms.dto.SmsMessageDto;
import com.caidaocloud.message.service.interfaces.vo.template.TemplateVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 根据模版生产短信消息
 */
@Slf4j
@Service
public class SmsMsgHandler implements IMsgHandler {

    @Override
    public String getHandlerType() {
        return "0";
    }

    @Override
    public void doMsgHandler(MessageContext context) {
        /**
         * 根据模版处理短信收件人
         */
        Map<String, NotifierDto> receiverMap = context.getReceiverMap();
        List<String> phones = new ArrayList<>();
        context.getEmpList().forEach(sysEmpId -> {
            NotifierDto notifierDto = receiverMap.get(sysEmpId);
            if(null == notifierDto || StringUtil.isEmpty(notifierDto.getPhone())){
                log.warn("sysEmpId={} is not exist Notifier={}", sysEmpId, FastjsonUtil.toJson(notifierDto));
                return;
            }

            addPhoneList(phones, notifierDto);
        });

        // 处理监护人
        context.getOtherObjList().forEach(otherObj -> {
            if(null == otherObj || StringUtil.isEmpty(otherObj.getPhone())){
                log.warn("otherObj is not exist other Notifier={}", FastjsonUtil.toJson(otherObj));
                return;
            }

            addPhoneList(phones, otherObj);
        });

        // 根据手机号列表和模版生产短信消息
        createSmsMessage(context.getMsgDto(), context.getTemplate(), phones);
    }

    public void createSmsMessage(MsgDto msgDto, TemplateVo template, List<String> phoneList) {
        phoneList = phoneList.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(phoneList)) {
            log.warn("The mobile phone number collection of SMS recipients is empty.");
            return;
        }

        // 手机号去重
        distinctList(phoneList);

        SmsMessageDto message = new SmsMessageDto();
        message.setContent(template.getContent());
        //增加sms短信编码
        message.setMsgCode(template.getCode());
        List<SmsMessageDto.MobileDto> mobiles = new ArrayList<>();

        for (String phone : phoneList) {
            SmsMessageDto.MobileDto mobile = new SmsMessageDto.MobileDto();
            mobile.setMobile(phone);
            mobiles.add(mobile);
        }

        message.setMobile(mobiles);
        log.info("createSmsMessage data body=[{}]", FastjsonUtil.toJson(message));
        msgDto.setMessage(message);
    }
}
