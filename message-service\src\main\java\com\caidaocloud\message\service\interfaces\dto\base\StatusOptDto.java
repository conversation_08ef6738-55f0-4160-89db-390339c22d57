package com.caidaocloud.message.service.interfaces.dto.base;

import com.caidaocloud.excption.PreCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import springfox.documentation.annotations.ApiIgnore;

@Data
@ApiModel("停用启用DTO")
public class StatusOptDto {
    @ApiModelProperty("业务数据ID")
    private String bid;
    @ApiModelProperty("生效日期（单位毫秒）未开启时间轴模块功能不需要此参数")
    private long dataStartTime;
    @ApiModelProperty("状态（0 已启用 1 已停用）")
    private Integer status;

    @ApiIgnore
    public void preCheckTimelineArgument() {
        PreCheck.preCheckArgument(StringUtils.isBlank(this.bid), "ID不允许为空");
        PreCheck.preCheckArgument(null == this.status, "状态不允许为空");
        PreCheck.preCheckArgument(0L == this.getDataStartTime(), "生效日期不允许为空");
    }

    @ApiIgnore
    public void preCheckArgument() {
        PreCheck.preCheckArgument(StringUtils.isBlank(this.bid), "ID不允许为空");
        PreCheck.preCheckArgument(null == this.status, "状态不允许为空");
    }
}
