package com.caidaocloud.message.service.domain.base.enums;

/**
 * 组织模块-数据业务状态
 *
 * <AUTHOR>
 * @Date 2021/12/3
 */
public enum IdTypeEnum {
    ID_CARD(1, "身份证"),
    PASSPORT(2, "护照"),
    HKMTR(3, "港澳台居民居住证"),
    HKM(4, "港澳通行证"),
    TS(5, "台胞证"),
    PRF(6, "外国人永久居留居住证"),
    OTHER(7, "其他");

    private Integer index;
    private String name;

    IdTypeEnum(Integer index, String name) {
        this.index = index;
        this.name = name;
    }

    public static String getName(Integer index) {
        for (IdTypeEnum c : IdTypeEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
