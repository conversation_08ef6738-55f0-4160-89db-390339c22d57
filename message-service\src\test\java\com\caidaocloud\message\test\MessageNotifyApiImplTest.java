package com.caidaocloud.message.test;

import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.message.send.sms.MessageNotifyApiImpl;
import com.caidaocloud.message.service.application.sms.dto.SmsMessageDto;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.HashMap;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class MessageNotifyApiImplTest {
    @Resource
    private MessageNotifyApiImpl messageNotifyApiImpl;

    @Test
    public void testSendMsg(){
        SmsMessageDto messageDto = new SmsMessageDto();
        messageDto.setContent("[罗氏中国] 欢迎加入罗氏！请您登录https://easyhr.roche.com.cn/根据平台操作指引，尽快在线签署入职日当天所需的文件（如劳动合同）。如有疑问请致电021-2892 3111，谢谢！本信息由系统自动发出，请勿回复，回TD退订");
        //messageDto.setMsgCode("SMS_250525055");
        messageDto.setMsgCode("SMS_250510051");
        SmsMessageDto.MobileDto mobileDto = new SmsMessageDto.MobileDto();
        mobileDto.setMobile("13585606045");
        messageDto.setParams(Lists.newArrayList(new HashMap<>()));
        messageDto.setMobile(Lists.newArrayList(mobileDto));
//        messageNotifyApiImpl.sendMessage(messageDto);
    }

    @Before
    public void initUser(){
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("33");
        userInfo.setUserId(0L);
        userInfo.setEmpId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }
}
