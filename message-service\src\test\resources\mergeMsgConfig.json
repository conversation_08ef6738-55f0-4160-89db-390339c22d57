{"templates": [{"bid": "1621360141072412", "name": "短信测试01", "content": "钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱钱#员工任职信息.工号#vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv不啵啵啵啵啵啵啵啵#候选人任职信息.工号#啵啵啵啵啵啵啵啵啵啵不啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵不啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵不啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵啵#员工任职信息.姓名##员工任职信息.岗位##员工任职信息.姓名拼音##候选人任职信息.工号##候选人任职信息.工号##候选人任职信息.工号##候选人任职信息.工号##员工任职信息.工号##候选人任职信息.工号##候选人任职信息.工号##候选人任职信息.工号##员工任职信息.所属组织##候选人任职信息.工号##候选人任职信息.所属组织#", "params": null, "category": {"text": "短信模版", "value": "0"}, "templateType": null, "templateUrl": null, "code": "12", "attachFile": {"names": [], "urls": []}, "msgConfig": "1554720361682945", "variable": [{"model": "entity.hr.EmpWorkInfo", "text": "#员工任职信息.工号#", "prop": "empWorkInfo.workno", "name": "工号", "varType": "0"}, {"model": "entity.onboarding.EmpWorkInfo", "text": "#候选人任职信息.工号#", "prop": "empWorkInfo.workno", "name": "工号", "varType": "0"}, {"model": "entity.hr.EmpWorkInfo", "text": "#员工任职信息.姓名#", "prop": "empWorkInfo.name", "name": "姓名", "varType": "0"}, {"model": "entity.hr.EmpWorkInfo", "text": "#员工任职信息.岗位#", "prop": "empWorkInfo.postTxt", "name": "岗位", "varType": "0"}, {"model": "entity.hr.EmpWorkInfo", "text": "#员工任职信息.姓名拼音#", "prop": "empWorkInfo.ext.namePinyin", "name": "姓名拼音", "varType": "0"}, {"model": "entity.hr.EmpWorkInfo", "text": "#员工任职信息.所属组织#", "prop": "empWorkInfo.organizeTxt", "name": "所属组织", "varType": "0"}, {"model": "entity.onboarding.EmpWorkInfo", "text": "#候选人任职信息.所属组织#", "prop": "empWorkInfo.organizeTxt", "name": "所属组织", "varType": "0"}], "copyObject": null, "copyMail": null, "externalMail": null, "noticeRule": null}], "msgType": "RENEWAL_NOT_SIGN", "name": "合并通知测试", "notifyObject": ["Role$49"], "condition": {"id": "1676460986928_324", "relation": "and", "type": "group", "children": [{"id": "1676461015519_574", "type": "single", "relation": null, "children": [], "condition": {"name": "hr#EmpWorkInfo#jobGrade$startGrade", "symbol": "EQ", "value": "1596560962164737", "componentType": null}}, {"id": "1676513330125_650", "type": "single", "relation": null, "children": [], "condition": {"name": "hr#EmpWorkInfo#jobGrade$startGrade", "symbol": "NE", "value": "1479129003808805", "componentType": "JOB_GRADE"}}]}, "func": "2", "rule": "SEND_TIME", "round": "0", "mergeRule": {"mergeDay": 1, "mergeTime": 1678320000000, "mergePeriod": 30}, "channel": ["0"], "triggerDate": ""}