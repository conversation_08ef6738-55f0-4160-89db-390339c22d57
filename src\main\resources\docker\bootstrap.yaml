#nacos:
#  config:
#    type: yaml
#    server-addr: ***************:8848
#    data-id: caidaocloud-message-service-config
#    auto-refresh: true
#    group: CORE_HR_GROUP
#    namespace: cd2
#    bootstrap:
#      enable: true
#      log:
#        enable: true
#spring:
#  mail:
#    host: smtp.mxhichina.com
#    password: '!@#123qwe'
#    username: <EMAIL>
#    default-encoding: UTF-8
#    properties:
#      mail:
#        smtp:
#          auth: true
#          starttls:
#            enable: true
#            required: true
#  rabbitmq:
#    host: ***************
#    port: 5672
#    username: admin
#    password: myrabbitmq
#    virtual-host: /caidaocloud
#    listener:
#      simple:
#        acknowledge-mode: auto
#      prefetch: 1
#  redis:
#    host: ***************
#    port: 6379
#    password: myredis
#    database: 1
#    lettuce:
#      pool:
#        maxActive: 2048
#        maxIdle: 200
#        maxWait: 1500ms
#        minIdle: 20
#    timeout: 6000
#  data:
#    mongodb:
#      uri: *************************************************
#  application:
#    name: caidaocloud-message-service-herman
#  cloud:
#    nacos:
#      discovery:
#        server-addr: ***************:8848
#        namespace: cd2
##    namespace: local