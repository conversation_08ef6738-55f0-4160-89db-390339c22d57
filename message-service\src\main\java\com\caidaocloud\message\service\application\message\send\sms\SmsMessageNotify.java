package com.caidaocloud.message.service.application.message.send.sms;

import com.caidaocloud.message.service.application.sms.dto.SmsMessageDto;
import com.google.common.collect.Maps;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentMap;

/**
 * 消息通知接口
 *
 * <AUTHOR>
 * @date 2022/6/13
 **/
public interface SmsMessageNotify<T> {

    ConcurrentMap<String, SmsMessageNotify<?>> messageManager = Maps.newConcurrentMap();

    /**
     * 注册实现
     */
    @PostConstruct
    default void register() {
        String type = getSignType();
        messageManager.put(type, this);
    }

    /**
     * 发送单条短信
     */
    void sendMessage(SmsMessageDto messageDto);

    /**
     * 发送单条短信(无token)
     *
     * @param messageDto
     */
    default void sendSingleMessage(SmsMessageDto messageDto) {
    }

    /**
     * 注册标识
     *
     * @return
     */
    String getSignType();

    /**
     * 发送短信之前操作，如鉴权获取token等
     *
     * @return
     */
    default T beforeSendSms() {
        return null;
    }

}
