package com.caidaocloud.message.service.application.email.service;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.util.Optional;
import java.util.Properties;

@Slf4j
public class MailSenderPooledObjectFactory extends BasePooledObjectFactory<JavaMailSender> implements EnvironmentAware {
    private GenericObjectPool<JavaMailSender> pool = null;
    private int maxTotal = 10;
    private int maxIdle = 5;
    private int minIdle = 1;

    @Setter
    private String host;
    @Setter
    private String port;
    @Setter
    private String socketFactoryClass = "javax.net.ssl.SSLSocketFactory";
    @Setter
    private String user;
    @Setter
    private String password;
    @Setter
    private boolean openSSl = true;

    @Override
    public void setEnvironment(Environment environment) {
    }

    @Override
    public JavaMailSender create() throws Exception {
        JavaMailSenderImpl jms = new JavaMailSenderImpl();
        jms.setHost(host);
        if (port != null) {
            jms.setPort(Integer.valueOf(port));
        }
        jms.setUsername(user);
        jms.setPassword(password);
        jms.setProtocol("smtp");
        jms.setDefaultEncoding("UTF-8");
        Properties props = new Properties();
        props.setProperty("mail.smtp.auth", "true");
        props.setProperty("mail.smtp.port", port);
        props.setProperty("mail.smtp.connectionTimeout","90000");
        props.setProperty("mail.smtp.timeout","30000");
        props.setProperty("mail.smtp.writeTimeout","30000");
        //props.setProperty("mail.transport.protocol", "smtp");
        //props.setProperty("mail.debug", "true");
        if(openSSl){
            props.setProperty("mail.smtp.starttls.enable", "true");
            props.setProperty("mail.smtp.starttls.required", "true");
            // 启用调试
            final String socketPort = getTlsPort(port);
            props.setProperty("mail.smtp.socketFactory.port", getTlsPort(port));
            props.setProperty("mail.smtp.socketFactory.fallback", "false");
            if (socketPort.equals("465")) {
                // smtps协议使用ssl加密通讯方式，区别于587 ttls加密的方式
                props.setProperty("mail.smtps.ssl.enable", "true");
                props.setProperty("mail.smtp.socketFactory.class", socketFactoryClass);
            }
        }
        jms.setJavaMailProperties(props);
        return jms;
    }

    private String getTlsPort(String port) {
        return Optional.ofNullable(port).filter(it -> it.equals("465") || it.equals("587")).orElse("465");
    }

    public synchronized GenericObjectPool<JavaMailSender> getInstance() {
        if (pool == null || pool.isClosed()) {
            GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
            poolConfig.setMaxIdle(maxIdle);
            poolConfig.setMaxTotal(maxTotal);
            poolConfig.setMinIdle(minIdle);
            poolConfig.setLifo(true);
            poolConfig.setTestOnBorrow(true);
            poolConfig.setBlockWhenExhausted(true);
            poolConfig.setTestOnReturn(true);
            poolConfig.setTimeBetweenEvictionRunsMillis(60 * 1000);
            pool = new GenericObjectPool<>(this, poolConfig);
            Runtime.getRuntime().addShutdownHook(new Thread(() -> pool.close()));
        }
        return pool;
    }

    public JavaMailSender borrowObject() throws Exception {
        JavaMailSender trackerServer = this.getInstance().borrowObject();
        return trackerServer;
    }

    public void returnObject(JavaMailSender trackerServer) {
        this.getInstance().returnObject(trackerServer);
    }

    public synchronized void close() throws Exception {
        if (this.getInstance() != null) {
            this.getInstance().close();
        }
    }

    @Override
    public PooledObject<JavaMailSender> wrap(JavaMailSender obj) {
        return new DefaultPooledObject<>(obj);
    }

}
