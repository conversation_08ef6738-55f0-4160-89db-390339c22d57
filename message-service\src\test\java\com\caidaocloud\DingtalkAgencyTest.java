package com.caidaocloud;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.feign.DingTalkAgencyFeignClient;
import com.caidaocloud.message.service.application.feign.DingTalkFeignClient;
import com.caidaocloud.util.FastjsonUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiV2DepartmentListsubidRequest;
import com.dingtalk.api.request.OapiV2UserListRequest;
import com.dingtalk.api.response.OapiV2DepartmentListsubidResponse;
import com.dingtalk.api.response.OapiV2UserListResponse;
import com.google.common.collect.Lists;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import com.aliyun.tea.*;
import com.aliyun.teautil.models.*;
import com.aliyun.dingtalktodo_1_0.models.*;
import com.aliyun.teaopenapi.models.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 钉钉代办任务消息
 * @Date 2023/2/15 下午4:31
 * @Version 1.0
 **/
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class DingtalkAgencyTest {

    @NacosValue("${caidaocloud.msg.dingtalk.agentId:}")
    private String agentId;
    @NacosValue("${caidaocloud.msg.dingtalk.appKey:}")
    private String appKey;
    @NacosValue("${caidaocloud.msg.dingtalk.appSecret:}")
    private String appSecret;


    @Resource
    private DingTalkFeignClient dingTalkFeignClient;

    @Resource
    private DingTalkAgencyFeignClient dingTalkAgencyFeignClient;

    private String getUnionId(String mobile, String accessToken) {
        Map map = dingTalkFeignClient.getUserId(mobile, accessToken);
        System.out.println(FastjsonUtil.toJson(map));
        String  userId = null;
        if (map != null && "ok".equals(map.get("errmsg"))) {
            Map result = (Map) map.get("result");
            userId = (String) result.get("userid");
        }
        String unionId = null;
        if (StringUtils.isNotEmpty(userId)) {
            Map unionIdMap = dingTalkFeignClient.getUnionId(userId, accessToken);
            if (unionIdMap != null && "ok".equals(unionIdMap.get("errmsg"))) {
                Map result = (Map) unionIdMap.get("result");
                unionId = (String) result.get("unionid");
            }
        }
        System.out.println("unionid====" + unionId);
        return unionId;
    }

    /**
     * 创建代办
     * @throws Exception
     */
    @Test
    public void testSendMesage() throws Exception {
        com.aliyun.dingtalktodo_1_0.Client client = createClient();
        CreateTodoTaskHeaders createTodoTaskHeaders = new CreateTodoTaskHeaders();
        Map<String, String> ssoToken = dingTalkFeignClient.getDingTalkToken(appKey, appSecret);
        String accessToken = ssoToken.get("access_token");
        createTodoTaskHeaders.xAcsDingtalkAccessToken = accessToken;
        //发起人
        String unionId = getUnionId("18332559937", accessToken);
        //参与者（抄送人）
//        String participantId = getUnionId("15515756802", accessToken);
        List<String> mobiles = Lists.newArrayList("18722586570","17601065205","18332559937");
        //执行者的unionId列表
        List<String> executorIds = Lists.newArrayList();
        mobiles.forEach(mobile  -> {
            String uId = getUnionId(mobile, accessToken);
            executorIds.add(uId);
        });
        CreateTodoTaskRequest.CreateTodoTaskRequestNotifyConfigs notifyConfigs = new CreateTodoTaskRequest.CreateTodoTaskRequestNotifyConfigs()
                .setDingNotify("1");
        CreateTodoTaskRequest.CreateTodoTaskRequestContentFieldList contentFieldList0 = new CreateTodoTaskRequest.CreateTodoTaskRequestContentFieldList();
        CreateTodoTaskRequest.CreateTodoTaskRequestDetailUrl detailUrl = new CreateTodoTaskRequest.CreateTodoTaskRequestDetailUrl()
//                .setAppUrl("https://www.dingtalk.com")
                .setAppUrl("https://hcm-test.ciwork.cn/h5/authentication")
                .setPcUrl("https://hcm-test.ciwork.cn/h5/authentication");
        CreateTodoTaskRequest createTodoTaskRequest = new CreateTodoTaskRequest()
                .setOperatorId(unionId)
//                .setSourceId(String.valueOf(System.currentTimeMillis()))
                .setSubject("abc接入钉钉待办test11111" + String.valueOf(System.currentTimeMillis()))
                .setCreatorId(unionId)
                .setDescription("应用可以调用该接口发起一个钉钉待办任务，该待办事项会出现在钉钉客户端“待办”页面，需要注意的是，通过开放接口发起的待办，目前仅支持直接跳转ISV应用详情页（ISV在调该接口时需传入自身应用详情页链接）。")
                .setDueTime(1677211261000L)
                .setExecutorIds(executorIds)
//                .setParticipantIds(java.util.Arrays.asList(
//                        participantId
//                ))
                .setDetailUrl(detailUrl)
                .setContentFieldList(java.util.Arrays.asList(
                        contentFieldList0
                ))
//                .setIsOnlyShowExecutor(true)
//                .setPriority(20)
                .setNotifyConfigs(notifyConfigs);
        CreateTodoTaskResponse todoTaskWithOptions = null;
        try {
            todoTaskWithOptions = client.createTodoTaskWithOptions(unionId, createTodoTaskRequest, createTodoTaskHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        }
        System.out.println("todoTaskWithOptions=====***************-==========" + JSONObject.toJSONString(todoTaskWithOptions));
    }

    /**
     * 更新代办任务
     * @throws Exception
     */
    @Test
    public void updateAgency() throws Exception {
        com.aliyun.dingtalktodo_1_0.Client client = createClient();
        UpdateTodoTaskHeaders updateTodoTaskHeaders = new UpdateTodoTaskHeaders();
        Map<String, String> ssoToken = dingTalkFeignClient.getDingTalkToken(appKey, appSecret);
        String accessToken = ssoToken.get("access_token");
        //代办发起人
        String unionId = getUnionId("13128806083", accessToken);
        List<String> mobiles = Lists.newArrayList("18722586570","17601065205","18332559937");
        List<String> executorIds = Lists.newArrayList();
        mobiles.forEach(mobile  -> {
            String uId = getUnionId(mobile, accessToken);
            executorIds.add(uId);
        });
        updateTodoTaskHeaders.xAcsDingtalkAccessToken = accessToken;
        UpdateTodoTaskRequest updateTodoTaskRequest = new UpdateTodoTaskRequest()
                .setOperatorId(unionId)
//                .setSubject("更新钉钉待办11111")
//                .setDescription("应用可以调用该接口更新钉钉待办任务信息及状态，isv在自身应用待办数据被更新后，需调用该接口将钉钉侧待办数据同步更新。")
//                .setDueTime(1617675000000L)
                .setDone(true)
                .setExecutorIds(executorIds)
                /*.setParticipantIds(java.util.Arrays.asList(
                        "PUoiinWIpxxx"
                ))*/;
        UpdateTodoTaskResponse response = null;
        try {
            response = client.updateTodoTaskWithOptions(unionId, "taskd6bea319690d72dd6008dcce31c2628f", updateTodoTaskRequest, updateTodoTaskHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        }
        System.out.println("UpdateTodoTaskResponse response ===========" + JSONObject.toJSONString(response));
    }


    /**
     * 删除代办
     */
    @Test
    public void deleteAgency() throws Exception {
        com.aliyun.dingtalktodo_1_0.Client client = createClient();
        DeleteTodoTaskHeaders deleteTodoTaskHeaders = new DeleteTodoTaskHeaders();
        Map<String, String> ssoToken = dingTalkFeignClient.getDingTalkToken(appKey, appSecret);

        String accessToken = ssoToken.get("access_token");
        deleteTodoTaskHeaders.xAcsDingtalkAccessToken = accessToken;

        //删除代办发起人
        String unionId = getUnionId("18722586570", accessToken);
        //创建任务返回的ID
        String taskId = "task066bfb0ec4a7d370566262e1ac99ba14";

        DeleteTodoTaskRequest deleteTodoTaskRequest = new DeleteTodoTaskRequest()
                .setOperatorId(unionId);
        DeleteTodoTaskResponse deleteTodoTaskResponse = null;
        try {
            deleteTodoTaskResponse = client.deleteTodoTaskWithOptions(unionId, taskId, deleteTodoTaskRequest, deleteTodoTaskHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        }
        System.out.println("DeleteTodoTaskResponse deleteTodoTaskResponse ===========" + JSONObject.toJSONString(deleteTodoTaskResponse));
    }


    /**
     * 查询代办任务列表
     * @throws Exception
     */
    @Test
    public void selectAgencyList() throws Exception {
        com.aliyun.dingtalktodo_1_0.Client client = createClient();
        QueryOrgTodoTasksHeaders queryOrgTodoTasksHeaders = new QueryOrgTodoTasksHeaders();

//        Map<String, String> ssoToken = dingTalkFeignClient.getDingTalkToken(appKey, appSecret);
        Map<String, String> ssoToken = dingTalkFeignClient.getDingTalkToken("dingkrva6lpflk9i4vq4", "ZHEmdlGiC_z0UruJfcfPBmCJ08Jrczv6FRQEpS-5G6-pu4pZswudNL_K2_BzCuL-");

        String accessToken = ssoToken.get("access_token");
        //代办发起人
        String unionId = getUnionId("18612336220", accessToken);

        queryOrgTodoTasksHeaders.xAcsDingtalkAccessToken = accessToken;
        QueryOrgTodoTasksRequest queryOrgTodoTasksRequest = new QueryOrgTodoTasksRequest()
//                .setNextToken("0")
                .setIsDone(true);
        List<QueryOrgTodoTasksResponseBody.QueryOrgTodoTasksResponseBodyTodoCards> todoCards = Lists.newArrayList();
        QueryOrgTodoTasksResponse response = null;
        try {
            response = client.queryOrgTodoTasksWithOptions(unionId, queryOrgTodoTasksRequest, queryOrgTodoTasksHeaders, new RuntimeOptions());
            todoCards.addAll(response.getBody().todoCards);

            while (true) {
                String nextToken = response.getBody().nextToken;
                System.out.println("------" + nextToken + "------" + todoCards.size());
                if (StringUtils.isEmpty(nextToken)) {
                    break;
                }
                queryOrgTodoTasksRequest = new QueryOrgTodoTasksRequest().setNextToken(nextToken).setIsDone(false);
                response = client.queryOrgTodoTasksWithOptions(unionId, queryOrgTodoTasksRequest, queryOrgTodoTasksHeaders, new RuntimeOptions());
                todoCards.addAll(response.getBody().todoCards);
            }
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        }

        System.out.println("QueryOrgTodoTasksResponse response ===========" + JSONObject.toJSONString(response));
        System.out.println("QueryOrgTodoTasksResponse todoCards ===========" + JSONObject.toJSONString(todoCards));
    }



    /**
     * 使用 Token 初始化账号Client
     * @return Client
     * @throws Exception
     */
    public com.aliyun.dingtalktodo_1_0.Client createClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalktodo_1_0.Client(config);
    }


    /**
     * 查部门下的子部门
     */
    @Test
    public void departmentListSubId() throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsubid");
        OapiV2DepartmentListsubidRequest req = new OapiV2DepartmentListsubidRequest();
        req.setDeptId(1L);
        Map<String, String> ssoToken = dingTalkFeignClient.getDingTalkToken(appKey, appSecret);

        String accessToken = ssoToken.get("access_token");
        OapiV2DepartmentListsubidResponse rsp = client.execute(req, accessToken);
        System.out.println(rsp.getBody());
    }

    /**
     * 查部门下的所有人
     * @throws ApiException
     */
    @Test
    public void departmentUserInfo() throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
        OapiV2UserListRequest req = new OapiV2UserListRequest();
        req.setDeptId(1L);
        req.setCursor(0L);
        req.setSize(10L);
        req.setOrderField("modify_desc");
        req.setContainAccessLimit(false);
        req.setLanguage("zh_CN");
        Map<String, String> ssoToken = dingTalkFeignClient.getDingTalkToken(appKey, appSecret);

        String accessToken = ssoToken.get("access_token");

        OapiV2UserListResponse response = null;
        try {
            response = client.execute(req, accessToken);
            if (response != null && response.isSuccess()) {

                System.out.println("****------****" + FastjsonUtil.toJson(response.isSuccess()));
                System.out.println("------****" + FastjsonUtil.toJson(response));
            }
        } catch (com.taobao.api.ApiException e) {
            log.error(e.getErrMsg(), e);
        }
    }


}

 
    
    
    
    