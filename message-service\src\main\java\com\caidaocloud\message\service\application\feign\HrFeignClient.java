package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.hr.service.dto.EmpDataQueryDto;
import com.caidaocloud.hr.service.dto.EmpFileAttachmentDto;
import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.hr.service.vo.organization.company.CompanyVo;
import com.caidaocloud.hr.service.vo.organization.company.org.OrgVo;
import com.caidaocloud.hr.service.vo.organization.post.PostVo;
import com.caidaocloud.message.service.interfaces.vo.msg.MsgImportDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(
    value = "caidaocloud-hr-service",
    fallback = HrFeignClientFallback.class,
    configuration = FeignConfiguration.class,
    contextId = "HrFeignClient"
)
public interface HrFeignClient {
    @PostMapping("/api/hr/empData/output/v1/getEmpInfos")
    Result<List<EmpDataVo>> getEmpInfos(@RequestBody EmpDataQueryDto empDataQuery);

    @GetMapping("/api/hr/empData/output/v1/getEmpWorkInfo")
    Result<EmpWorkInfoVo> getEmpWorkInfo(@RequestParam("empId") String empId,
                                         @RequestParam("dataTime") Long dataTime);

    @PostMapping("/api/hr/emp/work/v1/getEmpListByEmpIds")
    Result<List<EmpWorkInfoVo>> getEmpListByEmpIds(@RequestBody List<String> empIds);

    @GetMapping("/api/hr/company/v1/detail")
    Result<CompanyVo> getCompanyById(@RequestParam("bid") String bid);

    @GetMapping("/api/hr/org/v1/detail")
    Result<OrgVo> getOrgById(@RequestParam("bid") String bid, @RequestParam("dataTime") Long dataTime);

    @GetMapping("/api/hr/v1/workflow/approver/batch")
    Result<String> getApprovers(@RequestParam("empId") String empId, @RequestParam("settingBids") String settingBids,
            @RequestParam(value = "isFormOnboarging", defaultValue = "false") boolean isFormOnboarging);

    @GetMapping("/api/hr/v1/workflow/approver/setting/list")
    Result<List<Map>> getApproverSettings();

    @GetMapping("/api/hr/emp/file/v1/detail")
    Result<EmpFileAttachmentDto> getEmpFileAttachment(@RequestParam("empId") String empId);


    @PostMapping("/api/hr/emp/work/v1/getEmpListByWorkno")
    Result<List<MsgImportDto>> getEmpListByWorkno(@RequestBody List<String> worknoList);

    @GetMapping("/api/hr/transfer/v1/last")
    Result<Map> getEmpTransferRecord(@RequestParam("empId") String empId);

    @GetMapping("/api/hr/postmanage/v1/detail")
    Result<PostVo> getPost(@RequestParam("bid") String bid, @RequestParam("dataTime") Long dataTime);
}
