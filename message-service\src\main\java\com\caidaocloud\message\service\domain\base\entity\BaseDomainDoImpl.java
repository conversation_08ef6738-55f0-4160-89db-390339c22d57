package com.caidaocloud.message.service.domain.base.entity;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;

import java.util.List;

public class BaseDomainDoImpl<T extends DataSimple> extends DataEntity implements BaseDomainDo<T>{
    @Override
    public BaseRepository<T> getRepository() {
        return null;
    }

    @Override
    public T queryDetailByTenantId() {
        if(null == getRepository()){
            return null;
        }

        return getRepository().selectOneByTenantId(getDoIdentifier(), UserContext.getTenantId());
    }

    @Override
    public T getByBid(String bid) {
        return getRepository().selectById(bid, this.getDoIdentifier());
    }

    @Override
    public PageResult<T> getPage(BasePage query) {
        DataSimple dataSimple = build();
        dataSimple.setIdentifier(getDoIdentifier());
        dataSimple.setTenantId(UserContext.getTenantId());
        return getRepository().selectPage(query, (T) dataSimple);
    }

    @Override
    public void deleteByBid(String bid) {
        DataSimple dataSimple = new DataSimple();
        dataSimple.setBid(bid);
        dataSimple.setIdentifier(getDoIdentifier());
        getRepository().delete((T) dataSimple);
    }

    protected DataSimple build(){
        return new DataSimple();
    }

    @Override
    public T getDetail(T t) {
        return getRepository().selectOne(t);
    }

    @Override
    public List<T> selectByIds(List<String> ids) {
        return getRepository().selectBatchIds(ids, getDoIdentifier());
    }

    @Override
    public int deleteBatchIds(List<String> ids) {
        return getRepository().deleteBatchIds(getDoIdentifier(), ids);
    }

    @Override
    public List<T> selectList() {
        return getRepository().selectList((T) build());
    }
}
