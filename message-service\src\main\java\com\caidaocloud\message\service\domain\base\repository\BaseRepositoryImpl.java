package com.caidaocloud.message.service.domain.base.repository;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.google.common.collect.Lists;

import java.lang.reflect.ParameterizedType;
import java.util.List;

import org.springframework.util.function.SupplierUtils;

/**
 * 规范定义简单的增删改查
 * <AUTHOR>
 * @date 2021-11-24
 */
public class BaseRepositoryImpl<T extends DataSimple> implements BaseRepository<T> {
    @Override
    public T insert(T data) {
        String dataId = DataInsert.identifier(data.getIdentifier()).insert(data);
        data.setBid(dataId);
        return data;
    }

    @Override
    public int updateById(T data) {
        DataUpdate.identifier(data.getIdentifier()).update(data);
        return 0;
    }

    @Override
    public int delete(T data) {
        DataDelete.identifier(data.getIdentifier()).delete(data.getBid());
        return 0;
    }

    @Override
    public int delete(T data, Long dateTime) {
        DataDelete.identifier(data.getIdentifier()).softDelete(data.getBid(), dateTime);
        return 0;
    }

    @Override
    public int deleteBatchIds(String identifier, List<String> ids) {
        DataFilter filter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andIn("bid", ids);
        DataDelete.identifier(identifier).batchDelete(filter);
        return 0;
    }

    @Override
    public T selectById(String bid, String identifier, Long dateTime) {
        return DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .oneOrNull(bid, getDataClazz(), dateTime);
    }

    @Override
    public T selectById(String bid, String identifier) {
        return DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .oneOrNull(bid, getDataClazz());
    }

    @Override
    public T selectOneByTenantId(String identifier, String tenantId) {
        final List<T> items = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible().
                filter(DataFilter.eq("tenantId", tenantId)
                        .andNe("deleted", Boolean.TRUE.toString()), getDataClazz()).getItems();

        return null != items && items.isEmpty() ? items.get(0) : null;
    }

    @Override
    public PageResult<T> selectPage(BasePage page, T data) {
        return DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage().queryInvisible()
                .limit(page.getPageSize(), page.getPageNo()).filter(DataFilter.eq("tenantId", data.getTenantId())
                        .andEq("deleted", Boolean.FALSE.toString()), getDataClazz());
    }

    @Override
    public List<T> selectList(T data) {
        PageResult<T> pageResult = DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage()
                .queryInvisible().limit(5000, 1).filter(DataFilter.eq("tenantId", UserContext.getTenantId())
                        .andEq("deleted", Boolean.FALSE.toString()), getDataClazz());

        return null != pageResult ? pageResult.getItems() : Lists.newArrayList();
    }

    @Override
    public List<T> selectList(T data, Long dateTime) {
        PageResult<T> pageResult = DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage()
                .queryInvisible().filter(DataFilter.eq("tenantId", data.getTenantId())
                        .andNe("deleted", Boolean.TRUE.toString()), getDataClazz(), dateTime);

        return null != pageResult ? pageResult.getItems() : Lists.newArrayList();
    }

    protected Class<T> getDataClazz() {
        ParameterizedType parameterizedType = (ParameterizedType) getClass().getGenericSuperclass();
        return (Class<T>) parameterizedType.getActualTypeArguments()[0];
    }

    protected DataFilter getBaseFilter(T data){
        DataFilter filter = DataFilter.eq("tenantId", data.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString());
        return filter;
    }

    protected DataFilter getBaseFilter(){
        DataFilter filter = DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId())
                .andNe("deleted", Boolean.TRUE.toString());
        return filter;
    }

    @Override
    public T selectOne(T data) {
        return null;
    }

    @Override
    public List<T> selectBatchIds(List<String> ids, String identifier) {
        DataFilter filter = getBaseFilter();
        filter = filter.andIn("bid", ids);
        PageResult<T> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage()
                .queryInvisible().filter(filter, getDataClazz());

        return null != pageResult ? pageResult.getItems() : Lists.newArrayList();
    }

    protected List<T> getPageList(PageResult<T> pageResult) {
        return null != pageResult ?
                (null == pageResult.getItems() || pageResult.getItems().isEmpty() ? Lists.newArrayList() : pageResult.getItems())
                : Lists.newArrayList();
    }
}
