package com.caidaocloud.message.service.application.template.service.analysis.datasource;

import com.googlecode.totallylazy.Maps;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import java.util.Map;

public interface IEmpDataReader {
    Map<String, IEmpDataReader> readers = Maps.map();

    String getReaderType();

    static void reader(Map<String, Object> map, String identifier, String empId, Map<String, String> ext, int type) {
        IEmpDataReader reader = readers.get(identifier);
        if (reader == null) {
            return;
        }
        reader.getData(map, identifier, empId, ext, type);
    }

    void getData(Map<String, Object> map, String identifier, String empId, Map<String, String> ext, int type);

    @PostConstruct
    default void register(){
        readers.put(getReaderType(), this);
    }

}
