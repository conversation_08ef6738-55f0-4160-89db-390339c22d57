package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.message.service.domain.email.dto.EmailTemplateBatchDto;
import com.caidaocloud.message.service.domain.email.dto.EmailTemplateDto;
import com.caidaocloud.message.service.domain.email.dto.SameEmailTemplateDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

@Component
public class EmailAdapterFeignClientFallback implements EmailAdapterFeignClient {
    @Override
    public Result emailSingleSend(EmailTemplateDto emailTemplateDto) {
        return Result.fail("单条邮件发送失败");
    }

    @Override
    public Result emailBatchSend(EmailTemplateBatchDto emailTemplateBatchDto) {
        return Result.fail("批量邮件发送失败");
    }

    @Override
    public Result sameEmailBatchSend(SameEmailTemplateDto sameEmailTemplateDto) {
        return Result.fail("批量邮件(相同模板)发送失败");
    }

}
