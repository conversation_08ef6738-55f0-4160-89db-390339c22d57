package com.caidaocloud.message.service.application.template.service.analysis;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.dto.EmpFileAttachmentDto;
import com.caidaocloud.hr.service.vo.EmpPrivateInfoVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.hr.service.vo.organization.company.CompanyVo;
import com.caidaocloud.hr.service.vo.organization.company.org.CustomOrgRoleVo;
import com.caidaocloud.hr.service.vo.organization.company.org.OrgVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.message.sdk.dto.EmpWelcomeFileAttachmentVo;
import com.caidaocloud.message.sdk.dto.PreEmpEntryProcessVo;
import com.caidaocloud.message.sdk.dto.PreEmpPrivateInfoVo;
import com.caidaocloud.message.sdk.dto.PreEmpWorkInfoVo;
import com.caidaocloud.message.service.application.feign.HrFeignClient;
import com.caidaocloud.message.service.application.feign.OnBoardingFeignClient;
import com.caidaocloud.message.service.application.feign.dto.WorkflowApproverSettingDetail;
import com.caidaocloud.message.service.application.template.service.analysis.datasource.IEmpDataReader;
import com.caidaocloud.message.service.domain.base.repository.INotifyEmpRepository;
import com.caidaocloud.message.service.infrastructure.utils.DateUtil;
import com.caidaocloud.message.service.infrastructure.utils.RestUtil;
import com.caidaocloud.message.service.interfaces.dto.template.TemplateVariableDto;
import com.caidaocloud.message.service.interfaces.vo.template.TemplateVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Maps;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TemplateAnalysisService {
    @Resource
    private INotifyEmpRepository masterdataEmpRepository;
    @Resource
    private HrFeignClient hrFeignClient;
    @Resource
    private OnBoardingFeignClient onBoardingFeignClient;
    @Value("${env.domain.url:}")
    private String envDomainUrl;

    @Autowired
    private DiscoveryClient discoveryClient;

    public void analysisTemplateInfo(TemplateVo templateDo, String eventEmpId, Map<String, String> ext, int type) {
        if (ext != null && !ext.isEmpty()) {
            templateDo.setContent(formatTemplateWithPlaceholder(StringUtils.defaultString(templateDo.getContent(), ""), ext));
        }
        if (templateDo.getCategory() != null
                && "0".equals(templateDo.getCategory().getValue())
                && StringUtils.isNotBlank(templateDo.getContent())) {
            templateDo.setContent(templateDo.getContent().replaceAll("&nbsp;", " "));
        }
        // 获取模板参数配置信息
        List<TemplateVariableDto> params = templateDo.getVariable();
        if (null == params || params.isEmpty()) {
            return;
        }

        // 获取配置信息数据
        Map<String, Object> map = new HashMap<>();
        log.info("templateVariable:"+FastjsonUtil.toJson(params));
        for (TemplateVariableDto dto : params) {
            loadInfoFromMasterdata(map, dto.getModel(), eventEmpId, ext, type);
        }
        // 组装配置信息数据字典
        Map<String, String> valueMap = new HashMap<>();
        // 短信填充模板参数
        Map<String, String> paramsMap = Maps.newHashMap();
        for (TemplateVariableDto dto : params) {
            String prop = dto.getProp();
            String model = dto.getModel();
            Object obj = map.get(model);
            String propMark = dto.getText().replaceAll("#", "");
            if (null == valueMap.get(propMark)) {
                String value = getValueFromObj(prop, obj, ext, dto.getVarType());
                String newValue = doFormat(value, dto.getVarType(), templateDo);
                log.info("Template Variable Parse After. model={},prop={},value={},newValue={}", model, prop, value, newValue);
                valueMap.put(propMark, newValue);
                paramsMap.put(prop, newValue);
            }
        }
        final String filterContent = filterLabelContent(valueMap.keySet(), templateDo.getContent());
        // 根据配置字段解析匹配模板中的字段并替换
        templateDo.setContent(formatTemplate(StringUtils.defaultString(filterContent, ""), valueMap));
        templateDo.setTitle(formatTemplate(StringUtils.defaultString(templateDo.getTitle(), ""), valueMap));
        log.info("analysis Template Info params:{}", FastjsonUtil.toJson(paramsMap));
        templateDo.setParams(paramsMap);
    }

    private String doFormat(String value, String varType, TemplateVo templateVo) {
        if (StringUtil.isEmpty(varType) || StringUtil.isEmpty(value) || "0".equals(varType)) {
            return value;
        }

        if ("1".equals(varType)) {
            return DateUtil.getFormatDate(Long.parseLong(value), DateUtil.DEFAULT_DATE_PATTERN);
        } else if ("2".equals(varType)) {
            return DateUtil.getFormatDate(Long.parseLong(value), DateUtil.YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
        } else if ("3".equals(varType)) {
            return Boolean.valueOf(value) ? "是" : "否";
        } else if ("4".equals(varType) && null != templateVo) {
            Attachment attachment = templateVo.getAttachFile();
            if (null == attachment) {
                attachment = new Attachment();
            }
            if (null == attachment.getUrls()) {
                attachment.setUrls(Lists.list());
                attachment.setNames(Lists.list());
            }
            Attachment empFile = FastjsonUtil.toObject(value, Attachment.class);
            if (null != empFile && null != empFile.getUrls() && !empFile.getUrls().isEmpty()) {
                attachment.getNames().addAll(empFile.getNames());
                attachment.getUrls().addAll(empFile.getUrls());
            }
            templateVo.setAttachFile(attachment);
            return "";
        }

        return value;
    }

    private String doFormat(String value, String varType) {
        return doFormat(value, varType, null);
    }

    private String formatTemplate(String str, Map<String, String> valueMap) {
        StringSubstitutor sub = new StringSubstitutor(valueMap);
        sub.setVariablePrefix("#");
        sub.setVariableSuffix("#");
        return sub.replace(str);
    }

    private String filterLabelContent(Set<String> keys, String content) {
        for (String key :
                keys) {
            content = content.replaceAll(String.format("data-labelName=\"#%s#\"", key), "")
                    .replaceAll(String.format("data-labelname=\"#%s#\"", key), "");
        }
        return content;
    }

    /**
     * 占位符替换
     *
     * @param str
     * @param extMap
     * @return
     */
    private String formatTemplateWithPlaceholder(String str, Map<String, String> extMap) {
        var sub = new StringSubstitutor(extMap);
        sub.setVariablePrefix("{");
        sub.setVariableSuffix("}");
        return sub.replace(str);
    }

    private String getValueFromObj(String prop, Object obj, Map<String, String> extMap, String varType) {
        // 替换exMap扩展参数
        if (extMap != null && extMap.containsKey(prop)) {
            return extMap.get(prop);
        }
        String propValue = StringUtil.EMPTY;
        if (null == obj) {
            return propValue;
        }
        String[] propLinks = prop.split("\\.");
        Object temp = obj;
        if (obj instanceof HashMap) {
            Object dataObj = ((Map) temp).get(prop);
            if (dataObj != null) {
                return getDataTypeValue(dataObj, varType);
            } else if(propLinks.length > 0){
                return getLinkData(propLinks, temp, varType);
            }
            return propValue;
        }
        return getLinkData(propLinks, temp, varType);
    }

    private String getLinkData(String [] propLinks, Object temp, String varType){
        String propValue = StringUtil.EMPTY;
        for (int i = 1; i < propLinks.length; ++i) {
            temp = reflectObjectFile(propLinks[i], temp);
        }
        propValue = null == temp ? propValue : getDataTypeValue(temp, varType);
        return propValue;
    }

    private String getDataTypeValue(Object temp, String varType) {
        if (temp instanceof Attachment || "4".equals(varType)) {
            return FastjsonUtil.toJson(temp);
        }

        return temp.toString();
    }

    private Object reflectObjectFile(String prop, Object obj) {
        try {
            if (obj instanceof Map) {
                Map map = (Map) obj;
                return map.get(prop);
            }
            if (null == obj) {
                return obj;
            }
            Class clazz = obj.getClass();
            Field field = clazz.getDeclaredField(prop);
            field.setAccessible(true);
            return field.get(obj);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.error("reflect failed, field={}, from={}, model={}", prop,
                    obj == null ? null : obj.getClass().getName(),
                    FastjsonUtil.toJson(obj));
        }
        return null;
    }

    private void loadInfoFromMasterdata(Map<String, Object> map, String identifier, String empId, Map<String, String> ext, int type) {
        if (StringUtil.isEmpty(identifier) || null != map.get(identifier)) {
            return;
        }

        // 后期根据empid和identifier做一个接口可以直接获取对应模型的相关信息
        if ("entity.hr.EmpWorkInfo".equals(identifier)) {
            getEmpWorkInfo(map, identifier, empId);
            return;
        }

        if ("entity.hr.EmpPrivateInfo".equals(identifier)) {
            EmpPrivateInfoVo obj = masterdataEmpRepository.loadPrivateInfo(empId).getData();
            map.put(identifier, obj);
            return;
        }

        if ("entity.esign.DocTemplate".equals(identifier) || "entity.hr.Contract".equals(identifier)) {
            map.put(identifier, ext);
            return;
        }

      /*  if ("entity.hr.EmpPrivateInfo".equals(identifier)) {
            EmpPrivateInfoVo obj = masterdataFeignClient.getPrivateInfo(empId).getData();
            map.put(identifier, obj);
            return;
        }*/

        if ("entity.onboarding.EmpPrivateInfo".equals(identifier)) {
            // 候选人个人信息
            PreEmpPrivateInfoVo obj = onBoardingFeignClient.getPrivateInfoByEmpId(empId).getData();
            map.put(identifier, obj);
            return;
        }

        if ("entity.onboarding.EmpWorkInfo".equals(identifier)) {
            // 候选人任职信息
            getPreEmpWorkInfo(map, identifier, empId);
            return;
        }

        if ("entity.onboarding.Teamwork".equals(identifier)) {
            // 候选人协同
            map.put(identifier, ext);
            return;
        }

        if ("entity.onboarding.Step".equals(identifier)) {
            if (ext == null) {
                ext = new HashMap<>();
            }
            // 候选人步骤
            ext.put("entry.step.sendDate", System.currentTimeMillis() + "");
            map.put(identifier, ext);
            return;
        }

        if ("entity.onboarding.EmpEntryProcess".equals(identifier)) {
            PreEmpEntryProcessVo obj = onBoardingFeignClient.getEmpProcessInfo(empId).getData();
            map.put(identifier, obj);
            return;
        }

        if ("entity.hr.Company".equals(identifier)) {
            EmpWorkInfoVo empWorkInfo = getEmpWorkInfo(map, "entity.hr.EmpWorkInfo", empId);
            // 公司管理
            if (StringUtil.isNotEmpty(empWorkInfo.getCompany())) {
                CompanyVo obj = hrFeignClient.getCompanyById(empWorkInfo.getCompany()).getData();
                map.put(identifier, obj);
            }
            return;
        }

        if ("entity.onboarding.Company".equals(identifier)) {
            PreEmpWorkInfoVo pewi = getPreEmpWorkInfo(map, "entity.onboarding.EmpWorkInfo", empId);
            // 公司管理
            if (StringUtil.isNotEmpty(pewi.getCompany())) {
                CompanyVo obj = hrFeignClient.getCompanyById(pewi.getCompany()).getData();
                map.put(identifier, obj);
            }
            return;
        }

        /**
         * 招聘顾问手机号, 查询个人信息
         */
        if ("entity.onboarding.recruitment.person".equals(identifier)) {
            PreEmpWorkInfoVo preEmpWorkInfo = getPreEmpWorkInfo(map, "entity.onboarding.EmpWorkInfo", empId);
            if (preEmpWorkInfo != null && preEmpWorkInfo.getRecruitment() != null) {
                EmpPrivateInfoVo obj = masterdataEmpRepository.loadPrivateInfo(preEmpWorkInfo.getRecruitment().getEmpId()).getData();
                map.put(identifier, obj);
                return;
            }
        }

        /**
         * 招聘顾问公司邮箱，查询任职信息
         */
        if ("entity.onboarding.recruitment.work".equals(identifier)) {
            PreEmpWorkInfoVo preEmpWorkInfo = getPreEmpWorkInfo(map, "entity.onboarding.EmpWorkInfo", empId);
            if (preEmpWorkInfo != null && preEmpWorkInfo.getRecruitment() != null) {
                getEmpWorkInfo(map, identifier, preEmpWorkInfo.getRecruitment().getEmpId());
                return;
            }
        }

        if ("entity.onboarding.Org".equals(identifier)) {
            PreEmpWorkInfoVo pewi = getPreEmpWorkInfo(map, "entity.onboarding.EmpWorkInfo", empId);
            // 候选人组织管理
            if (StringUtil.isNotEmpty(pewi.getOrganize())) {
                OrgVo obj = getOnboardingOrg(map, identifier, pewi.getOrganize());
                doOrganizePathTxt(obj, pewi.getOrganizeTxt());
                map.put(identifier, obj);
            }
            return;
        }

        if("entity.hr.Renewal".equals(identifier)){
            val url = envDomainUrl + "/hr/employee/contract/sign/new?id=" + empId + "&type=0";
            map.put(identifier, com.googlecode.totallylazy.Maps.map("renewalAddress", url));
            return;
        }

        if("entity.hr.OrgRole".equals(identifier)){
            val empWorkInfo = getEmpWorkInfo(map, "entity.hr.EmpWorkInfo", empId);
            if(StringUtil.isNotEmpty(empWorkInfo.getOrganize())){
                OrgVo org = hrFeignClient.getOrgById(empWorkInfo.getOrganize(), System.currentTimeMillis()).getData();
                List<CustomOrgRoleVo> roleList = null;
                if(null == org || null == (roleList = org.getCustomOrgRoles())){
                    return;
                }
                DictSimple dictHrbp = DictSimple.code2Dict("HRBP");
                List<EmpSimple> hrbpList = new ArrayList<>();
                roleList.forEach(orgRole -> {
                    if(Objects.equals(orgRole.getRole(), dictHrbp.getValue())){
                        hrbpList.add(orgRole.getLeaderEmp());
                        return;
                    }
                });
                String hrbps = hrbpList.stream().map(empSimple -> empSimple.getName())
                        .filter(StringUtil::isNotEmpty).collect(Collectors.joining("、"));
                Map<String, Object> orgRoleMap = new HashMap<>();
                orgRoleMap.put("work.hrbpNames", hrbps);
                map.put(identifier, orgRoleMap);
            }
            return;
        }

        if(identifier.startsWith("entity.message.CustomizedVariable")){
            log.info("CustomizedVariable:"+identifier);
            val code = identifier.replace("entity.message.CustomizedVariable_", "");
            val variableMap = Maps.newHashMap();
            val variableDefine = DataQuery.identifier("entity.message.CustomizedVariable").limit(1, 1)
                    .filter(DataFilter.eq("code", code), DataSimple.class)
                    .getItems();
            log.info("CustomizedVariableDefine:" + FastjsonUtil.toJson(variableDefine));
            if(!variableDefine.isEmpty()){
                String serviceName = ((SimplePropertyValue) variableDefine.get(0).getProperties().get("serviceName")).getValue();
                String path = ((SimplePropertyValue) variableDefine.get(0).getProperties().get("path")).getValue();
                HttpHeaders headers = RestUtil.token();
                List<ServiceInstance> services = discoveryClient.getInstances(serviceName);
                String url = String.format("%s/%s", services.get(0).getUri().toString(), path);
                ResponseEntity<String> result = RestUtil.getRestTemplate().postForEntity(url + "?empId="+empId,
                        new HttpEntity(null, headers), String.class);
                var success = Lists.list(HttpStatus.OK, HttpStatus.CREATED, HttpStatus.ACCEPTED)
                        .contains(result.getStatusCode());
                if (!success) {
                    throw new ServerException("rest call failed, code : " + result.getStatusCodeValue() + ", msg : " + result.getBody());
                }
                val requestResult = FastjsonUtil.toObject(result.getBody(), new TypeReference<Result>(){});
                log.info("CustomizedVariableResult:" + FastjsonUtil.toJson(requestResult));
                if(!requestResult.isSuccess()){
                    throw new ServerException("request failed, code : " + requestResult.getMsg());
                }
                String variableValue = FastjsonUtil.toObject(result.getBody(), new TypeReference<Result<String>>(){})
                        .getData();
                variableMap.put(code, variableValue);
                map.put(identifier, variableMap);
            }
            return;
        }

        if ("entity.onboarding.OrgRole".equals(identifier)) {
            PreEmpWorkInfoVo pewi = getPreEmpWorkInfo(map, "entity.onboarding.EmpWorkInfo", empId);
            // 候选人组织管理
            if (StringUtil.isEmpty(pewi.getOrganize())) {
                return;
            }

            OrgVo obj = getOnboardingOrg(map, "entity.onboarding.Org", pewi.getOrganize());
            doOrganizePathTxt(obj, pewi.getOrganizeTxt());
            map.put("entity.onboarding.Org", obj);

            List<CustomOrgRoleVo> roleList = null;
            if (null == obj || null == (roleList = obj.getCustomOrgRoles())) {
                return;
            }
            DictSimple dictHrbp = DictSimple.code2Dict("HRBP");
            List<EmpSimple> hrbpList = new ArrayList<>();
            roleList.forEach(orgRole -> {
                if (Objects.equals(orgRole.getRole(), dictHrbp.getValue())) {
                    hrbpList.add(orgRole.getLeaderEmp());
                    return;
                }
            });
            String hrbps = hrbpList.stream().map(empSimple -> empSimple.getName())
                    .filter(StringUtil::isNotEmpty).collect(Collectors.joining("、"));
            Map<String, Object> orgRoleMap = new HashMap<>();
            orgRoleMap.put("work.hrbpNames", hrbps);
            map.put(identifier, orgRoleMap);
            return;
        }

        if ("entity.hr.Org".equals(identifier)) {
            EmpWorkInfoVo emp = getEmpWorkInfo(map, "entity.hr.EmpWorkInfo", empId);
            // 员工组织管理
            if (StringUtil.isNotEmpty(emp.getOrganize())) {
                OrgVo obj = hrFeignClient.getOrgById(emp.getOrganize(), System.currentTimeMillis()).getData();
                doOrganizePathTxt(obj, emp.getOrganizeTxt());
                map.put(identifier, obj);
            }
            return;
        }

        if ("entity.hr.EmpReportLine".equals(identifier)) {
            map.put(identifier, ext);
            return;
        }

        if ("entity.hr.EmpFileAttachment".equals(identifier)) {
            // 员工附件档案
            EmpFileAttachmentDto obj = hrFeignClient.getEmpFileAttachment(empId).getData();
            map.put(identifier, obj);
            return;
        }

        if (identifier.startsWith("$approver_")) {
            loadApproverInfo(map, identifier, empId, type);
            return;
        }

        if ("entity.onboarding.EmpWelcomeFileAttachment".equals(identifier)) {
            EmpWelcomeFileAttachmentVo data = onBoardingFeignClient.getEmpWelcomeFileAttachment(empId).getData();
            map.put(identifier, data);
            return;
        }

        IEmpDataReader.reader(map, identifier, empId, ext, type);
    }

    private void doOrganizePathTxt(OrgVo obj, String organizeTxt) {
        TreeParent pid = null;
        if (null != obj && null != (pid = obj.getPid())) {
            organizeTxt = null == organizeTxt ? "" : organizeTxt;
            organizeTxt = StringUtil.isNotEmpty(pid.getNamePath()) ? pid.getNamePath() + "/" + organizeTxt : organizeTxt;
        }
        obj.getExt().put("organizePathTxt", organizeTxt);
    }

    private PreEmpWorkInfoVo getPreEmpWorkInfo(Map<String, Object> map, String identifier, String empId) {
        PreEmpWorkInfoVo obj = (PreEmpWorkInfoVo) map.get(identifier);
        if (null != obj) {
            return obj;
        }

        obj = onBoardingFeignClient.getWorkInfoByEmpId(empId).getData();
        map.put(identifier, obj);
        return obj;
    }


    private EmpWorkInfoVo getEmpWorkInfo(Map<String, Object> map, String identifier, String empId) {
        EmpWorkInfoVo obj = (EmpWorkInfoVo) map.get(identifier);
        if (null != obj) {
            return obj;
        }

        obj = hrFeignClient.getEmpWorkInfo(empId, System.currentTimeMillis()).getData();
        map.put(identifier, obj);
        return obj;
    }

    private OrgVo getOnboardingOrg(Map<String, Object> map, String identifier, String organize) {
        OrgVo obj = (OrgVo) map.get(identifier);
        if (null != obj) {
            return obj;
        }

        obj = hrFeignClient.getOrgById(organize, System.currentTimeMillis()).getData();
        map.put(identifier, obj);
        return obj;
    }

    private void loadApproverInfo(Map<String, Object> map, String identifier, String empId, int type) {
        Object dataObj = map.get(identifier);
        if (null != dataObj) {
            return;
        }

        List<WorkflowApproverSettingDetail> items = DataQuery.identifier("entity.hr.ApproverSettingDetail")
                .filter(DataFilter.eq("settingBid", StringUtils.substringAfter(identifier, "_")),
                        WorkflowApproverSettingDetail.class).getItems();
        List<EmpSimple> list = getMatchingApprovers(empId, items, type);
        String collect = list.stream().map(empSimple -> empSimple.getName()).collect(Collectors.joining("、"));
        Map<String, Object> valMap = new HashMap<>();
        valMap.put("other.approverTxt", collect);
        map.put(identifier, valMap);
    }

    private List<EmpSimple> getMatchingApprovers(String empId, List<WorkflowApproverSettingDetail> items, int type) {
        String identifier = 1 == type ? "entity.onboarding.EmpWorkInfo" : "entity.hr.EmpWorkInfo";
        val empPage = DataQuery.identifier(identifier)
                .filterProperties(
                        DataFilter.eq("empId", empId),
                        Lists.list("workplace", "empType.dict.value", "company"),
                        System.currentTimeMillis());
        val preEmp = empPage.getItems().stream().findFirst()
                .orElseThrow(() -> new ServerException("员工或候选人不存在"));

        List<EmpSimple> list = new ArrayList<>();
        items.stream().filter(detail -> detail.getMatchingCondition().match(preEmp))
                .forEach(detail -> list.addAll(detail.getApprovers()));
        return list;
    }

    private String getWorkplace(Map<String, Object> map, String empId, int type) {
        String workplace = "";
        if (0 == type) {
            EmpWorkInfoVo emp = getEmpWorkInfo(map, "entity.hr.EmpWorkInfo", empId);
            workplace = null != emp && StringUtil.isNotEmpty(emp.getWorkplace()) ? emp.getWorkplace() : "";
        } else if (1 == type) {
            PreEmpWorkInfoVo preEmpWorkInfo = getPreEmpWorkInfo(map, "entity.onboarding.EmpWorkInfo", empId);
            workplace = null != preEmpWorkInfo && StringUtil.isNotEmpty(preEmpWorkInfo.getWorkplace()) ? preEmpWorkInfo.getWorkplace() : "";
        }

        return workplace;
    }
}
