package com.caidaocloud.message.service.application.email.dto;

import java.io.Serializable;

/**
 * 邮件模版消息
 */
public class EmailTmplateMessage implements Serializable {
    /**
     * 必须赋予belongid
     */
    private String tenantId;
    private String to;
    private String subject;
    private String content;
    /**
     * 多个附件用逗号分割
     */
    private String affix; //附件地址
    /**
     * 指定附件对应的名称，多个用逗号分割同affix一一对应，如果为空或对应下标上的值为空，则取文件名作为名称
     */
    private String affixName; //附件名称
    private String cc;
    // 密送人
    private String bcc;
    private String logEmailId;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTo() {
        return to;
    }

    public void setTo(String to) {
        this.to = to;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getAffix() {
        return affix;
    }

    public void setAffix(String affix) {
        this.affix = affix;
    }

    public String getAffixName() {
        return affixName;
    }

    public void setAffixName(String affixName) {
        this.affixName = affixName;
    }

    public String getCc() {
        return cc;
    }

    public void setCc(String cc) {
        this.cc = cc;
    }

    public String getLogEmailId() {
        return logEmailId;
    }

    public void setLogEmailId(String logEmailId) {
        this.logEmailId = logEmailId;
    }

    @Override
    public String toString() {
        String s = "to:" + this.to + ",subject:" + this.subject + ",cc:" + this.cc + ",affix:" + this.affix + ",affixName:" + this.affixName + ",content:" + this.content;
        return s;
    }

    public String getBcc() {
        return bcc;
    }

    public EmailTmplateMessage setBcc(String bcc) {
        this.bcc = bcc;
        return this;
    }
}
