package com.caidaocloud.message.service.application.email.service;

import com.caidaocloud.util.PropUtil;
import com.caidaocloud.util.StringUtil;

/**
 * EmailNotifyFactory
 *
 * <AUTHOR>
 * @date 2022/6/6 下午3:28
 */
public class EmailNotifyFactory {
    public static String emailType = null;

    public static EmailNotify getEmailNotify(String signType) {
        EmailNotify service = EmailNotify.emailManager.get(signType);
        return service;
    }

    public static EmailNotify getEmailNotify() {
        if(StringUtil.isEmpty(emailType)){
            emailType = PropUtil.getProp("caidaocloud.emailType");
            emailType = StringUtil.isEmpty(emailType) ? "smtp" : emailType;
        }

        return getEmailNotify(emailType);
    }

}
