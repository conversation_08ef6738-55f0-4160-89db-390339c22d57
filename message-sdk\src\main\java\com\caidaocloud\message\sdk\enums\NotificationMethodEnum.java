package com.caidaocloud.message.sdk.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 通知方式
 *
 * <AUTHOR>
 * @date 2022/6/15
 **/
public enum NotificationMethodEnum {

    ONCE("单次通知", "0"),
    CYCLE("循环通知", "1"),
    MERGE("合并通知", "2");
    ;

    public String txt;

    public String value;

    NotificationMethodEnum(String txt, String value) {
        this.txt = txt;
        this.value = value;
    }

    public static NotificationMethodEnum getEnumByVal(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }

        NotificationMethodEnum[] values = NotificationMethodEnum.values();
        for (NotificationMethodEnum notificationMethodEnum : values) {
            if (notificationMethodEnum.value.equals(value)) {
                return notificationMethodEnum;
            }
        }
        return null;
    }

}
