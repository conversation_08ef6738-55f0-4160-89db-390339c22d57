package com.caidaocloud.message.service.infrastructure.repository.impl.log;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.message.service.application.common.constant.LangCodeConstant;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import com.caidaocloud.message.service.application.log.dto.MessageLogRecordDto;
import com.caidaocloud.message.service.application.log.repository.IMessageLogRecordRepository;
import com.caidaocloud.message.service.application.msg.dto.MsgDto;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.domain.msg.entity.MessageLogRecordDo;
import com.caidaocloud.message.service.infrastructure.repository.po.MessageLogRecordPo;
import com.caidaocloud.message.service.infrastructure.utils.LangUtil;
import com.caidaocloud.message.service.interfaces.dto.msg.MessageLogRecordQueryDto;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

/**
 * 消息日志
 * <AUTHOR>
 */
@Repository
public class MessageLogRecordRepositoryImpl implements IMessageLogRecordRepository {
    /**
     * paas模型定义
     */
    private final String LOG_RECORD_IDENTIFIER = "entity.message.LogRecord";

    @Override
    public String saveMessageLogRecord(MessageLogRecordDto logDto) {
        if (logDto == null) {
            String lang = LangUtil.getMsg(LangCodeConstant.code_70000);
            String msg = String.format(lang, "parameter");
            throw new ServerException(msg);
        }
        long currentTimeMillis = System.currentTimeMillis();
        UserInfo userInfo = UserContext.getContextUser();

        MessageLogRecordPo data = FastjsonUtil.convertObject(logDto, MessageLogRecordPo.class);
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        String tenantId = null == userInfo || null == userInfo.getTenantId() ? null : userInfo.getTenantId();
        data.setTenantId(tenantId);
        data.setUpdateBy(userId);
        data.setUpdateTime(currentTimeMillis);
        data.setIdentifier(LOG_RECORD_IDENTIFIER);
        if(logDto.getEmpId() != null){
            EmpSimple emp = new EmpSimple();
            emp.setEmpId(logDto.getEmpId());
            emp.setWorkno(logDto.getWorkno());
            emp.setName(logDto.getName());
            data.setEmp(emp);
        }
        if (logDto.getStatus() != null) {
            EnumSimple sendStatus = logDto.getStatus();
            EnumSimple status = new EnumSimple();
            status.setText(sendStatus.getText());
            status.setValue(sendStatus.getValue());
            data.setStatus(status);
        }
        if (data.getBid() == null) {
            data.setCreateTime(currentTimeMillis);
            data.setCreateBy(userId);
            String bid = DataInsert.identifier(LOG_RECORD_IDENTIFIER).insert(data);
            data.setBid(bid);
        } else {
            DataUpdate.identifier(LOG_RECORD_IDENTIFIER).update(data);
        }
        return data.getBid();
    }

    @Override
    public String updateMessageLogRecordStatus(String mdc, SendStatusEnum sendStatusEnum) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andEq("mdc", String.valueOf(mdc));

        MessageLogRecordQueryDto recordDto = new MessageLogRecordQueryDto();
        PageResult<MessageLogRecordPo> page = DataQuery.identifier(LOG_RECORD_IDENTIFIER).queryInvisible().decrypt()
                .specifyLanguage()
                .limit(recordDto.getPageSize(), recordDto.getPageNo())
                .filter(dataFilter, MessageLogRecordPo.class);
        if (CollectionUtils.isEmpty(page.getItems())) {
            throw new ServerException("消息记录不存在！");
        }
        MessageLogRecordPo messageLogRecordPo = page.getItems().get(0);
        messageLogRecordPo.setUpdateTime(System.currentTimeMillis());
        EnumSimple status = new EnumSimple();
        status.setText(sendStatusEnum.txt);
        status.setValue(sendStatusEnum.status);
        messageLogRecordPo.setStatus(status);
        DataUpdate.identifier(LOG_RECORD_IDENTIFIER).update(messageLogRecordPo);

        return "success";
    }

    @Override
    public String updateMessageLogRecordContent(String mdc, String content) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andEq("mdc", String.valueOf(mdc));

        MessageLogRecordQueryDto recordDto = new MessageLogRecordQueryDto();
        PageResult<MessageLogRecordPo> page = DataQuery.identifier(LOG_RECORD_IDENTIFIER).queryInvisible().decrypt()
                .specifyLanguage()
                .limit(recordDto.getPageSize(), recordDto.getPageNo())
                .filter(dataFilter, MessageLogRecordPo.class);
        if (CollectionUtils.isEmpty(page.getItems())) {
            throw new ServerException("消息记录不存在！");
        }
        MessageLogRecordPo messageLogRecordPo = page.getItems().get(0);
        messageLogRecordPo.setUpdateTime(System.currentTimeMillis());
        messageLogRecordPo.setContent(content);
        DataUpdate.identifier(LOG_RECORD_IDENTIFIER).update(messageLogRecordPo);

        return "success";
    }

    @Override
    public PageResult<MessageLogRecordDo> selectPage(MessageLogRecordQueryDto recordDto) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId());

        if (StringUtils.isNotBlank(recordDto.getName())) {
            dataFilter = dataFilter.andRegex("name", recordDto.getName());
        }

        PageResult<MessageLogRecordPo> result = DataQuery.identifier(LOG_RECORD_IDENTIFIER).queryInvisible().decrypt()
                .specifyLanguage()
                .limit(recordDto.getPageSize(), recordDto.getPageNo())
                .filter(dataFilter, MessageLogRecordPo.class);
        return new PageResult<>(Sequences.sequence(result.getItems()).map(MessageLogRecordPo::toEntity)
                .toList(), result.getPageNo(), result.getPageSize(), result.getTotal());

    }

    @Override
    public MessageLogRecordDo getMessageLogRecordByBid(Long bid) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andEq("bid", String.valueOf(bid));

        MessageLogRecordQueryDto recordDto = new MessageLogRecordQueryDto();
        PageResult<MessageLogRecordPo> page = DataQuery.identifier(LOG_RECORD_IDENTIFIER).queryInvisible().decrypt()
                .specifyLanguage()
                .limit(recordDto.getPageSize(), recordDto.getPageNo())
                .filter(dataFilter, MessageLogRecordPo.class);
        if (CollectionUtils.isEmpty(page.getItems())) {
            return new MessageLogRecordDo();
        }
        MessageLogRecordPo messageLogRecordPo = page.getItems().get(0);
        return messageLogRecordPo.toEntity();
    }

    @Override
    public String updateMessageLogRecord(MsgDto msgDto, SendStatusEnum sendStatusEnum) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andEq("mdc", String.valueOf(msgDto.getMdc()));

        MessageLogRecordQueryDto recordDto = new MessageLogRecordQueryDto();
        PageResult<MessageLogRecordPo> page = DataQuery.identifier(LOG_RECORD_IDENTIFIER).queryInvisible().decrypt()
                .specifyLanguage()
                .limit(recordDto.getPageSize(), recordDto.getPageNo())
                .filter(dataFilter, MessageLogRecordPo.class);
        if (CollectionUtils.isEmpty(page.getItems())) {
            throw new ServerException("消息记录不存在！");
        }
        MessageLogRecordPo messageLogRecordPo = page.getItems().get(0);
        messageLogRecordPo.setUpdateTime(System.currentTimeMillis());
        EnumSimple status = new EnumSimple();
        status.setText(sendStatusEnum.txt);
        status.setValue(sendStatusEnum.status);
        messageLogRecordPo.setStatus(status);
        messageLogRecordPo.setContent(FastjsonUtil.toJson(msgDto));
        DataUpdate.identifier(LOG_RECORD_IDENTIFIER).update(messageLogRecordPo);
        return "success";
    }
}
