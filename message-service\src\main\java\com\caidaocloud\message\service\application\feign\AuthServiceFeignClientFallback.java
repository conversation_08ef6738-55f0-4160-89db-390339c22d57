package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.service.application.message.notifier.dto.RoleUserInfoDto;
import com.caidaocloud.message.service.application.message.notifier.dto.RoleUserQueryDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AuthServiceFeignClientFallback implements AuthServiceFeignClient{
    @Override
    public Result<List<KeyValue>> getAllRole() {
        return Result.fail();
    }

    @Override
    public Result<PageResult<RoleUserInfoDto>> getUserInfoByRoles(RoleUserQueryDto roleUserQuery) {
        return Result.fail();
    }
}
