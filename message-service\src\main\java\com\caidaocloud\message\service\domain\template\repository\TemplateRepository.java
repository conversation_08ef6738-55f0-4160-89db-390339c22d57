package com.caidaocloud.message.service.domain.template.repository;

import com.caidaocloud.message.service.domain.base.repository.BaseRepository;
import com.caidaocloud.message.service.domain.template.entity.TemplateDo;

import java.util.List;

public interface TemplateRepository extends BaseRepository<TemplateDo> {

    void deleteByConfigId(String identifier, String configId);

    void batchInsert(String identifier, List<TemplateDo> list);

    long countName(String identifier, TemplateDo data);

}
