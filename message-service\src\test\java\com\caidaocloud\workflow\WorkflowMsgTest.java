package com.caidaocloud.workflow;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.sdk.dto.MsgNoticeDto;
import com.caidaocloud.message.sdk.event.publish.MsgPublish;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.msg.dto.MsgDto;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.domain.template.entity.TemplateDo;
import com.caidaocloud.message.service.domain.template.service.TemplateDomainService;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class WorkflowMsgTest {
    @Resource
    private MqMessageProducer producer;
    @Resource
    private MsgPublish msgPublish;
    @Resource
    private TemplateDomainService templateDomainService;

    @Test
    public void testMsg(){
        String body = "{\"channel\":[\"1\"],\"emailMsg\":{\"content\":\"<p>Dear<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#流程.审批人#\\\">#流程.审批人#</span><span style=\\\"font-family: 宋体;\\\">，</span> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </p><p> &nbsp; &nbsp; &nbsp; &nbsp; <span style=\\\"font-family: 宋体;\\\">有一条人员调转流程任务等待您处理。</span></p><p> &nbsp; &nbsp; &nbsp; &nbsp; <span style=\\\"font-family: 宋体;\\\">本流程到达您那需要尽快处理，否则会影响该流程生效日期。</span> </p><p><br></p><p><span style=\\\"font-family: 宋体;\\\"> &nbsp; &nbsp; &nbsp; 异动员工工号：</span><span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#流程.申请人工号#\\\">#流程.申请人工号#</span> &nbsp; &nbsp; </p><p> &nbsp; &nbsp; &nbsp; &nbsp; <span style=\\\"font-family: 宋体;\\\">异动员工姓名：</span><span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#流程.申请人#\\\">#流程.申请人#</span> &nbsp;</p><p> &nbsp; &nbsp; &nbsp; &nbsp; <span style=\\\"font-family: 宋体;\\\">拟调整生效日期：</span><span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"2023-03-07\\\">2023-03-07</span> &nbsp; &nbsp; &nbsp;</p><p><br></p><p> &nbsp; &nbsp; &nbsp; &nbsp; <span style=\\\"font-family: 宋体;\\\">请</span><a href=\\\"https://hcm-test.ciwork.cn/module/workflow/backlog\\\" target=\\\"_blank\\\"><span style=\\\"font-family: 宋体;\\\">点击这里</span></a><span style=\\\"font-family: 宋体;\\\">进入工作流系统处理。</span></p>\",\"subject\":\"操作提醒：员工异动信息填写-#流程.申请人工号#，#流程.申请人#\",\"to\":\"<EMAIL>\"},\"tenantId\":\"8\"}";
        log.info("[publishToMessageService] prepare send message to rabbitmq, body=[{}]", body);

        TemplateDo byId = templateDomainService.getById("****************");
        MsgDto msgDto = FastjsonUtil.toObject(body, MsgDto.class);
        msgDto.getEmailMsg().setContent(byId.getContent());
        body = FastjsonUtil.toJson(msgDto);
        var rabbitBaseMessage = new RabbitBaseMessage();
        rabbitBaseMessage.setExchange("message.fac.direct.exchange");
        rabbitBaseMessage.setRoutingKey("routingKey.message.plain.msg");
        rabbitBaseMessage.setBody(body);
        log.info("[publishToMessageService] success send message, body=[{}]", body);
        producer.publish(rabbitBaseMessage);
        producer.publish(rabbitBaseMessage);
    }

    @Test
    public void testMq(){
        String mqMsg = "{\"createTime\":*************,\"msgConfig\":\"****************\",\"msgFrom\":\"OnBoarding\",\"subjects\":[\"****************\"],\"tenantId\":\"33\",\"type\":1,\"userId\":****************}";
        MsgNoticeDto mn = FastjsonUtil.toObject(mqMsg, MsgNoticeDto.class);
        msgPublish.msgNoticePublish(mn, 0);
        msgPublish.msgNoticePublish(mn, 0);
    }

    @Before
    public void initUser(){
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("8");
        userInfo.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);

        UserInfo user = new UserInfo();
        user.setTenantId(String.valueOf(8));
        user.doSetUserId(0L);
        UserContext.setCurrentUser(user);
    }

}
