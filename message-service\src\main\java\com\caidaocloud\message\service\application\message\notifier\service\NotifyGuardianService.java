package com.caidaocloud.message.service.application.message.notifier.service;

import com.caidaocloud.hr.service.dto.EmpDataDto;
import com.caidaocloud.hr.service.dto.EmpPrivateInfoDto;
import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.hr.service.vo.EmpPrivateInfoVo;
import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.message.service.application.message.notifier.input.NotifierDataFactory;
import com.caidaocloud.util.StringUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 查询监护人
 */
@Service
public class NotifyGuardianService implements INotifyTargetService{

    @Override
    public String getNotifyTarget() {
        return NoticeTarget.GUARDIAN.toString();
    }

    @Override
    public void getNotifier(List<NotifierDto> notifier, NotifyObjectDto obj) {
        List<EmpPrivateInfoVo> empDataList = null;
        if(1 == obj.getType()){
            List<EmpDataVo> candidates = NotifierDataFactory
                    .getNotifierService().getCandidateDataByEmpIds(obj.getEmpIds(), obj.getDataTime());
            empDataList = candidates.stream().map(data -> data.getEmpPrivateInfo()).collect(Collectors.toList());
        } else {
            empDataList = NotifierDataFactory
                    .getNotifierService().getEmpGuardianByEmpIds(obj.getEmpIds(), obj.getDataTime());
        }

        if(null == empDataList || empDataList.isEmpty()){
            return;
        }

        List<EmpPrivateInfoVo> collect = empDataList.stream().filter(emp -> !(StringUtil.isEmpty(emp.getGuardianEmail())
                && StringUtil.isEmpty(emp.getGuardianPhone())))
        .map(epi -> {
            epi.setEmpId(null);
            return epi;
        }).collect(Collectors.toList());

        notifier.addAll(listConvert(collect));
    }
}
