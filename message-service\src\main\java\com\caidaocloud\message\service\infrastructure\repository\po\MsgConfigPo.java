package com.caidaocloud.message.service.infrastructure.repository.po;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.service.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.message.service.domain.msg.entity.MergeRule;
import com.caidaocloud.message.service.domain.msg.entity.MsgConfigDo;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;

import org.springframework.beans.BeanUtils;

/**
 *
 * <AUTHOR>
 * @date 2023/3/7
 */
@Data
public class MsgConfigPo extends BaseDomainDoImpl {

	/**
	 * 消息通知名称
	 */
	private String name;

	/**
	 * 通知类型：
	 * 0 续签未签署提醒
	 * 1 电子合同签署提醒
	 * 2 电子合同作废提醒
	 * 3 手动推送
	 */
	private EnumSimple msgType;

	/**
	 * 通知对象
	 * hrbp HRBP
	 * assignEmp 指定系统人员
	 * leader 上级领导
	 * orgLeader 部门负责人
	 * eventEmp 事件人员
	 * allEmp 全部人员
	 * guardian 监护人
	 */
	private String notifyObject;

	private String condition;

	/**
	 * 通知方式
	 * 0 单次通知
	 * 1 循环通知
	 */
	private EnumSimple func;

	/**
	 * 通知轮次
	 * 0 提前
	 * 1 当天
	 * 2 延后
	 */
	private EnumSimple round;

	/**
	 * 通知天数
	 */
	private Integer day;

	/**
	 * 发送时间，0点开始毫秒数
	 */
	private Integer sendTime;

	/**
	 * 通知周期
	 */
	private Integer loop;

	/**
	 * 发送途径
	 */
	private String channel;

	/**
	 * 状态：
	 * 0 未启用
	 * 1 已启用
	 * 2 已停用
	 */
	private EnumSimple status;

	/**
	 * 绑定的模版
	 */
	private String templateBid;

	/**
	 * 指定系统人员
	 */
	private List<EmpSimple> emps;

	/**
	 * 手动推送通知日期
	 */
	private Long triggerDate;


	/**
	 * 通知规则
	 */
	private EnumSimple rule;


	/**
	 * 循环天数
	 */
	private Integer loopDay;


	/**
	 * 合并发送日，1-31
	 */
	private Integer mergeDay;

	/**
	 * 合并发送时间
	 */
	private Integer mergeTime;

	/**
	 * 合并周期
	 */
	private Integer mergePeriod;
	// 事件人过滤，false 不过滤，true 过滤
	private Boolean subjectFilter;

	public static MsgConfigPo fromEntity(MsgConfigDo entity) {
		MsgConfigPo po = ObjectConverter.convert(entity, MsgConfigPo.class);
		if (entity.getMergeRule() != null) {
			BeanUtils.copyProperties(entity.getMergeRule(), po);
		}
		return po;
	}

	public MsgConfigDo toEntity() {
		MsgConfigDo entity = ObjectConverter.convert(this, MsgConfigDo.class);
		MergeRule mergeRule = new MergeRule(mergeDay, mergeTime, mergePeriod);
		entity.setMergeRule(mergeRule);
		return entity;
	}
}
