package com.caidaocloud.message.service.domain.template.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.message.service.domain.base.entity.BaseDomainDo;
import com.caidaocloud.message.service.domain.base.service.BaseDomainServiceImpl;
import com.caidaocloud.message.service.domain.template.entity.PlaceholderDo;
import com.caidaocloud.message.service.interfaces.dto.template.TemplateVariableDto;
import com.caidaocloud.util.ObjectConverter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class PlaceholderDomainService extends BaseDomainServiceImpl<PlaceholderDo, BasePage> {
    @Resource
    private PlaceholderDo placeholderDo;

    @Override
    public BaseDomainDo<PlaceholderDo> getDoService() {
        return placeholderDo;
    }

    public Map<String, TemplateVariableDto> mapPlaceholder(){
        List<PlaceholderDo> list = selectList();
        Map<String, TemplateVariableDto> map = new HashMap();
        if(null == list){
            return map;
        }

        list.forEach(p -> {
            TemplateVariableDto convert = ObjectConverter.convert(p, TemplateVariableDto.class);
            convert.setVarType(null == p.getVarType() ? "0" : p.getVarType().getValue());
            map.put(p.getText(), convert);
        });

        return map;
    }
}