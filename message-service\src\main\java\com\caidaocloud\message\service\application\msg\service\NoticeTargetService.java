package com.caidaocloud.message.service.application.msg.service;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.message.service.application.common.constant.CacheCodeConstant;
import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.service.application.feign.AuthServiceFeignClient;
import com.caidaocloud.message.service.application.feign.HrFeignClient;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NoticeTargetService {

    @Resource
    private AuthServiceFeignClient authServiceFeignClient;

    @Resource
    private HrFeignClient hrFeignClient;

    public List<KeyValue> getNoticeTarget() {
        List<KeyValue> collect = Arrays.stream(NoticeTarget.values()).filter(o -> NoticeTarget.ORG_LEADER != o).map(type -> {
            KeyValue kv = new KeyValue();
            kv.setText(type.getName());
            kv.setValue(type);
            return kv;
        }).collect(Collectors.toList());

        // 查询权限角色
        Result<List<KeyValue>> allRole = authServiceFeignClient.getAllRole();
        List<KeyValue> dataList;
        if(null == allRole || !allRole.isSuccess() || null == (dataList = allRole.getData()) || dataList.isEmpty()){
            //return collect;
        }else{
            List<KeyValue> newList = dataList.stream().map(keyValue -> {
                keyValue.setValue(String.format(CacheCodeConstant.NOTIFY_OBJECT_PREFIX, keyValue.getValue()));
                return keyValue;
            }).collect(Collectors.toList());
            collect.addAll(newList);
        }
        //查询审批人
        dataList = hrFeignClient.getApproverSettings().getData().stream().map(it->new KeyValue(it.get("name").toString(), it.get("bid")))
                .collect(Collectors.toList());
        List<KeyValue> newList = dataList.stream().map(keyValue -> {
            keyValue.setValue(String.format(CacheCodeConstant.NOTIFY_OBJECT_WORKFLOW_APPROVER_PREFIX, keyValue.getValue()));
            return keyValue;
        }).collect(Collectors.toList());
        collect.addAll(newList);
        try{
            List<KeyValue> customized = DataQuery.identifier("entity.message.CustomizedNoticeTarget")
                    .filter(DataFilter.ne("deleted", Boolean.TRUE.toString()), DataSimple.class)
                    .getItems().stream().map(it->{
                        KeyValue target = new KeyValue();
                        target.setText(((SimplePropertyValue)it.getProperties().get("targetText")).getValue());
                        target.setValue(((SimplePropertyValue)it.getProperties().get("targetCode")).getValue());
                        return target;
                    }).collect(Collectors.toList());
            collect.addAll(customized);
        }catch(Exception e){
            log.error("客户自定义通知对象获取失败", e);
        }
        return collect;


    }

    public Map<String, String> getNotifyObjectMap() {
        // 查询权限角色
        Result<List<KeyValue>> allRole = authServiceFeignClient.getAllRole();
        Map<String, String> notifyObjectMap = new HashMap<>();
        List<KeyValue> dataList;
        if(null == allRole || !allRole.isSuccess()
                || null == (dataList = allRole.getData()) || dataList.isEmpty()){
            return notifyObjectMap;
        }

        dataList.stream().forEach(item ->
                notifyObjectMap.put(String.format(CacheCodeConstant.NOTIFY_OBJECT_PREFIX, item.getValue()),
                        item.getText()));
        //查询审批人

        dataList = hrFeignClient.getApproverSettings().getData().stream().map(it->new KeyValue(it.get("name").toString(), it.get("bid")))
                .collect(Collectors.toList());
        dataList.stream().forEach(item ->
                notifyObjectMap.put(String.format(CacheCodeConstant.NOTIFY_OBJECT_WORKFLOW_APPROVER_PREFIX, item.getValue()),
                        item.getText()));
        return notifyObjectMap;
    }
}
