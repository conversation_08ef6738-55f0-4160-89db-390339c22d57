package com.caidaocloud.message.test;

import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.common.enums.MsgType;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import com.caidaocloud.message.service.application.log.dto.MessageLogDto;
import com.caidaocloud.message.service.application.log.repository.IMessageLogRepository;
import com.caidaocloud.message.service.infrastructure.repository.po.MessageLogPo;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class MsgLogTest {

    @Resource
    private IMessageLogRepository messageLogRepository;

    @Test
    public void insertLogTest() {
        MessageLogDto logDto = new MessageLogDto();
        logDto.setType(MsgType.EMAIL);
        logDto.setReceiver("<EMAIL>");
        logDto.setMessage("test");
        logDto.setStatus(SendStatusEnum.SUCESS);
        logDto.setChannel("qq");
//        String bid = messageLogRepository.saveOrUpdateOfLog(logDto);
//        System.out.println(bid);
        System.out.println("success");
    }

    public static void main(String[] args) {
        MessageLogDto logDto = new MessageLogDto();
        logDto.setType(MsgType.EMAIL);
        logDto.setReceiver("<EMAIL>");
        logDto.setMessage("test");
        logDto.setStatus(SendStatusEnum.SUCESS);
        logDto.setChannel("qq");
        MessageLogPo data = FastjsonUtil.convertObject(logDto, MessageLogPo.class);
        System.out.println(data);
    }

}
