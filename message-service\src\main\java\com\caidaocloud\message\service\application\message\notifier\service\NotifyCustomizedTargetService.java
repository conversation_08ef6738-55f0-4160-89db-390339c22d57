package com.caidaocloud.message.service.application.message.notifier.service;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.message.service.application.feign.HrFeignClient;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.message.service.infrastructure.utils.RestUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.caidaocloud.message.service.application.message.notifier.service.NotifyTargetFactory.PREFIX_CUSTOMIZED_TARGET;

/**
 * 查询客户自定义通知对象
 */
@Service
@Slf4j
public class NotifyCustomizedTargetService implements INotifyTargetService {

    @Autowired
    private DiscoveryClient discoveryClient;

    @Autowired
    private HrFeignClient hrFeignClient;

    @Override
    public String getNotifyTarget() {
        return "CustomizedTarget";
    }

    @Override
    public void getNotifier(List<NotifierDto> notifier, NotifyObjectDto obj) {
        if (StringUtils.isEmpty(obj.getEventSubject())) {
            return;
        }
        val eventSubjectEmpId = obj.getEventSubject();
        val customizedTargetCode = obj.getEmpIds().stream().map(it->
                it.replace(PREFIX_CUSTOMIZED_TARGET, "")).collect(Collectors.toList());
        HttpHeaders headers = RestUtil.token();
        List<String> noticeTargets = Lists.list();
        DataQuery.identifier("entity.message.CustomizedNoticeTarget")
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andIn("targetCode", customizedTargetCode), DataSimple.class)
                .getItems().stream().forEach(it -> {
            String serviceName = ((SimplePropertyValue) it.getProperties().get("serviceName")).getValue();
            String path = ((SimplePropertyValue) it.getProperties().get("path")).getValue();
            List<ServiceInstance> services = discoveryClient.getInstances(serviceName);
            String url = String.format("%s/%s", services.get(0).getUri().toString(), path);
            ResponseEntity<String> result = RestUtil.getRestTemplate().postForEntity(url + "?empId="+eventSubjectEmpId,
                    new HttpEntity(null,
                            headers), String.class);
            var success = Lists.list(HttpStatus.OK, HttpStatus.CREATED, HttpStatus.ACCEPTED)
                    .contains(result.getStatusCode());
            if (!success) {
                throw new ServerException("rest call failed, code : " + result.getStatusCodeValue() + ", msg : " + result.getBody());
            }
            val requestResult = FastjsonUtil.toObject(result.getBody(), new TypeReference<Result>(){});
            if(!requestResult.isSuccess()){
                throw new ServerException("request failed, code : " + requestResult.getMsg());
            }
            noticeTargets.addAll(FastjsonUtil.toObject(result.getBody(), new TypeReference<Result<List<String>>>(){})
                    .getData());
        });
        if (noticeTargets.isEmpty()) {
            return;
        }
        List<Map<String, String>> phoneList = DataQuery.identifier("entity.hr.EmpPrivateInfo")
                .filterProperties(DataFilter.in("empId", noticeTargets),
                        Lists.list("empId", "phone"), System.currentTimeMillis())
                .getItems();
        phoneList.removeIf(map -> {
            if (map.get("phone") == null) {
                log.warn("phone is empty,empId={}", map.get("empId"));
                return true;
            }
            return false;
        });
        val phoneMap = phoneList.stream()
                .collect(Collectors.toMap(it -> it.get("empId"), it -> it.get("phone")));
        var result = hrFeignClient.getEmpListByEmpIds(noticeTargets);
        if (result.isSuccess() && !CollectionUtils.isEmpty(result.getData())) {
            List<NotifierDto> collect = result.getData().stream().map(emp -> {
                NotifierDto notifierDto = new NotifierDto();
                String phone = phoneMap.get(emp.getEmpId());
                PhoneSimple phoneSimple = new PhoneSimple();
                phoneSimple.setValue(phone);
                PhoneSimple.doValue(phoneSimple);
                notifierDto.setPhone(phoneSimple);
                notifierDto.setEmail(emp.getCompanyEmail());
                notifierDto.setEmpId(emp.getEmpId());
                return notifierDto;
            }).collect(Collectors.toList());
            notifier.addAll(collect);
        }
    }
}
