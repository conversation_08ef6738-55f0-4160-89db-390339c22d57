package com.caidaocloud.message.service.record.dto;

import com.caidaocloud.dto.BasePage;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * created by: FoAng
 * create time: 23/5/2024 2:35 下午
 */
@Data
@ApiModel("查询日志dto")
@EqualsAndHashCode(callSuper = true)
public class LogRecordQueryDto extends BasePage implements Serializable {

    @ApiModelProperty("操作人姓名")
    private String operatorName;

    @ApiModelProperty("工号")
    private String workNo;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("操作平台")
    private String[] platforms;

    @ApiModelProperty("菜单")
    private String menu;

    @ApiModelProperty("操作类型")
    private String category;

    @ApiModelProperty("搜索关键字")
    private String keyword;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}
