package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.security.config.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/2/6 上午10:40
 * @Version 1.0
 **/
@FeignClient(value = "dingTalkFeignClient",
        url = "https://oapi.dingtalk.com",
        configuration = FeignConfiguration.class,
        fallback = DingTalkFeignClientFallback.class)
public interface DingTalkFeignClient {

    @GetMapping("/gettoken")
    Map<String, String> getDingTalkToken(@RequestParam("appkey") String appkey, @RequestParam("appsecret") String appsecret);

    @PostMapping("/topapi/message/corpconversation/asyncsend_v2")
    Map pushMsg(@RequestBody Map reqMap, @RequestParam("access_token") String accessToken);

    @GetMapping("/topapi/v2/user/getbymobile")
    Map getUserId(@RequestParam("mobile") String mobile, @RequestParam("access_token") String accessToken);


//    @PostMapping(value= "/media/upload" ,produces = {MediaType.MULTIPART_FORM_DATA_VALUE}, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
//    Map uploadMedia(@RequestPart(value = "media") MultipartFile media, @RequestParam("type") String type, @RequestParam("access_token") String accessToken);



    @GetMapping("/topapi/v2/user/get")
    Map getUnionId(@RequestParam("userid") String userid, @RequestParam("access_token") String accessToken);


}



