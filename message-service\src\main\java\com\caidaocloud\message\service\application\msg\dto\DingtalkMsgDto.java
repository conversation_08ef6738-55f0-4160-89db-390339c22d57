package com.caidaocloud.message.service.application.msg.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/2/9 上午10:19
 * @Version 1.0
 **/
@Data
@Accessors(chain = true)
public class DingtalkMsgDto {

    /**
     * 通知标题
     */
    private String subject;

    /**
     * 通知模板类型
     */
    private String templateType;


    /**
     * 通知url
     */
    private String url;


    /**
     * 钉钉内容
     */
    private String content;

    /**
     * 支持
     */
    private String markdown;

    /**
     * 多个附件用逗号分割
     * 附件地址：/caidao-pa/259dtfgdb9cdb5ddab4b-jvm.png
     */
    private String affix;

    /**
     * 指定图标对应的名称
     * jvm.png
     */
    private String affixName;


    /**
     * 用户手机
     */
    private List<PhoneSimple> mobiles;

    /**
     * 截止时间，Unix时间戳，单位毫秒
     */
    private Long dueTime;

//    public String msgReceiver(){
//        String phoneNumbers = mobiles.stream()
//                .filter(m -> StringUtil.isNotEmpty(m.getValue()))
//                .map(m -> {
//                    if(m.getValue().indexOf("/") > 0){
//                        return m.getValue().substring(0, m.getValue().indexOf("/"));
//                    }
//                    return m.getValue();
//                }).collect(Collectors.joining(","));
//        return phoneNumbers;
//    }

    /**
     * 接受消息的empId
     */
    private List<String> empIds;

    /**
     * 发送状态
     */
    private SendStatusEnum status = SendStatusEnum.SUCESS;


    public void addMobile(String phone){
        if(null == phone){
            return;
        }
        if(null == mobiles){
            this.mobiles = new ArrayList<>();
        }
        addMobile(phone);
    }

    public void addMobile(PhoneSimple ps){
        if(null == mobiles){
            this.mobiles = new ArrayList<>();
        }
        mobiles.add(ps);
    }

}

 
    
    
    
    