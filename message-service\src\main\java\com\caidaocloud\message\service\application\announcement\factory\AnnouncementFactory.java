package com.caidaocloud.message.service.application.announcement.factory;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.message.service.application.announcement.dto.AnnouncementDto;
import com.caidaocloud.message.service.domain.announcement.entity.Announcement;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementContent;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementReceive;
import com.caidaocloud.message.service.domain.announcement.enums.ReceiveType;
import com.googlecode.totallylazy.Sequences;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/11
 */
public class AnnouncementFactory {
    
    /**
     * DTO转换为实体
     * @param dto 公告DTO
     * @return 公告实体
     */
    public static Announcement createAnnouncement(AnnouncementDto dto) {
        Announcement announcement = new Announcement(dto.getName(),dto.getType());

        announcement.setEffectiveTime(dto.getEffectiveTime());
        announcement.setExpiryTime(dto.getExpiryTime());
        announcement.setReceiveType(dto.getReceiveType().toEnumSimple());
        announcement.setIsTop(dto.getIsTop());
        announcement.setDomain(dto.getDomain());
        
        return announcement;
    }
    
    /**
     * 实体转换为DTO
     * @param announcement 公告实体
     * @return 公告DTO
     */
    public static AnnouncementDto entityToDto(Announcement announcement) {
        if (announcement == null) {
            return null;
        }
        
        AnnouncementDto dto = new AnnouncementDto();
        dto.setBid(announcement.getBid());
        dto.setName(announcement.getName());
        
        // 设置公告类型
        if (announcement.getType() != null) {
            dto.setType(announcement.getType().getValue());
        }
        
        dto.setReleaseTime(announcement.getReleaseTime());
        dto.setEffectiveTime(announcement.getEffectiveTime());
        dto.setExpiryTime(announcement.getExpiryTime());
        
        // 设置接收类型
        if (announcement.getReceiveType() != null) {
            String receiveTypeValue = announcement.getReceiveType().getValue();
            if (receiveTypeValue != null) {
                dto.setReceiveType(ReceiveType.fromValue(Integer.parseInt(receiveTypeValue)));
            }
        }
        
        dto.setIsTop(announcement.getIsTop());
        dto.setDomain(announcement.getDomain());
        
        // 设置内容和附件
        if (announcement.getContent() != null) {
            dto.setContent(announcement.getContent().getContent());
            dto.setAttachment(announcement.getContent().getAttachment());
        }
        
        // 设置接收者值列表
        if (dto.getReceiveType()==ReceiveType.DESIGNATED_DEPARTMENT || dto.getReceiveType()==ReceiveType.DESIGNATED_AND_SUB_DEPARTMENTS){
            dto.setReceiveDeptList(Sequences.sequence(announcement.getReceiveList())
                    .map(AnnouncementReceive::getReceiveValue).toList());
        }else if (dto.getReceiveType()==ReceiveType.DESIGNATED_PERSONNEL){
            dto.setReceiveEmpList(Sequences.sequence(announcement.getReceiveList())
                    .map(AnnouncementReceive::getReceiveEmp).toList());
        }

        return dto;
    }
    
    /**
     * DTO转换为公告内容实体
     * @param dto 公告DTO
     * @param announcementId 公告ID
     * @return 公告内容实体
     */
    public static AnnouncementContent createContent(AnnouncementDto dto, String announcementId) {
        if (dto == null) {
            return null;
        }
        
        AnnouncementContent content = new AnnouncementContent();
        content.setBid(announcementId);
        content.setAnnouncementId(announcementId);
        content.setContent(dto.getContent());
        content.setAttachment(dto.getAttachment());
        
        return content;
    }
    
    /**
     * DTO转换为接收者实体列表
     * @param dto 公告DTO
     * @param announcementId 公告ID
     * @return 接收者实体列表
     */
    public static List<AnnouncementReceive> createReceiver(AnnouncementDto dto, String announcementId) {
        List<AnnouncementReceive> receiveList = new ArrayList<>();
        
        if (dto == null) {
            return receiveList;
        }
        if (dto.getReceiveType()==ReceiveType.ALL_PERSONNEL) {
            AnnouncementReceive receive = new AnnouncementReceive();
            receive.setAnnouncementId(announcementId);

            receive.setReceiveType(dto.getReceiveType().toEnumSimple());
            receiveList.add(receive);
            return receiveList;
        }

        if (dto.getReceiveType()==ReceiveType.DESIGNATED_PERSONNEL){
            for (EmpSimple receiveValue : dto.getReceiveEmpList()) {
                AnnouncementReceive receive = new AnnouncementReceive();
                receive.setAnnouncementId(announcementId);
                receive.setReceiveValue(receiveValue.getEmpId());
                receive.setReceiveEmp(receiveValue);
                receive.setReceiveType(dto.getReceiveType().toEnumSimple());
                receiveList.add(receive);
            }
            return receiveList;
        }

        for (String receiveValue : dto.getReceiveDeptList()) {
            AnnouncementReceive receive = new AnnouncementReceive();
            receive.setAnnouncementId(announcementId);
            receive.setReceiveValue(receiveValue);
            receive.setReceiveType(dto.getReceiveType().toEnumSimple());
            receiveList.add(receive);
        }
        return receiveList;
    }
}
