package com.caidaocloud.message.service.infrastructure.utils;

import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.message.service.application.message.notifier.service.NotifyCustomizedTargetService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.WebUtil;
import lombok.var;
import org.springframework.http.HttpHeaders;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Optional;

public class RestUtil {

    private static RestTemplate restTemplate;

    public static RestTemplate getRestTemplate() {
        if (restTemplate == null) {
            synchronized (NotifyCustomizedTargetService.class) {
                if (restTemplate == null) {
                    restTemplate = new RestTemplate();
                }
            }
        }
        return restTemplate;
    }

    public static HttpHeaders token() {
        var headers = new HttpHeaders();
        var request = WebUtil.getRequest();
        if (null != request) {
            headers.add("Access-Token", request.getHeader("Access-Token"));
            headers.add("accept-language", request.getHeader("accept-language"));
            Optional<SecurityUserInfo> internalCallUser = com.caidaocloud.security.util.RestUtil.getInternalRequestUser();
            if (internalCallUser.isPresent()) {
                headers.add("Internal-Call-User", request.getHeader("Internal-Call-User"));
            }
        }
        if (null != SecurityUserUtil.getThreadLocalSecurityUserInfo()) {
            headers.add("Internal-Call-User", Base64.getEncoder()
                    .encodeToString(FastjsonUtil.toJson(SecurityUserUtil.getThreadLocalSecurityUserInfo())
                            .getBytes(StandardCharsets.UTF_8)));
        }
        headers.add(CommonConstant.IS_FEIGN_REQUEST, "true");
        return headers;
    }
}
