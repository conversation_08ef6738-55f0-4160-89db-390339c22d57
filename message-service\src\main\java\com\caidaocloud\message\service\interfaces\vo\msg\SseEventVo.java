package com.caidaocloud.message.service.interfaces.vo.msg;

import com.caidaocloud.message.sdk.enums.SseEventEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 推流事件
 * created by: FoAng
 * create time: 20/8/2024 2:36 下午
 */
@Data
public class SseEventVo implements Serializable {

    @ApiModelProperty("事件类型")
    private SseEventEnum eventType;

    @ApiModelProperty("详情")
    private String content;

}
