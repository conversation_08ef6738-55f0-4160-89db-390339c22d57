package com.caidaocloud.message.sdk.enums;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.caidaocloud.condition.tree.ConditionCallType;
import com.caidaocloud.condition.tree.ConditionItem;
import com.caidaocloud.condition.tree.ConditionOperator;
import com.caidaocloud.condition.tree.ValueComponent;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;

// 定义 ConditionItem 枚举类，每个枚举值对应一个 ConditionItem 实例
public enum ConditionItems {
    EMP_TYPE(
            "用工类型",
            "hr#EmpWorkInfo#empType$dictValue",
            ConditionOperator.noContainChild(),
            ConditionCallType.OUTSIDE_SERVICE,
            ValueComponent.DICT_SELECTOR,
            Maps.map("belongModule", "Employee", "typeCode", "EmployType"),
            null, "entity.hr.EmpWorkInfo", "empType.dict.value"),
    CONFIRMATION_STATUS(
            "转正状态",
            "hr#EmpWorkInfo#confirmationStatus",
            ConditionOperator.noContainChild(),
            ConditionCallType.OUTSIDE_SERVICE,
            ValueComponent.ENUM,
            null,
            null, "entity.hr.EmpWorkInfo", "confirmationStatus"),
    COMPANY(
            "合同公司",
            "hr#EmpWorkInfo#company",
            ConditionOperator.noContainChild(),
            ConditionCallType.OUTSIDE_SERVICE,
            ValueComponent.COMPANY,
            null,
            null, "entity.hr.EmpWorkInfo", "company"),
    ORGANIZE(
            "任职组织",
            "hr#EmpWorkInfo#organize",
            ConditionOperator.enableOpts(),
            ConditionCallType.OUTSIDE_SERVICE,
            ValueComponent.ORG,
            null,
            null, "entity.hr.EmpWorkInfo", "organize"),
    WORKPLACE(
            "工作地",
            "hr#EmpWorkInfo#workplace",
            ConditionOperator.noContainChild(),
            ConditionCallType.OUTSIDE_SERVICE,
            ValueComponent.WORKPLACE,
            null,
            null, "entity.hr.EmpWorkInfo", "workplace"),
    JOB_GRADE(
            "职级",
            "hr#EmpWorkInfo#jobGrade$startGrade",
            ConditionOperator.noContainChild(),
            ConditionCallType.INSIDER_SERVICE,
            ValueComponent.JOB_GRADE,
            null,
            "api/hr/jobgrade/v1/treeList", "entity.hr.EmpWorkInfo", "jobGrade.startGrade"),
    CONTRACT_SETTING_TYPE(
            "合同类型",
            "hr#LastContract#contractSettingType$dictValue#owner.empId",
            ConditionOperator.noContainChild(),
            ConditionCallType.INSIDER_SERVICE,
            ValueComponent.DICT_SELECTOR,
            Maps.map("belongModule", "Employee", "typeCode", "ContractType"),
            null, "entity.hr.LastContract", "contractSettingType.dict.value"),
    SEX(
            "性别",
            "hr#EmpPrivateInfo#sex$dictValue",
            ConditionOperator.noContainChild(),
            ConditionCallType.INSIDER_SERVICE,
            ValueComponent.DICT_SELECTOR,
            Maps.map("belongModule", "Employee", "typeCode", "Gender"),
            null, "entity.hr.EmpPrivateInfo", "sex.dict.value"),
    COM_CAREER(
            "劳动合同主体与科锐的关系",
            "hr#EmpWorkInfo#comCareer$dictValue",
            ConditionOperator.noContainChild(),
            ConditionCallType.OUTSIDE_SERVICE,
            ValueComponent.DICT_SELECTOR,
            Maps.map("belongModule", "Employee", "typeCode", "comCareer"),
            null, "entity.hr.EmpWorkInfo", "comCareer.dict.value"),
    EMPSUBTYPE(
            "员工子类型",
            "hr#EmpWorkInfo#empsubtype$dictValue",
            ConditionOperator.noContainChild(),
            ConditionCallType.OUTSIDE_SERVICE,
            ValueComponent.DICT_SELECTOR,
            Maps.map("belongModule", "Employee", "typeCode", "Subtype"),
            null, "entity.hr.EmpWorkInfo", "empsubtype.dict.value");

    private final String name;
    private final String code;
    private final List<ConditionOperator> operators;
    private final ConditionCallType type;
    private final ValueComponent component;
    private final Map<String, String> dataSourceParams;
    private final String url;
    private final String identifier;
    private final String property;

    ConditionItems(String name, String code, List<ConditionOperator> operators, ConditionCallType type, ValueComponent component,
            Map<String, String> dataSourceParams, String url, String identifier, String property) {
        this.name = name;
        this.code = code;
        this.operators = operators;
        this.type = type;
        this.component = component;
        this.dataSourceParams = dataSourceParams;
        this.url = url;
        this.identifier = identifier;
        this.property = property;
    }

    public String getCode() {
        return code;
    }

    public ConditionItem toConditionItem() {
        ConditionItem condition = new ConditionItem();
        condition.setName(name);
        condition.setCode(code);
        condition.setOperators(operators);
        condition.setType(type);
        condition.setComponent(component);
        condition.setDataSourceParams(dataSourceParams);
        condition.setDataSourceAddress(url);
        return condition;
    }

    // 使用枚举生成 ConditionItem 列表
    public static List<ConditionItem> generateConditionItemList() {
        List<ConditionItem> result = new ArrayList<>();
        for (ConditionItems item : ConditionItems.values()) {
            result.add(item.toConditionItem());
        }
        return result;
    }

    public static List<ConditionItems> getStandardItems() {
        return Sequences.sequence(ConditionItems.values()).filter(item -> {
            if ("8".equals(SecurityUserUtil.getSecurityUserInfo().getTenantId())) {
                return true;
            }
            return item != COM_CAREER && item != EMPSUBTYPE;
        }).toList();
    }

    public String getIdentifier() {
        return identifier;
    }

    public String getProperty() {
        return property;
    }
}