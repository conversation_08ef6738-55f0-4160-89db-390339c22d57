package com.caidaocloud.message.service.record.feign.callback;

import com.caidaocloud.hrpaas.paas.common.dto.KvDto;
import com.caidaocloud.message.service.record.feign.PaasFeign;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

/**
 * created by: FoAng
 * create time: 22/5/2024 4:37 下午
 */
@Component
public class PaasFeignFallback implements PaasFeign {

    @Override
    public Result<KvDto> saveKv(KvDto kv) {
        return Result.fail("保存失败");
    }

    @Override
    public Result<String> getKv(String key) {
        return Result.fail("获取失败");
    }
}
