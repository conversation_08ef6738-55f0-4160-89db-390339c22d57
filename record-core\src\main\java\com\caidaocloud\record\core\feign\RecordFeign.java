package com.caidaocloud.record.core.feign;

import com.caidaocloud.record.core.beans.LogRecordData;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * created by: FoAng
 * create time: 28/5/2024 2:45 下午
 */
@FeignClient(
        /*value = "caidaocloud-log-record-service",*/
        value = "caidaocloud-message-service",
        fallback = RecordFallback.class,
        configuration = FeignConfiguration.class,
        contextId = "record-service"
)
public interface RecordFeign {

    @PostMapping("/api/log/record/v1/save")
    Result<?> saveRecord(@RequestBody LogRecordData logRecordData);
}
