CREATE TABLE IF NOT EXISTS log_record_$tenantId (
                                          id bigserial PRIMARY KEY,
                                          operator bigint NOT NULL,
                                          emp_id bigint,
                                          operator_account varchar(50),
                                          operator_mobile varchar(50),
                                          operator_email varchar(50),
                                          operator_name varchar(50),
                                          operator_source varchar(50),
                                          operator_platform varchar(50),
                                          uri varchar(255),
                                          menu varchar(255),
                                          category varchar(255) ,
                                          action varchar(255),
                                          detail text,
                                          status boolean DEFAULT true,
                                          create_time date DEFAULT now(),
                                          is_deleted integer DEFAULT 0
);
COMMENT ON TABLE log_record_$tenantId IS '日志记录表';
COMMENT ON COLUMN log_record_$tenantId.id IS '主键ID';
COMMENT ON COLUMN log_record_$tenantId.operator IS '操作主键';
COMMENT ON COLUMN log_record_$tenantId.emp_id IS '员工ID';
COMMENT ON COLUMN log_record_$tenantId.operator_source IS '操作来源';
COMMENT ON COLUMN log_record_$tenantId.operator_account IS '操作账号';
COMMENT ON COLUMN log_record_$tenantId.operator_mobile IS '操作手机号';
COMMENT ON COLUMN log_record_$tenantId.operator_email IS '操作邮箱';
COMMENT ON COLUMN log_record_$tenantId.operator_name IS '操作姓名';
COMMENT ON COLUMN log_record_$tenantId.operator_platform IS '操作平台';
COMMENT ON COLUMN log_record_$tenantId.uri IS '请求uri';
COMMENT ON COLUMN log_record_$tenantId.action IS '操作摘要';
COMMENT ON COLUMN log_record_$tenantId.detail IS '操作详情';
COMMENT ON COLUMN log_record_$tenantId.status IS '操作状态';
COMMENT ON COLUMN log_record_$tenantId.create_time IS '创建时间';
COMMENT ON COLUMN log_record_$tenantId.is_deleted IS '是否删除';