package com.caidaocloud.message.sdk.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.sdk.enums.FormWorkType;
import lombok.Data;

import java.util.List;

/**
 * 候选人流程信息VO
 *
 * <AUTHOR>
 */
@Data
public class PreEmpEntryProcessVo {
    /**
     * 员工ID
     */
    private String empId;

    /**
     * 员工工号
     */
    private String workno;

    /**
     * 员工姓名（个人信息冗余字段
     */
    private String name;

    /**
     * 员工英文名（个人信息冗余字段
     */
    private String enName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 证件号
     */
    private String cardNo;

    /**
     * 员工个人邮箱
     */
    private String email;

    /**
     * 所属组织
     */
    private String organize;

    /**
     * 所属组织名称（国际化）
     */
    private String organizeTxt;

    /**
     * 岗位
     */
    private String post;

    /**
     * 岗位名称（国际化）
     */
    private String postTxt;

    /**
     * 用工类型\员工类型
     */
    private DictSimple empType;

    /**
     * 入职日期
     */
    private Long hireDate;

    /**
     * 入职培训日期
     */
    private Long trainingDate;

    /**
     * 员工入职状态
     */
    private EnumSimple status;

    /**
     * 取消入职原因
     */
    private String cancelReason;

    /**
     * 入职业务线id
     */
    private String businessLineId;

    /**
     * 步骤明细id
     */
    private String stepLabelId;

    /**
     * 步骤id
     */
    private String stepId;

    /**
     * 步骤状态
     */
    private EnumSimple empStepStatus;

    /**
     * 退回原因
     */
    private String rollbackReason;

    /**
     * 入职模块
     */
    private List<FormWorkType> taskModule;

    /**
     * 处理人
     */
    private String processor;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 入职发起人名称
     */
    private String initiatorName;

    /**
     * 入职发起人Id
     */
    private String initiatorId;

    /**
     * 入职发起时间
     */
    private Long initiateDate;

    /**
     * 欢迎页附件
     */
    private Attachment entryFile;

}
