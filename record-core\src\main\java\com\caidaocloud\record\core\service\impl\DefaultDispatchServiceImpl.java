package com.caidaocloud.record.core.service.impl;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.record.core.beans.LogRecordData;
import com.caidaocloud.record.core.constants.MsgConstant;
import com.caidaocloud.record.core.feign.RecordFeign;
import com.caidaocloud.record.core.message.LogRecordMessage;
import com.caidaocloud.record.core.service.IDispatchRecordService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * 默认日志消费处理模式
 * created by: FoAng
 * create time: 21/5/2024 6:44 下午
 */  
@Slf4j
@AllArgsConstructor
public class DefaultDispatchServiceImpl implements IDispatchRecordService {

    private MqMessageProducer<LogRecordMessage> producer;

    private CacheService cacheService;

    private RecordFeign recordFeign;

    private AsyncTaskExecutor recordExecutor;

    /**
     * 发送消息
     * @param record
     */
    public void dispatchLogRecord(LogRecordData record) throws Exception {
        if (producer == null && recordFeign == null) {
            throw new Exception("[record] record consumer not config....");
        }
        if (!StringUtils.isEmpty(record.getTenantId())/* && !record.getTenantId().equals("0")*/) {
            String cacheData = cacheService.getValue(MsgConstant.CACHE_PAAS_KV_LOG_RECORD);
            List<String> cacheTenants = Optional.ofNullable(cacheData).map(it -> FastjsonUtil.toList(it, String.class))
                    .orElse(Lists.newArrayList());
            if (cacheTenants.contains(record.getTenantId())) {
                if (producer != null) {
                    dispatchByMsg(record);
                } else {
                    SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
                    recordExecutor.execute(() -> {
                        try {
                            SecurityUserUtil.setSecurityUserInfo(userInfo);
                            dispatchByFeign(record);
                        } catch (Exception e) {
                            log.error("[record] dispatch record by feign error:{}", e.getMessage());
                        } finally {
                            SecurityUserUtil.removeSecurityUserInfo();
                        }
                    });
                }
            } else {
                log.info("[record] annotation is enable, but unResolve cache config status.....");
            }
        }
    }

    private void dispatchByMsg(LogRecordData record) {
        final String routingKey = String.format("%s_%s", MsgConstant.ROUTING_KEY_RECORD_EVENT, record.getTenantId());
        LogRecordMessage message = new LogRecordMessage();
        message.setBody(FastjsonUtil.toJson(record));
        message.setExchange(MsgConstant.EXCHANGE_KEY_RECORD_EVENT);
        message.setRoutingKey(routingKey);
        producer.publish(message);
        log.info("[record] dispatchByRabbitmq success, exchange:{}, routingKey:{}", message.getExchange(),
                message.getRoutingKey());
    }

    private void dispatchByFeign(LogRecordData recordData) {
        Result<?> result = recordFeign.saveRecord(recordData);
        if (result != null && result.isSuccess()) {
            log.info("[record] dispatchByFeign success.....");
        } else {
            log.error("[record] dispatchByFeign failed, msg:{}",
                    Optional.ofNullable(result).map(Result::getMsg).orElse(""));
        }
    }
}
