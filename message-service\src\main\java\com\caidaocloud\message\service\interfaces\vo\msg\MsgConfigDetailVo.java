package com.caidaocloud.message.service.interfaces.vo.msg;

import com.caidaocloud.condition.tree.ConditionTree;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.message.sdk.enums.NoticeRule;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.service.interfaces.vo.template.TemplateVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel("消息配置详情vo")
public class MsgConfigDetailVo {

    @ApiModelProperty("业务id")
    private String bid;

    @ApiModelProperty("通知名称")
    private String name;

    @ApiModelProperty("通知名称")
    private Map<String,Object> i18nName;

    @ApiModelProperty("通知类型")
    private NoticeType msgType;

    @ApiModelProperty("通知对象")
    private List<String> notifyObject;

    @ApiModelProperty("匹配条件")
    private ConditionTree condition;

    @ApiModelProperty("通知方式")
    private String func;

    @ApiModelProperty("通知轮次")
    private String round;

    @ApiModelProperty("通知天数")
    private Integer day;

    @ApiModelProperty("手动推送通知日期")
    private Long triggerDate;
    @ApiModelProperty("发送时间，秒级")
    private Integer sendTime;

    @ApiModelProperty("通知周期")
    private Integer loop;

    @ApiModelProperty("发送途径")
    private List<String> channel;

    @ApiModelProperty("模版信息")
    private List<TemplateVo> templates;

    @ApiModelProperty("指定系统人员")
    private List<EmpSimple> emps;

    @ApiModelProperty("通知规则")
    private NoticeRule rule;

    @ApiModelProperty("循环天数")
    private Integer loopDay;

    @ApiModelProperty("合并规则")
    private MergeRuleVo mergeRule;
    @ApiModelProperty("事件人过滤")
    private Boolean subjectFilter = false;
}
