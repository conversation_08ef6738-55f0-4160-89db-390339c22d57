package com.caidaocloud.message.service.record.provider.es.impl;

import com.caidaocloud.message.service.record.provider.es.DynamicIndexProvider;
import com.caidaocloud.record.core.annotation.DynamicIndex;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.zxp.esclientrhl.util.IndexTools;
import org.zxp.esclientrhl.util.MetaData;

import java.util.Arrays;

@Slf4j
@Component
@Primary
@AllArgsConstructor
public class CusDynamicIndexTool extends IndexTools {

    private DynamicIndexProvider indexProvider;

    @Override
    public MetaData getMetaData(Class<?> clazz) {
        MetaData metaData = super.getMetaData(clazz);
        boolean dynamicIndexMode = clazz.isAnnotationPresent(DynamicIndex.class);
        if (dynamicIndexMode) {
            DynamicIndex dynamicIndex = clazz.getAnnotation(DynamicIndex.class);
            if (dynamicIndex.enable()) {
                metaData.setIndexname(indexProvider.getTenantIndex(metaData.getIndexname()));
                if (metaData.getSearchIndexNames() != null) {
                    String[] tenantSearchIndexes = Arrays.stream(metaData.getSearchIndexNames()).map(it -> indexProvider.getTenantIndex(it))
                            .toArray(String[]::new);
                    metaData.setSearchIndexNames(tenantSearchIndexes);
                }
            }
        }
        return metaData;
    }
}