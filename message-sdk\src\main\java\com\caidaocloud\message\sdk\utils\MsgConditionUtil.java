package com.caidaocloud.message.sdk.utils;

import java.util.List;
import java.util.Map;

import com.caidaocloud.condition.tree.ConditionItem;
import com.caidaocloud.hrpaas.paas.match.ConditionNameMapping;
import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import com.caidaocloud.message.sdk.enums.ConditionItems;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;

/**
 *
 * <AUTHOR>
 * @date 2024/4/3
 */
public class MsgConditionUtil {
	public static boolean match(String empId, ConditionTree tree) {
		List<ConditionItems> itemList = ConditionItems.getStandardItems();
		List<ConditionNameMapping> mappings = Sequences.sequence(itemList).map(item -> {
			ConditionNameMapping mapping = new ConditionNameMapping();
			mapping.setName(item.getCode());
			mapping.setMappingIdentifier(item.getIdentifier());
			mapping.setMappingProperty(item.getProperty());
			return mapping;
		}).toList();
		tree.setIdentifierEmpIdKey(Maps.map("entity.hr.LastContract","owner$empId"));
		return tree.match(empId, mappings);
	}
}
