package com.caidaocloud.message.service.domain.base.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.service.domain.base.entity.BaseDomainDo;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.util.StringUtil;

import java.util.List;

public class BaseDomainServiceImpl<T extends DataSimple, D extends BasePage> implements BaseDomainService<T, D> {

    @Override
    public BaseDomainDo<T> getDoService() {
        return null;
    }

    @Override
    public T getDetail(T data) {
        return null;
    }

    @Override
    public T getOne(T data) {
        return null;
    }

    @Override
    public List<T> getAll(T data) {
        return null;
    }

    @Override
    public T getById(String bid) {
        T data = getDoService().getByBid(bid);
        return data;
    }

    @Override
    public PageResult<T> getPage(D query) {
        return getDoService().getPage(query);
    }

    @Override
    public void delete(String bid) {
        getDoService().deleteByBid(bid);
    }

    @Override
    public void softDelete(String bid) {

    }

    @Override
    public T save(T data) {
        setRequiredField(data, true);
        linkDataConvert(data);
        return getDoService().getRepository().insert(data);
    }

    @Override
    public void batchSave(List<T> data) {

    }

    @Override
    public T saveOrUpdate(T data) {
        return StringUtil.isEmpty(data.getBid()) ? save(data) : updateById(data);
    }

    @Override
    public T updateById(T data) {
        setRequiredField(data, false);
        linkDataConvert(data);
        getDoService().getRepository().updateById(data);
        return data;
    }

    @Override
    public List<T> selectList() {
        return getDoService().selectList();
    }

    @Override
    public List<T> selectByIds(List<String> ids) {
        return getDoService().selectByIds(ids);
    }

    @Override
    public int deleteBatchIds(List<String> ids) {
        return getDoService().deleteBatchIds(ids);
    }

    /**
     * 填充系统必填字段
     */
    protected void setRequiredField(T data, boolean saveOrUpdate) {
        UserInfo userInfo = UserContext.preCheckUser();
        final String userId = userInfo.getUserId().toString();
        long currentTimeMillis = System.currentTimeMillis();
        data.setIdentifier(getDoService().getDoIdentifier());
        data.setDeleted(Boolean.FALSE);

        if(!saveOrUpdate){
            // 修改
            data.setUpdateBy(userId);
            data.setTenantId(userInfo.getTenantId());
            if(data.getUpdateTime() == 0){
                data.setUpdateTime(currentTimeMillis);
            }
            return;
        }

        data.setCreateBy(userId);
        // 新增时，修改人信息默认为新增人信息
        data.setUpdateBy(userId);
        if(data.getCreateTime() != 0){
            return;
        }

        data.setCreateTime(currentTimeMillis);
        data.setUpdateTime(currentTimeMillis);
    }

    protected void linkDataConvert(T data){}
}
