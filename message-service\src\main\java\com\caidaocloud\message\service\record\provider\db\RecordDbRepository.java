package com.caidaocloud.message.service.record.provider.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidaocloud.dto.BasePage;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.service.record.bean.LogRecord;
import com.caidaocloud.message.service.record.bean.UserAccountInfo;
import com.caidaocloud.message.service.record.dto.LogRecordQueryDto;
import com.caidaocloud.message.service.record.provider.RecordRepository;
import com.caidaocloud.message.service.record.vo.LogRecordVo;
import com.caidaocloud.util.FastjsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 28/5/2024 3:30 下午
 */
@Slf4j
@AllArgsConstructor
public class RecordDbRepository implements RecordRepository {

    private IRecordDbService recordService;

    public static IPage<LogRecord> of(BasePage query) {
        return new Page<>(query.getPageNo(), query.getPageSize());
    }

    @Override
    public void saveRecord(LogRecord record, UserAccountInfo accountInfo) {
        recordService.save(record);
    }

    @Override
    public PageResult<LogRecordVo> pageRecord(LogRecordQueryDto queryDto) {
        IPage<LogRecord> page = recordService.page(of(queryDto), buildQuery(queryDto));
        return new PageResult<>(transfer(page.getRecords()), (int) page.getCurrent(), (int) page.getSize(), (int) page.getTotal());
    }

    @Override
    public List<LogRecordVo> listRecord(LogRecordQueryDto queryDto) {
        return transfer(recordService.list(buildQuery(queryDto)));
    }

    @Override
    public <T> List<LogRecordVo> transfer(List<T> list) {
        return list.stream().map(it -> {
            return FastjsonUtil.convertObject(it, LogRecordVo.class);
        }).collect(Collectors.toList());
    }

    private LambdaQueryWrapper<LogRecord> buildQuery(LogRecordQueryDto queryDto) {
        return new LambdaQueryWrapper<LogRecord>()
                .and(!StringUtils.isEmpty(queryDto.getOperatorName()), it -> it.like(LogRecord::getOperatorName,
                        queryDto.getOperatorName()))
                .eq(LogRecord::getOperatorAccount, queryDto.getAccount())
                .eq(LogRecord::getMenu, queryDto.getMenu())
                .in(LogRecord::getOperatorPlatform, (Object[]) queryDto.getPlatforms())
                .between(LogRecord::getCreateTime, queryDto.getStartTime(), queryDto.getEndTime())
                .and(!StringUtils.isEmpty(queryDto.getKeyword()), it -> {
                    it.like(LogRecord::getOperatorName, queryDto.getKeyword())
                            .or()
                            .like(LogRecord::getDetail, queryDto.getKeyword());
                });
    }
}
