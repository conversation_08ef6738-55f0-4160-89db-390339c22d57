package com.caidaocloud.message.service.interfaces.vo.msg;

import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import com.caidaocloud.message.sdk.enums.NoticeRule;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.enums.NotificationCycleTypeEnum;
import com.caidaocloud.message.sdk.enums.NotificationMethodEnum;
import com.caidaocloud.message.service.application.common.enums.MsgConfigStatusEnum;
import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.service.domain.msg.entity.MsgConfigDo;
import com.caidaocloud.message.service.interfaces.vo.template.TemplateVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/15
 **/
@Data
@ApiModel("消息配置vo")
public class MsgConfigVo {

    @ApiModelProperty("业务id")
    private String bid;

    @ApiModelProperty("通知名称")
    private String name;

    @ApiModelProperty("通知类型")
    private NoticeType msgType;

    @ApiModelProperty("通知对象")
    private List<NoticeTarget> notifyObject;

    @ApiModelProperty("匹配条件")
    private Object condition;

    @ApiModelProperty("通知方式")
    private NotificationMethodEnum func;

    @ApiModelProperty("通知轮次")
    private NotificationCycleTypeEnum round;

    @ApiModelProperty("通知天数")
    private Integer day;

    @ApiModelProperty("手动推送通知日期")
    private Long triggerDate;
    @ApiModelProperty("发送时间")
    private Integer sendTime;

    @ApiModelProperty("通知周期")
    private Integer loop;

    /**
     * 发送途径：1,2,3,4
     * 3：app 通知
     * 2：系统通知
     * 0：短信通知
     * 1：邮件通知
     * <p>
     * 多个发送途径，英文逗号分割
     */
    @ApiModelProperty("发送途径")
    private String channel;

    @ApiModelProperty("状态")
    private MsgConfigStatusEnum status;

    @ApiModelProperty("模版信息")
    private List<TemplateVo> templateList;


    @ApiModelProperty("通知规则")
    private NoticeRule rule;

    @ApiModelProperty("循环通知，循环结束天数")
    private Integer loopDay;

    @ApiModelProperty("合并规则")
    private MergeRuleVo mergeRule;

    // 事件人过滤，false 不过滤，true 过滤
    private Boolean subjectFilter;


    public static MsgConfigVo fromEntity(MsgConfigDo entity) {
        MsgConfigVo vo = ObjectConverter.convert(entity, MsgConfigVo.class);
        if (vo != null && entity != null && entity.getMsgType() != null) {
            vo.setMsgType(NoticeType.getEnumByIndex(entity.getMsgType().getValue()));
        }
        if (vo != null && entity != null && entity.getFunc() != null) {
            vo.setFunc(NotificationMethodEnum.getEnumByVal(entity.getFunc().getValue()));
        }
        if (vo != null && entity != null && entity.getRound() != null) {
            vo.setRound(NotificationCycleTypeEnum.getByValue(entity.getRound().getValue()));
        }
        if (vo != null && entity != null && entity.getRule() != null) {
            vo.setRule(NoticeRule.getByName(entity.getRule().getValue()));
        }
        if (entity.getMergeRule() != null) {
            vo.setMergeRule(ObjectConverter.convert(entity.getMergeRule(), MergeRuleVo.class));
        }
        vo.setCondition(FastjsonUtil.convertObject(entity.getCondition(), ConditionTree.class));
        return vo;
    }
}
