CREATE TABLE log_record_$tenantId (
                            id bigint AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
                            operator bigint NOT NULL COMMENT '操作人',
                            emp_id bigint COMMENT '员工Id',
                            operator_account varchar(50) COMMENT '操作账号',
                            operator_mobile varchar(50) COMMENT '操作手机号',
                            operator_email varchar(50) COMMENT '操作邮箱',
                            operator_name varchar(50) COMMENT '操作名称',
                            operator_source varchar(50) COMMENT '操作来源',
                            operator_platform varchar(50) COMMENT '操作平台',
                            uri varchar(255) COMMENT '请求地址uri',
                            menu varchar(255) COMMENT '菜单地址',
                            category varchar(255) COMMENT '操作类型',
                            action varchar(255) COMMENT '操作摘要',
                            detail text COMMENT '操作详情',
                            status boolean DEFAULT true COMMENT '操作状态',
                            create_time datetime DEFAULT now() COMMENT '操作时间',
                            is_deleted integer DEFAULT 0 COMMENT '是否删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT = '操作日志';