package com.caidaocloud.message.service.interfaces.vo.template;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.sdk.enums.DingtalkTemplateTypeEnum;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.service.application.common.enums.EmailCopyTarget;
import com.caidaocloud.message.service.domain.base.util.ObjectConvertUtil;
import com.caidaocloud.message.service.domain.template.entity.TemplateDo;
import com.caidaocloud.message.service.interfaces.dto.template.TemplateVariableDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@ApiModel("消息模版vo")
public class TemplateVo {

    private String bid;

    @ApiModelProperty("模版名称")
    private String name;

    @ApiModelProperty("模版名称多语言")
    private Map<String,Object> i18nName;

    @ApiModelProperty("模版内容")
    private String content;

    @ApiModelProperty("模板code不为空时，解析content填充参数")
    private Map<String, String> params;

    /**
     * 模版分类
     * 0 短信模版
     * 1 邮件模版
     * 2 系统模版
     * 3 APP模版
     */
    @ApiModelProperty("模版分类")
    private EnumSimple category;

    @ApiModelProperty("模版分类多语言")
    private Map<String,Object> i18nCategory;

    /**
     * 模版标题
     */
    @ApiModelProperty("模版标题")
    private String title;

    /**
     * 钉钉模版类型： 链接消息，文本消息，流程消息
     */
    @ApiModelProperty("模版类型")
    private DingtalkTemplateTypeEnum templateType;

    /**
     * 钉钉模版URL
     */
    @ApiModelProperty("钉钉模版URL")
    private String templateUrl;


    /**
     * 模版code
     */
    @ApiModelProperty("模版code")
    private String code;

    /**
     * 模版附件
     */
    @ApiModelProperty("模版附件")
    private Attachment attachFile;

    /**
     * 消息设置
     */
    @ApiModelProperty("消息设置")
    private String msgConfig;

    @ApiModelProperty("占位符")
    private List<TemplateVariableDto> variable;

    @ApiModelProperty("抄送对象\n" +
            "hrbp HRBP\n" +
            "leader 上级领导\n" +
            "orgLeader 部门负责人")
    private List<String> copyObject;

    @ApiModelProperty("抄送邮箱")
    private String copyMail;

    @ApiModelProperty("密送对象\n" +
            "hrbp HRBP\n" +
            "leader 上级领导\n" +
            "orgLeader 部门负责人")
    private List<String> blindCopyObject;

    @ApiModelProperty("密送邮箱")
    private String blindCopyMail;

    @ApiModelProperty("外部人员邮箱")
    private String externalMail;

    @ApiModelProperty("通知规则")
    private String noticeRule;

    public static List<TemplateVo> toVoList(List<TemplateDo> list) {
        return list.stream().map(data -> {
            TemplateVo vo = ObjectConvertUtil.convert(data, TemplateVo.class,(t1,v1)->{
                if (t1.getI18nName()!=null){
                    v1.setI18nName(FastjsonUtil.toObject(t1.getI18nName(),Map.class));
                }else {
                    Map<String,Object> map=new HashMap<>();
                    map.put("default",t1.getName());
                    v1.setI18nName(map);
                }

                v1.setI18nCategory(FastjsonUtil.toObject(t1.getI18nCategory(),Map.class));
            });
            vo.variable = FastjsonUtil.toArrayList(data.getVariable(), TemplateVariableDto.class);
            if (data.getCopyObject() != null) {
                List<String> copyTarget = FastjsonUtil.toObject(data.getCopyObject(), List.class);
                vo.copyObject = copyTarget;
            }
            if (data.getBlindCopyObject() != null) {
                List<String> blindCopyTarget = FastjsonUtil.toObject(data.getBlindCopyObject(), List.class);
                vo.blindCopyObject = blindCopyTarget;
            }
            if (null != data.getTemplateType() && StringUtils.isNotEmpty(data.getTemplateType().getValue())) {
                DingtalkTemplateTypeEnum templateType = DingtalkTemplateTypeEnum.getEnumByIndex(data.getTemplateType().getValue());
                vo.setTemplateType(templateType);
            }

            if (null != data.getNoticeRule()) {
                vo.setNoticeRule(data.getNoticeRule().getValue());
            }
            return vo;
        }).collect(Collectors.toList());
    }
}

