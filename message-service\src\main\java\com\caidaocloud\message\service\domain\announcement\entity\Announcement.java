package com.caidaocloud.message.service.domain.announcement.entity;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.service.domain.announcement.enums.AnnouncementStatus;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.Data;

@Data
public class Announcement extends DataSimple {

    private String name;

    private DictSimple type;

    private Long releaseTime;

    private Long effectiveTime;

    private Long expiryTime;

    private EnumSimple receiveType;

    private Boolean isTop = false;

    private EnumSimple status;

    @DisplayAsArray
    private List<String> domain;

    private AnnouncementContent content;

    private List<AnnouncementReceive> receiveList;

    public static String identifier = "entity.message.Announcement";

    public Announcement() {
        setIdentifier(identifier);
        setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        setCreateTime(System.currentTimeMillis());
        setCreateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    }

    public Announcement(String name, String type) {
        this();
        this.name=name;
        DictSimple dict = new DictSimple();
        dict.setValue(type);
        this.type=dict;
        this.status = AnnouncementStatus.UNPUBLISHED.toEnumSimple();
    }
}