package com.caidaocloud.message.test;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.feign.DingTalkFeignClient;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.infrastructure.utils.FileUtils;
import com.caidaocloud.oss.service.OssService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMediaUploadRequest;
import com.dingtalk.api.response.OapiMediaUploadResponse;
import com.taobao.api.ApiException;
import com.taobao.api.FileItem;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class OssTest {
    @Resource
    private OssService ossService;
    @Resource
    private DingTalkFeignClient dingTalkFeignClient;
    @Before
    public void initUser(){
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("8");
        userInfo.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);

        UserInfo user = new UserInfo();
        user.setTenantId(String.valueOf(8));
        user.doSetUserId(0L);
        UserContext.setCurrentUser(user);
    }

//    @Test
//    @SneakyThrows
//    public void ss(){
//        InputStream inputStream = ossService.getInputStream("/caidao-pa/259dtfgdb9cdb5ddab4b-jvm.png");
//        byte[] bytes = FileUtils.inputStreamToByteArray(inputStream);
//        String fileName = "jvm.png";
//
//        Map<String, String> ssoToken = dingTalkFeignClient.getDingTalkToken("dinguorrgiqy0v4gxtva", "ZRuKUtxYOF7IWtNKrL_r7FT3QKaQDtcp4hntKGoAOMlwsh4VzglGF4wyaR0ui_i5");
//        String accessToken = ssoToken.get("access_token");
//
//        MultipartFile multipartFile = new LocalMultipartFile(fileName, bytes, MediaType.MULTIPART_FORM_DATA_VALUE);
//
//        Map image = dingTalkFeignClient.uploadMedia(multipartFile, "image", accessToken);
//        System.out.println(image);
//    }

    @Test
    public void test() throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/media/upload");
        OapiMediaUploadRequest req = new OapiMediaUploadRequest();
        req.setType("image");
        // 要上传的媒体文件
        InputStream inputStream = ossService.getInputStream("/caidao-pa/259dtfgdb9cdb5ddab4b-jvm.png");
        byte[] bytes = FileUtils.inputStreamToByteArray(inputStream);

        FileItem item = new FileItem("jvm.png", bytes);
        req.setMedia(item);

        Map<String, String> ssoToken = dingTalkFeignClient.getDingTalkToken("dinguorrgiqy0v4gxtva", "ZRuKUtxYOF7IWtNKrL_r7FT3QKaQDtcp4hntKGoAOMlwsh4VzglGF4wyaR0ui_i5");
        String accessToken = ssoToken.get("access_token");

        OapiMediaUploadResponse rsp = client.execute(req, accessToken);
        System.out.println(rsp.getBody());

    }


}
