package com.caidaocloud.record.core.configure;

import com.caidaocloud.cache.CacheAutoConfiguration;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.mq.rabbitmq.MqAutoConfiguration;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.record.core.annotation.EnableLogRecord;
import com.caidaocloud.record.core.feign.RecordFeign;
import com.caidaocloud.record.core.message.LogRecordMessage;
import com.caidaocloud.record.core.service.*;
import com.caidaocloud.record.core.service.impl.*;
import com.caidaocloud.record.core.support.BeanFactoryLogRecordAdvisor;
import com.caidaocloud.record.core.support.LogRecordInterceptor;
import com.caidaocloud.record.core.support.LogRecordOperationSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.*;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.ClassUtils;

import java.util.List;


@Slf4j
@Configuration
@EnableFeignClients(basePackages = "com.caidaocloud.record.core.feign")
@EnableConfigurationProperties(LogRecordProperties.class)
@Import({CacheAutoConfiguration.class, MqAutoConfiguration.class})
@ConditionalOnProperty(name = "log.record.enable", havingValue = "true", matchIfMissing = true)
public class LogRecordProxyAutoConfiguration implements ImportAware {

    private AnnotationAttributes enableLogRecord;

    @Autowired
    private LogRecordProperties logRecordProperties;

    @Bean
    @Role(BeanDefinition.ROLE_INFRASTRUCTURE)
    public LogRecordOperationSource logRecordOperationSource() {
        return new LogRecordOperationSource();
    }

    @Bean
    @ConditionalOnMissingBean(IFunctionService.class)
    @ConditionalOnExpression
    public IFunctionService parseFunctionService(ParseFunctionFactory parseFunctionFactory) {
        return new DefaultFunctionServiceImpl(parseFunctionFactory);
    }

    @Bean
    public ParseFunctionFactory parseFunctionFactory(@Autowired List<IParseFunction> parseFunctions) {
        return new ParseFunctionFactory(parseFunctions);
    }

    @Bean
    @ConditionalOnMissingBean(IParseFunction.class)
    public DefaultParseFunction parseFunction() {
        return new DefaultParseFunction();
    }


	@Bean
	@ConditionalOnMissingBean(IOperatorGetService.class)
	public IOperatorGetService operatorGetService() {
		return new DefaultOperatorGetServiceImpl();
	}

    @Bean
    @ConditionalOnMissingBean(IUrlResourceService.class)
    public IUrlResourceService filterUrlGetService() {
        return new DefaultUrlResourceService();
    }

    public AsyncTaskExecutor recordTaskExecutor() {
        ThreadPoolTaskExecutor asyncTaskExecutor = new ThreadPoolTaskExecutor();
        asyncTaskExecutor.setMaxPoolSize(50);
        asyncTaskExecutor.setCorePoolSize(10);
        asyncTaskExecutor.setThreadNamePrefix("msg-record-thread-pool-");
        asyncTaskExecutor.initialize();
        return asyncTaskExecutor;
    }

    @Bean
    @ConditionalOnMissingBean(IDispatchRecordService.class)
    @ConditionalOnProperty(value = "log.record.dispatch", havingValue = "rabbitmq")
    public IDispatchRecordService msgDispatch(MqMessageProducer<LogRecordMessage> producer, CacheService cacheService) {
        return new DefaultDispatchServiceImpl(producer, cacheService, null, recordTaskExecutor());
    }

    @Bean
    @ConditionalOnMissingBean(IDispatchRecordService.class)
    @ConditionalOnProperty(value = "log.record.dispatch", havingValue = "feign", matchIfMissing = true)
    public IDispatchRecordService feignDispatch(CacheService cacheService, RecordFeign recordFeign) {
        return new DefaultDispatchServiceImpl(null, cacheService, recordFeign, recordTaskExecutor());
    }


    @Bean
    @Role(BeanDefinition.ROLE_INFRASTRUCTURE)
    public BeanFactoryLogRecordAdvisor logRecordAdvisor(IFunctionService functionService,
                                                        IOperatorGetService operatorGetService,
                                                        IDispatchRecordService dispatchRecordService,
                                                        IUrlResourceService resourceService) {
        BeanFactoryLogRecordAdvisor advisor = new BeanFactoryLogRecordAdvisor();
        advisor.setLogRecordOperationSource(logRecordOperationSource());
        advisor.setAdvice(logRecordInterceptor(functionService, operatorGetService, dispatchRecordService, resourceService));
        return advisor;
    }


    @Bean
    @Role(BeanDefinition.ROLE_INFRASTRUCTURE)
    public LogRecordInterceptor logRecordInterceptor(IFunctionService functionService, IOperatorGetService operatorGetService,
                                                     IDispatchRecordService dispatchRecordService, IUrlResourceService urlResourceService) {
        LogRecordInterceptor interceptor = new LogRecordInterceptor();
        interceptor.setLogRecordOperationSource(logRecordOperationSource());
        interceptor.setFunctionService(functionService);
		interceptor.setOperatorGetService(operatorGetService);
		interceptor.setDispatchLogService(dispatchRecordService);
        interceptor.setResourceService(urlResourceService);
        interceptor.setServiceName(enableLogRecord != null ?
                enableLogRecord.getString("name") : "");
        return interceptor;
    }

    @Override
    public void setImportMetadata(AnnotationMetadata importMetadata) {
        this.enableLogRecord = AnnotationAttributes.fromMap(
                importMetadata.getAnnotationAttributes(EnableLogRecord.class.getName(), false));
        if (this.enableLogRecord == null) {
            log.info("@EnableCaching is not present on importing class");
            return;
        }
        IUrlResourceService resourceService = filterUrlGetService();
        String packageName = ClassUtils.getPackageName(importMetadata.getClassName());
        resourceService.registerFilterUrl(packageName, logRecordProperties);
    }
}
