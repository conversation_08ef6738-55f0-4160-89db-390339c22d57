package com.caidaocloud.record.core.ext.parse;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 默认数据类型解析
 * created by: FoAng
 * create time: 16/5/2024 3:55 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class DefaultTypeParseService extends AbsDataTypeParse {

	@Override
	String parseComponentSimpleData(Object field, Class<?> componentClass) {
		return null;
	}
}
