package com.caidaocloud.message.test;

import com.google.common.collect.Lists;
import com.vladsch.flexmark.html2md.converter.FlexmarkHtmlConverter;

import java.util.ArrayList;
import java.util.List;

public class MainTest {
    public static void main(String[] args) {
        String htmlInput = "<html>123123123123 && dsdfsdf 9966  \n  669999995</html>";
        FlexmarkHtmlConverter converter = FlexmarkHtmlConverter.builder().build();
        System.out.println(htmlInput);
        //htmlInput = htmlInput.replaceAll("&&", "  \n  ");
        System.out.println(htmlInput);
        htmlInput = converter.convert(htmlInput);
        System.out.println(htmlInput);
        htmlInput = htmlInput.replaceAll("\\\\&\\\\&", "  \n  ");
        System.out.println(htmlInput);

        List<String> testList = new ArrayList<>();
        List<String> list = Lists.newArrayList("abc", "1", "2", "3", "4", "hellao");
        list.stream().filter(detail -> detail.contains("a"))
                // .collect(Collectors.toList())
                .forEach(detail -> testList.add(detail));

        testList.forEach(System.out::println);

        testList.clear();
        list.stream().filter(detail -> detail.contains(""))
                // .collect(Collectors.toList())
                .forEach(detail -> testList.add(detail));
        testList.forEach(System.out::println);
    }
}
