package com.caidaocloud.message.service.application.message.send.sms;

import com.caidaocloud.util.PropUtil;

/**
 * 短信消息通知工厂
 *
 * <AUTHOR>
 * @date 2022/6/13
 **/
public class SmsMessageNotifyFactory {
    public static volatile String smsType = null;

    public static SmsMessageNotify getSmsService(String msgType) {
        SmsMessageNotify messageNotify = SmsMessageNotify.messageManager.get(msgType);
        return messageNotify;
    }

    public static SmsMessageNotify getSmsService() {
        initSmsType();
        SmsMessageNotify messageNotify = SmsMessageNotify.messageManager.get(smsType);
        return messageNotify;
    }

    private static void initSmsType(){
        if(null != smsType){
            return;
        }

        synchronized (SmsMessageNotifyFactory.class){
            if(null == smsType){
                smsType = PropUtil.getProp("caidaocloud.msg.sms.type");
            }
            if(null == smsType){
                smsType = "adapter";
            }
        }
    }

}
