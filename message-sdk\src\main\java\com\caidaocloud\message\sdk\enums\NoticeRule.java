package com.caidaocloud.message.sdk.enums;

/**
 * 消息通知规则
 * 入职日期、合同结束日期、转正日期暂时不显示
 */
public enum NoticeRule {
    // 发送时间
    SEND_TIME("发送时间"),
    // 取任职信息模型的预计离职日期字段
    EXPECTED_RESIGN_DATE("离职日期"),
    EMPLOYMENT_DATE("入职日期"),
    // 合同结束日期
    CONTRACT_END_DATE("合同结束日期"),
    /* // 入职日期
     HIRE_DATE("入职日期"),*/
    // 转正日期
    CONFIRMATION_DATE("转正日期");

    public final String text;

    NoticeRule(String text) {
        this.text = text;
    }

    public static NoticeRule getByName(String value) {
        if (value == null) {
            return null;
        }
        for (NoticeRule e : values()) {
            if (e.name().equals(value)) {
                return e;
            }
        }
        return null;
    }
}
