package com.caidaocloud.message.service.application.msg.event;

import com.caidaocloud.msg.message.AbstractBasicMessage;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ContractEventEntity extends AbstractBasicMessage {
    private String tenantId;
//    private String eventEmpId;
    private String msgConfig;
    private Long userId;
    private List<String> subjects;
    // 通知对象类型; 0:员工；1:候选人；2:外部人员; 3: 角色
    private int type = 0;
    private String mdc;
    private long createTime;
    /**
     * 其他字段信息
     */
    private Map<String, String> ext;

    /**
     * 消息来源
     * 用于追踪消息来源于某个业务或模块
     */
    private String msgFrom;
}
