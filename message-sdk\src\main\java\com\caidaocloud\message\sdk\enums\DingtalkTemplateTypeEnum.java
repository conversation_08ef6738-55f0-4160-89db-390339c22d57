package com.caidaocloud.message.sdk.enums;

import com.caidaocloud.util.StringUtil;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/2/8 上午11:35
 * @Version 1.0
 **/
public enum DingtalkTemplateTypeEnum {


    /**
     * 工作通知以"MESSAGE"作为后缀，前端以此作为区分
     */
    LINK_MESSAGE("1", "工作通知--链接消息", "link_message"),
    TEXT_MESSAGE("2", "工作通知--文本消息", "text_message"),
    PROCESS_MESSAGE("3", "工作通知--流程消息", "process_message"),
    MARKDOWN_MESSAGE("4", "工作通知--markdown消息", "markdown_message"),
    TODO_NOTICE("5", "待办通知", "todo_notice"),
    ACTION_CARD_MESSAGE("6", "工作通知--卡片消息", "action_card_message");


    private String index;
    private String name;
    private String enName;

    DingtalkTemplateTypeEnum(String index, String name, String enName) {
        this.index = index;
        this.name = name;
        this.enName = enName;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }


    public static DingtalkTemplateTypeEnum getEnumByIndex(String value) {
        if (StringUtil.isBlank(value)) {
            return null;
        }

        DingtalkTemplateTypeEnum[] values = DingtalkTemplateTypeEnum.values();
        for (DingtalkTemplateTypeEnum templateType : values) {
            if (templateType.index.equals(value)) {
                return templateType;
            }
        }
        return null;
    }
}
