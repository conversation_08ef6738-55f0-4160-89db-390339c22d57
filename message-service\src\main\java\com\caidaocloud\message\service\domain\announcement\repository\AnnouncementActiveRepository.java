package com.caidaocloud.message.service.domain.announcement.repository;

import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementActive;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;

import java.util.List;

/**
 * 已生效公告Repository接口
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
public interface AnnouncementActiveRepository extends BaseRepository<AnnouncementActive> {
    
    /**
     * 根据原公告ID查询已生效公告
     * @param originalAnnouncementId 原公告ID
     * @return 已生效公告
     */
    AnnouncementActive findByOriginalAnnouncementId(String originalAnnouncementId);
    
    /**
     * 根据原公告ID删除已生效公告
     * @param originalAnnouncementId 原公告ID
     * @return 删除数量
     */
    int deleteByOriginalAnnouncementId(String originalAnnouncementId);
    
    /**
     * 查询已过期的生效公告
     * @param currentTime 当前时间
     * @return 已过期的生效公告列表
     */
    List<AnnouncementActive> findExpiredActiveAnnouncements(Long currentTime);
    
    /**
     * 批量删除已生效公告
     * @param announcementIds 公告ID列表
     * @return 删除数量
     */
    int batchDeleteByOriginalIds(List<String> announcementIds);
    
}
