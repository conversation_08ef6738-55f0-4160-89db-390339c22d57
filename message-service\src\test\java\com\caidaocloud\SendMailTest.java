package com.caidaocloud;

import com.caidaocloud.message.service.MessageApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class SendMailTest {
    @Resource
    private JavaMailSender javaMailSender;

    @Test
    public void testHtmlText(){
        // 邮件内容
        String mailSubject = "简单背景邮件主题background";
        String rawContent ="<html><body>" +
            "<div style='text-align:left'>{0}，您好！</div>" +
            "<div style='text-indent: 2em'>今天是{1}，祝您生活愉快。</div>" +
            "<img src='cid:pictureID'>" +
            "<div style='text-align:right'>--测试邮件</div>" +
            "</body></html>";

        rawContent ="<html><body>" +
                "<div style='text-align:left'>{0}，您好！</div>" +
                "<div style='text-indent: 2em'>今天是{1}，祝您生活愉快。</div>" +
                "<img src='https://camo.githubusercontent.com/30cf680ccf90e67fe006d5326a246bcc619fb62ff8c35dcc2bb0782499b069f9/68747470733a2f2f63646e2e6e6c61726b2e636f6d2f6c61726b2f302f323031382f706e672f35343331392f313533323331353935313831392d39666664393539652d303534372d346636312d386630362d3931333734636665376632312e706e67'>" +
                "<div style='text-align:right'>--测试邮件</div>" +
                "</body></html>";

        // QQ 邮箱不支持 body 背景图
        rawContent ="<html><body><div style=\"background: url('https://img-blog.csdnimg.cn/img_convert/264758fcf6a9ad834f02b5d0c3440480.png') no-repeat\"><div style=text-align:left>涛哥，您好！</div><div style=text-indent: 2em>今天是2022年09月06日，祝您生活愉快。</div><div style=text-align:right>--测试邮件</div></div></body></html>";

        String userName = "涛哥";
        String dateStr = new SimpleDateFormat("yyyy年MM月dd日").format(new Date());
        String content= MessageFormat.format(rawContent, userName, dateStr);
        log.info(content);
        try {
            // 邮件发送
//            MimeMessage message= javaMailSender.createMimeMessage();
//            MimeMessageHelper helper = new MimeMessageHelper(message, true, "utf-8");
//            // 发信人
//            helper.setFrom("<EMAIL>");
//            // 收信人
//            helper.setTo("<EMAIL>");
//            // 主题
//            helper.setSubject(mailSubject);
//            // 内容
//            helper.setText(content, true);
//
//            //helper.addInline("pictureID", new File("/Users/<USER>/Downloads/产品开发流程.png"));
//            // 加入附件
//            helper.addAttachment("失败数据-2023-01-13 14_34_50.xlsx", new File("/Users/<USER>/Downloads/失败数据-2023-01-13 14_34_50.xlsx"));
//
//            // 发送
//            javaMailSender.send(message);
            log.info("---------------");
        } catch (Exception e){
            log.error("testText err,{}", e.getMessage(), e);
        }

    }

    @Test
    public void testText(){
        // 邮件内容
        String mailSubject = "简单邮件主题";
        String rawContent = "{0}，您好！今天是{1}，祝您生活愉快。--测试邮件";
        String userName = "涛哥";
        String dateStr = new SimpleDateFormat("yyyy年MM月dd日").format(new Date());
        String content= MessageFormat.format(rawContent, userName, dateStr);
        try {
            // 邮件发送
            MimeMessage message= javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "utf-8");
            // 发信人
            helper.setFrom("<EMAIL>");
            // 收信人
            helper.setTo("<EMAIL>");
            // 主题
            helper.setSubject(mailSubject);
            // 内容
            helper.setText(content);
            // 发送
            javaMailSender.send(message);
        } catch (Exception e){
            log.error("testText err,{}", e.getMessage(), e);
        }

    }

    @Test
    public void testJmsHtmlText(){
        JavaMailSenderImpl jms = new JavaMailSenderImpl();
        jms.setHost("smtp.qiye.aliyun.com");

        jms.setUsername("<EMAIL>");
        jms.setPassword("pass@word1");
        jms.setProtocol("smtp");
        jms.setDefaultEncoding("UTF-8");

        // 邮件内容
        String mailSubject = "你好的";
        String rawContent ="<html><body>" +
                "<div style='text-align:left'>{0}，您好！</div>" +
                "<div style='text-indent: 2em'>今天是{1}，祝您生活愉快。</div>" +
                "<img src='cid:pictureID'>" +
                "<div style='text-align:right'>--测试邮件</div>" +
                "</body></html>";

        rawContent ="<html><body>" +
                "<div style='text-align:left'>{0}，您好！</div>" +
                "<div style='text-indent: 2em'>今天是{1}，祝您生活愉快。</div>" +
                "<img src='https://camo.githubusercontent.com/30cf680ccf90e67fe006d5326a246bcc619fb62ff8c35dcc2bb0782499b069f9/68747470733a2f2f63646e2e6e6c61726b2e636f6d2f6c61726b2f302f323031382f706e672f35343331392f313533323331353935313831392d39666664393539652d303534372d346636312d386630362d3931333734636665376632312e706e67'>" +
                "<div style='text-align:right'>--测试邮件</div>" +
                "</body></html>";

        // QQ 邮箱不支持 body 背景图
        rawContent ="<html><body><div style=\"background: url('https://img-blog.csdnimg.cn/img_convert/264758fcf6a9ad834f02b5d0c3440480.png') no-repeat\"><div style=text-align:left>涛哥，您好！</div><div style=text-indent: 2em>今天是2022年09月06日，祝您生活愉快。</div><div style=text-align:right>--测试邮件</div></div></body></html>";

        String userName = "涛哥";
        String dateStr = new SimpleDateFormat("yyyy年MM月dd日").format(new Date());
        String content= MessageFormat.format(rawContent, userName, dateStr);
        log.info(content);
        try {
            // 邮件发送
//            MimeMessage message= jms.createMimeMessage();
//            MimeMessageHelper helper = new MimeMessageHelper(message, true, "utf-8");
//            // 发信人
//            helper.setFrom("<EMAIL>");
//            // 收信人
//            helper.setTo("<EMAIL>");
//            // 主题
//            helper.setSubject(mailSubject);
//            // 内容
//            helper.setText(content, true);
//
//            //helper.addInline("pictureID", new File("/Users/<USER>/Downloads/产品开发流程.png"));
//            // 加入附件
//            helper.addAttachment("失败数据-2023-01-13 14_34_50.xlsx", new File("/Users/<USER>/Downloads/失败数据-2023-01-13 14_34_50.xlsx"));
//
//            // 发送
//            jms.send(message);
            log.info("---------------");
        } catch (Exception e){
            log.error("testText err,{}", e.getMessage(), e);
        }

    }

}
