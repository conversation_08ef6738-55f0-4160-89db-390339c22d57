package com.caidaocloud.message.service.interfaces.facade;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.message.service.application.common.constant.LangCodeConstant;
import com.caidaocloud.message.service.application.msg.dto.EmailMsgDto;
import com.caidaocloud.message.service.application.msg.dto.MsgDto;
import com.caidaocloud.message.service.application.msg.service.EmailConfigService;
import com.caidaocloud.message.service.application.msg.service.MsgService;
import com.caidaocloud.message.service.domain.msg.entity.EmailConfigDo;
import com.caidaocloud.message.service.infrastructure.utils.LangUtil;
import com.caidaocloud.message.service.interfaces.dto.msg.EmailConfigDto;
import com.caidaocloud.message.service.interfaces.vo.msg.EmailConfigVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/6 下午6:06
 */
@Slf4j
@RestController
@RequestMapping("/api/msg/emailconfig/v1")
@Api(value = "/api/msg/emailconfig/v1", description = "邮箱设置", tags = "v0.1")
public class EmailConfigController {
    @Resource
    private EmailConfigService emailConfigService;
    @Resource
    private MsgService msgService;

    @GetMapping("/detail")
    @ApiOperation("邮箱设置详情")
    public Result<EmailConfigVo> getDetail(){
        List<EmailConfigDo> configs = emailConfigService.selectList();
        if(null == configs || configs.isEmpty()){
            return Result.ok(new EmailConfigVo());
        }

        EmailConfigVo vo = ObjectConverter.convert(configs.get(0), EmailConfigVo.class);
        return Result.ok(vo);
    }

    @PostMapping("/save")
    @ApiOperation("邮箱设置保存")
    public Result<EmailConfigVo> save(@RequestBody EmailConfigDto config){
        checkConfig(config);
        EmailConfigVo vo = ObjectConverter.convert(emailConfigService.saveEmailConfig(config), EmailConfigVo.class);
        return Result.ok(vo);
    }

    @PostMapping("/sendTestEmail")
    @ApiOperation("测试邮箱设置")
    public Result sendTestEmail(@RequestBody EmailConfigDto config){
        checkConfig(config);
        String lang = LangUtil.getMsg(LangCodeConstant.code_70000);
        String msg = String.format(lang, LangUtil.getMsg(LangCodeConstant.code_70005));
        PreCheck.preCheckArgument(StringUtil.isBlank(config.getTestMail()), msg);
        boolean result = emailConfigService.sendTestEmail(config);
        return result ? Result.ok() : Result.fail();
    }

    @PostMapping("/sendMail")
    public Result sendEmail(@RequestBody Map<String,String> map) {
        String lang = LangUtil.getMsg(LangCodeConstant.code_70000);
        String msg = String.format(lang, LangUtil.getMsg(LangCodeConstant.code_70005));
        PreCheck.preCheckArgument(StringUtil.isBlank(map.get("to")), msg);
        EmailMsgDto emailMsgDto = new EmailMsgDto();
        emailMsgDto.setSubject(map.get("subject"));
        emailMsgDto.setContent(map.get("content"));
        emailMsgDto.setTo(map.get("to"));
        MsgDto msgDto = new MsgDto();
        msgDto.setTenantId(map.get("tenantId"));
        msgDto.setEmailMsg(emailMsgDto);
        msgService.sendEmail(msgDto);
        return Result.ok();
    }


    private void checkConfig(EmailConfigDto config){
        // 不启用邮件
        if (config.getStatus() != null && "0".equals(config.getStatus().getValue())) {
            return;
        }

        if(null != config.getEmailModel() && "1".equals(config.getEmailModel().getValue())){
            // 适配模式无需校验
            return;
        }

        String lang = LangUtil.getMsg(LangCodeConstant.code_70000);
        String msg = String.format(lang, LangUtil.getMsg(LangCodeConstant.code_70001));
        PreCheck.preCheckArgument(StringUtil.isBlank(config.getEmailSmtp()), msg);

        msg = String.format(lang, LangUtil.getMsg(LangCodeConstant.code_70002));
        PreCheck.preCheckArgument(StringUtil.isBlank(config.getEmailFrom()), msg);

        msg = String.format(lang, LangUtil.getMsg(LangCodeConstant.code_70003));
        PreCheck.preCheckArgument(StringUtil.isBlank(config.getEmailPwd()), msg);

        msg = String.format(lang, LangUtil.getMsg(LangCodeConstant.code_70004));
        PreCheck.preCheckArgument(StringUtil.isBlank(config.getEmailNick()), msg);
    }
}
