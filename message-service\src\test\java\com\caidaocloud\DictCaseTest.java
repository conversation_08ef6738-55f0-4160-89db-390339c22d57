package com.caidaocloud;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;

public class DictCaseTest {
    public static void main(String[] args) {
        DataSimple dataSimple = new DataSimple();
        // 定义Parser，可以定义全局的parser
        ExpressionParser parser = new SpelExpressionParser();
        String value = "byHr";
        String code = "if(\"byHr\".equals(value)){" +
                "    dataSimple.getProperties().add(\"a\", \"b\");" +
                "    System.out.println(\"----1\");" +
                "} else if(\"byUser\".equals(value)){" +
                "    dataSimple.getProperties().add(\"dict\", \"user\");" +
                "    System.out.println(\"----2\");" +
                "}" +
                "System.out.println(\"----3\");";
        String bc = parser.parseExpression("'abc'.substring(1, 3)").getValue(String.class);
        parser.parseExpression(code);
        /*if("byHr".equals(value)){
            dataSimple.getProperties().add("a", "b");
            System.out.println("----1");
        } else if("byUser".equals(value)){
            dataSimple.getProperties().add("dict", "user");
            System.out.println("----2");
        }
        System.out.println("----3");*/
        System.out.println("------");
    }
}
