package com.caidaocloud.message.service.application.message.send.sms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.caidaocloud.message.service.application.common.enums.MsgType;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import com.caidaocloud.message.service.application.log.dto.MessageLogDto;
import com.caidaocloud.message.service.application.log.repository.IMessageLogRepository;
import com.caidaocloud.message.service.application.sms.dto.SmsMessageDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AliyunSmsMessageNotify implements SmsMessageNotify<Object>{

    @Value("${aliyun.api.sms.regionId:cn-hangzhou}")
    private String regionId;

    @Value("${aliyun.api.sms.accessKeyId:}")
    private String accessKeyId;

    @Value("${aliyun.api.sms.accessSecret:}")
    private String accessSecret;

    @Value("${aliyun.api.sms.mns.signName:}")
    private String signName;
    @Resource
    private IMessageLogRepository messageLogRepository;
    //private String templateCode= "SMS_154950909";//模板编号（这个是阿里云测试模板的编号）
    @Override
    public void sendMessage(SmsMessageDto messageDto) {
        log.info("Send SMS request entity={}", FastjsonUtil.toJson(messageDto));
        MessageLogDto messageLogDto = new MessageLogDto();
        try {

            DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessSecret);
            IAcsClient client = new DefaultAcsClient(profile);
            CommonRequest request = new CommonRequest();
            request.setSysMethod(MethodType.POST);
            request.setSysDomain("dysmsapi.aliyuncs.com");
            request.setSysAction("SendSms");
            request.setSysVersion("2017-05-25");

            // 多个手机号逗号分隔
            String phoneNumbers = messageDto.getMobile().stream()
                    .filter(m -> StringUtil.isNotEmpty(m.getMobile()))
                    .map(m -> m.getMobile()).collect(Collectors.joining(","));

            request.putQueryParameter("PhoneNumbers", phoneNumbers);
            request.putQueryParameter("SignName", signName);
            request.putQueryParameter("TemplateCode", messageDto.getMsgCode());
            // 封装变量
            String tpJson = FastjsonUtil.toJson(getParams(messageDto.getContent()));
            request.putQueryParameter("TemplateParam", tpJson);
            messageLogDto.setType(MsgType.MESSAGE);

            messageLogDto.setReceiver(phoneNumbers);
            messageLogDto.setMessage(FastjsonUtil.toJson(messageDto));
            messageLogDto.setStatus(SendStatusEnum.SUCESS);
            String bid = messageLogRepository.saveOrUpdateOfLog(messageLogDto);
            messageLogDto.setBid(bid);
            CommonResponse response = client.getCommonResponse(request);
            JSONObject result = JSON.parseObject(response.getData());
            Boolean sendResult= result.containsKey("Code") && result.get("Code").equals("OK");
            if(!sendResult){
                messageLogDto.setErrorMsg(result.get("Message").toString());
                messageLogDto.setStatus(SendStatusEnum.FAIL);
                messageLogRepository.saveOrUpdateOfLog(messageLogDto);
            }
            log.info("Results of SMS sending={}", response.getData());
        } catch (Exception e) {
            log.error("doSendMessage:{}", e.getMessage(), e);
            messageLogDto.setErrorMsg(e.getMessage());
            messageLogDto.setStatus(SendStatusEnum.FAIL);
            messageLogRepository.saveOrUpdateOfLog(messageLogDto);
        }
    }

    private Map<String, String> getParams(String input){
        Map<String, String> params = new HashMap<>();
        if(StringUtil.isEmpty(input)){
            return params;
        }
        Pattern pattern = Pattern.compile("\\$(.*?)\\$");
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            String group = matcher.group(1);
            if(null != group){
                String [] split = group.split(":");
                params.put(split[0], split.length > 1 ? split[1] : "");
            }
        }
        return params;
    }

    @Override
    public String getSignType() {
        return "aliyun";
    }
}
