package com.caidaocloud.message.service.record.provider.es;

import com.caidaocloud.message.service.record.util.UserContextUtil;
import org.springframework.stereotype.Component;

/**
 * created by: FoAng
 * create time: 28/5/2024 9:54 上午
 */
@Component
public class DynamicIndexProvider {

    public String getTenantIndex(String prefix) {
        String tenantId = UserContextUtil.getTenantId();
        return String.format("%s_%s", prefix, tenantId);
    }

}
