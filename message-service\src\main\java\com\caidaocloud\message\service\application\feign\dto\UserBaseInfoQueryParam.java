package com.caidaocloud.message.service.application.feign.dto;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("用户信息查询参数")
public class UserBaseInfoQueryParam extends BasePage {


    @ApiModelProperty("手机号")
    private List<String> mobiles;

    @ApiModelProperty("邮箱")
    private List<String> emails;


    @ApiModelProperty("用户状态：1 正常 2 停用 3 锁定")
    private List<Integer> status;

    @ApiModelProperty("删除状态 0 未删除 1 已删除")
    private Integer deleted;


}
