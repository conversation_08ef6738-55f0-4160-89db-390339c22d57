package com.caidaocloud.message.service.interfaces.vo.msg;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * EmailConfigVo
 *
 * <AUTHOR>
 * @date 2022/6/7 上午9:46
 */
@Data
@ApiModel("邮箱配置vo")
public class EmailConfigVo {
    @ApiModelProperty("是否启用")
    private EnumSimple status;

    /**
     * 邮箱模式
     * 0 smtp 常规模式
     * 1 adapter api 适配模式
     */
    @ApiModelProperty("邮箱模式")
    private EnumSimple emailModel;

    /**
     * SMTP服务器
     */
    @ApiModelProperty("SMTP服务器")
    private String emailSmtp;

    /**
     * 端口
     */
    @ApiModelProperty("端口")
    private Integer emailPort;

    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    private String emailFrom;

    /**
     * 发件人昵称
     */
    @ApiModelProperty("发件人昵称")
    private String emailNick;

    /**
     * 密码
     */
    @ApiModelProperty("密码")
    private String emailPwd;

    /**
     * 是否开启SSL
     */
    @ApiModelProperty("是否开启SSL")
    private Boolean enableSsl;
}
