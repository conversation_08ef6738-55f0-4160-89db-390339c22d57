package com.caidaocloud.message.service.infrastructure.repository.impl.template;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.message.service.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.message.service.domain.template.entity.TemplateDo;
import com.caidaocloud.message.service.domain.template.repository.TemplateRepository;
import com.caidaocloud.message.service.interfaces.dto.template.TemplateQueryDto;
import com.caidaocloud.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.caidaocloud.message.service.domain.template.entity.TemplateDo.DEFAULT_CONFIG;

@Repository
public class TemplateRepositoryImpl extends BaseRepositoryImpl<TemplateDo> implements TemplateRepository {
    @Override
    public PageResult<TemplateDo> selectPage(BasePage page, TemplateDo data) {
        DataFilter dataFilter = DataFilter.eq("tenantId", data.getTenantId())
                .andEq("deleted", Boolean.FALSE.toString())
                .andEq("msgConfig",DEFAULT_CONFIG);

        TemplateQueryDto query = (TemplateQueryDto) page;
        if(null != query.getCategory()){
            dataFilter = dataFilter.andEq("category", query.getCategory().getValue());
        }

        if(StringUtil.isNotEmpty(query.getName())){
            dataFilter = dataFilter.andRegex("name", query.getName());
        }

        if (CollectionUtils.isNotEmpty(query.getTemplateBidList())) {
            dataFilter.andIn("bid", query.getTemplateBidList());
        }

        return DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage().queryInvisible()
                .limit(page.getPageSize(), page.getPageNo()).filter(dataFilter, TemplateDo.class);
    }

    @Override
    public List<TemplateDo> selectList(TemplateDo data) {
        DataFilter dataFilter = getBaseFilter();
        if(null != data.getCategory()){
            dataFilter = dataFilter.andEq("category", data.getCategory().getValue());
        }
        if (data.getMsgConfig() != null) {
            dataFilter = dataFilter.andEq("msgConfig", data.getMsgConfig());
        }

        return DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1)
                .filter(dataFilter, TemplateDo.class).getItems();
    }

    @Override
    public void deleteByConfigId(String identifier, String configId) {
        DataFilter dataFilter = getBaseFilter().andEq("msgConfig", configId);
        DataDelete.identifier(identifier).batchDelete(dataFilter);
    }

    @Override
    public void batchInsert(String identifier, List<TemplateDo> list) {
        DataInsert.identifier(identifier).batchInsert(list);
    }

    @Override
    public long countName(String identifier, TemplateDo data) {
        DataFilter dataFilter = getBaseFilter().andEq("name", data.getName())
                .andEq("category", data.getCategory().getValue())
                .andEq("msgConfig", data.getMsgConfig());
        if (data.getBid() != null) {
            dataFilter = dataFilter.andNe("bid", data.getBid());
        }
        return DataQuery.identifier(identifier).queryInvisible().decrypt().specifyLanguage()
                .count(dataFilter, System.currentTimeMillis());
    }

}
