package com.caidaocloud.message.service.application.sms.service;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AliyunSmsNotifyService implements SmsNotifyService{
    @Override
    public String getMsgType() {
        return "aliyun";
    }

    @Override
    public boolean sendMsg(SmsMsgSender sender) {
        // todo
        return false;
    }

    @Override
    public boolean batchSendMsg(List<SmsMsgSender> senderList) {
        // todo
        return false;
    }
}
