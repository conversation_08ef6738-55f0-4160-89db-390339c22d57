package com.caidaocloud.message.service.record.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.service.record.dto.LogRecordQueryDto;
import com.caidaocloud.message.service.record.vo.LogRecordVo;
import com.caidaocloud.record.core.beans.LogRecordData;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * created by: FoAng
 * create time: 28/5/2024 4:07 下午
 */
public interface RecordService {

    void saveRecordData(LogRecordData data);

    List<LogRecordVo> listRecord(LogRecordQueryDto dto);

    PageResult<LogRecordVo> pageRecord(LogRecordQueryDto dto);

    void export(LogRecordQueryDto dto, HttpServletResponse response);
}
