package com.caidaocloud.message.sdk.enums;

import com.caidaocloud.util.StringUtil;
import lombok.Getter;

import java.util.Arrays;

/**
 * 消息类型
 * created by: FoAng
 * create time: 15/8/2024 10:57 上午
 */
@Getter
public enum NoticeMsgTypeEnum {

    TODO("待办", "TODO"),

    NOTICE("消息", "NOTICE");

    final String label;

    final String value;

    NoticeMsgTypeEnum(String label, String value) {
        this.label = label;
        this.value = value;
    }

    public static NoticeMsgTypeEnum of(String value) {
        return Arrays.stream(values()).filter(it -> StringUtil.isNotEmpty(value) && it.value.equals(value))
                .findFirst().orElse(NOTICE);
    }
}
