package com.caidaocloud.message.service.domain.msg.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.service.application.common.enums.MsgConfigStatusEnum;
import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.sdk.enums.NotificationMethodEnum;
import com.caidaocloud.message.service.application.msg.dto.MessageConfigDto;
import com.caidaocloud.message.service.application.msg.dto.MessageConfigPageQueryDto;
import com.caidaocloud.message.service.domain.base.entity.BaseDomainDo;
import com.caidaocloud.message.service.domain.base.service.BaseDomainServiceImpl;
import com.caidaocloud.message.service.domain.msg.entity.AssignEmpDo;
import com.caidaocloud.message.service.domain.msg.entity.MsgConfigDo;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.caidaocloud.message.service.application.common.enums.NoticeTarget.ASSIGN_EMP;

/**
 * MsgConfigDomainService
 *
 * <AUTHOR>
 * @date 2022/6/6 下午6:11
 */
@Service
public class MsgConfigDomainService extends BaseDomainServiceImpl<MsgConfigDo, BasePage> {
    @Resource
    private MsgConfigDo msgConfigDo;
    @Resource
    private AssignEmpDo assignEmpDo;

    @Override
    public BaseDomainDo<MsgConfigDo> getDoService() {
        return msgConfigDo;
    }

    public List<MessageConfigDto> getMsgConfigByType(NoticeType noticeType, MsgConfigStatusEnum status) {
        List<MsgConfigDo> list = msgConfigDo.getMsgConfigRepository().selectByMsgTypeAndStatus(noticeType, status);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(e -> {
            MessageConfigDto messageConfigDto = new MessageConfigDto();
            BeanUtils.copyProperties(e, messageConfigDto, "msgType", "notifyObject", "func", "round", "status");
            if (e.getMsgType() != null) {
                messageConfigDto.setMsgType(NoticeType.getEnumByIndex(e.getMsgType().getValue()));
            }

            if (e.getNotifyObject() != null) {
                List<String> notifyObject = FastjsonUtil.toObject(e.getNotifyObject(), List.class);
                messageConfigDto.setNotifyObject(notifyObject.stream().map(NoticeTarget::getEnumByIndex).collect(Collectors.toList()));
                // messageConfigDto.setNotifyObject(NoticeTarget.getEnumByIndex(e.getNotifyObject().getValue()));
            }

            if (e.getFunc() != null) {
                messageConfigDto.setFunc(NotificationMethodEnum.getEnumByVal(e.getFunc().getValue()));
            }

            if (e.getStatus() != null) {
                messageConfigDto.setStatus(MsgConfigStatusEnum.getEnumByVal(e.getStatus().getValue()));
            }

            return messageConfigDto;
        }).collect(Collectors.toList());
    }

    public List<MsgConfigDo> getMsgConfigByTypeAndIds(List<String> ids, NoticeType noticeType, MsgConfigStatusEnum status) {
        List<MsgConfigDo> list = msgConfigDo.getMsgConfigRepository().getMsgConfigByTypeAndIds(ids, noticeType, status);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        return list;
    }

    public List<MsgConfigDo> getEnableMsgConfigList(String noticeType) {
        List<MsgConfigDo> list = msgConfigDo.getMsgConfigRepository().getEnableMsgConfigList(noticeType);
        return getAndDefaultEmpty(list);
    }

    public List<MsgConfigDo> getOnboardingSelectList(List<String> types) {
        List<MsgConfigDo> list = msgConfigDo.getMsgConfigRepository().getOnboardingSelectList(types);
        return getAndDefaultEmpty(list);
    }

    private List<MsgConfigDo> getAndDefaultEmpty(List<MsgConfigDo> list){
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        return list;
    }

    public PageResult<MsgConfigDo> getEnableMsgConfigPageList(MessageConfigPageQueryDto queryDto) {
        return msgConfigDo.getMsgConfigRepository().selectEnableMsgConfigPageList(queryDto);
    }

    @Override
    public MsgConfigDo save(MsgConfigDo data) {
        setRequiredField(data, true);
        linkDataConvert(data);

        MsgConfigDo config = msgConfigDo.save(data);
        assignEmpDo.saveAssignEmp(config);
        return config;
    }

    @Override
    public MsgConfigDo updateById(MsgConfigDo data) {
        setRequiredField(data, false);
        linkDataConvert(data);
        msgConfigDo.updateById(data);
        assignEmpDo.saveAssignEmp(data);
        return data;
    }

    @Override
    public MsgConfigDo getById(String bid) {
        MsgConfigDo data = super.getById(bid);
        if (null == data || StringUtils.isEmpty(data.getBid())) {
            return data;
        }
        // 查询指定系统人员
        if (null != data.getNotifyObject() && data.getNotifyObject().contains(ASSIGN_EMP.toString())) {
            List<AssignEmpDo> empList = assignEmpDo.getByConfig(data);
            data.setEmps(empList.stream().map(AssignEmpDo::getEmp).collect(Collectors.toList()));
        }

        return data;
    }

    public void enable(String bid) {
        msgConfigDo.changeStatus(bid, MsgConfigStatusEnum.ENABLED);
    }

    public void disable(String bid) {
        msgConfigDo.changeStatus(bid, MsgConfigStatusEnum.DISABLED);
    }
}
