package com.caidaocloud.message.service.infrastructure.repository.impl.log;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.message.service.application.common.constant.LangCodeConstant;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import com.caidaocloud.message.service.application.log.dto.MessageLogDto;
import com.caidaocloud.message.service.application.log.repository.IMessageLogRepository;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.infrastructure.repository.po.MessageLogPo;
import com.caidaocloud.message.service.infrastructure.utils.LangUtil;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 发送消息日志
 *
 * <AUTHOR>
 * @date 2022/6/10
 **/
@Repository
public class MessageLogRepositoryImpl implements IMessageLogRepository {

    /**
     * paas模型定义
     */
    private final String LOG_IDENTIFIER = "entity.message.LogInfoMessage";

    @Autowired
    private ISessionService sessionService;

    @Override
    public String saveOrUpdateOfLog(MessageLogDto logDto) {
        if (logDto == null) {
            String lang = LangUtil.getMsg(LangCodeConstant.code_70000);
            String msg = String.format(lang, "parameter");
            throw new ServerException(msg);
        }
        long currentTimeMillis = System.currentTimeMillis();
        UserInfo userInfo = UserContext.getContextUser();
        MessageLogPo data = FastjsonUtil.convertObject(logDto, MessageLogPo.class);
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        String tenantId = null == userInfo || null == userInfo.getTenantId() ? null : userInfo.getTenantId();
        data.setTenantId(tenantId);
        data.setUpdateBy(userId);
        data.setUpdateTime(currentTimeMillis);
        data.setIdentifier(LOG_IDENTIFIER);
        data.setDeleted(Boolean.FALSE);
        if (logDto.getStatus() != null) {
            SendStatusEnum sendStatus = logDto.getStatus();
            EnumSimple status = new EnumSimple();
            status.setText(sendStatus.txt);
            status.setValue(sendStatus.status);
            data.setStatus(status);
        }
        if (data.getBid() == null) {
            data.setCreateTime(currentTimeMillis);
            String bid = DataInsert.identifier(LOG_IDENTIFIER).insert(data);
            data.setBid(bid);
        } else {
            DataUpdate.identifier(LOG_IDENTIFIER).update(data);
        }
        return data.getBid();
    }

}
