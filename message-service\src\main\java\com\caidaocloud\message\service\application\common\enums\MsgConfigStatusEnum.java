package com.caidaocloud.message.service.application.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 消息配置状态枚举
 *
 * <AUTHOR>
 * @date 2022/6/15
 **/
public enum MsgConfigStatusEnum {

    NOT_ENABLED("未启用", "0"),
    ENABLED("启动", "1"),
    DISABLED("停用", "2");

    public String txt;

    public String value;

    MsgConfigStatusEnum(String txt, String value) {
        this.txt = txt;
        this.value = value;
    }

    public static MsgConfigStatusEnum getEnumByVal(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }

        MsgConfigStatusEnum[] values = MsgConfigStatusEnum.values();
        for (MsgConfigStatusEnum msgConfigStatusEnum : values) {
            if (msgConfigStatusEnum.value.equals(value)) {
                return msgConfigStatusEnum;
            }
        }
        return null;
    }

}
