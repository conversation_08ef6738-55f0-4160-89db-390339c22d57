package com.caidaocloud.message.service.application.email.dto;

import lombok.Data;

/**
 * MailMsgSender
 *
 * <AUTHOR>
 * @date 2022/6/6 下午3:20
 */
@Data
public class MailMsgSender {
    // 租户
    private String tenantId;
    // 收件人
    private String to;
    // 邮件主题
    private String subject;
    // 邮件内容
    private String content;
    /**
     * 多个附件用逗号分割
     * 附件地址
     */
    private String affix;

    /**
     * 指定附件对应的名称，多个用逗号分割同affix一一对应，如果为空或对应下标上的值为空，则取文件名作为名称
     * 附件名称
     */
    private String affixName;

    // 抄送人
    private String cc;
    // 密送人
    private String bcc;

    // 批量邮件的批次id
    private String logEmailId;
    // 循环次数
    private int cycle = 0;
}
