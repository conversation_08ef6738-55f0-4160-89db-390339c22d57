package com.caidaocloud.message.service.application.message.send.handler;

import com.caidaocloud.message.sdk.enums.*;
import com.caidaocloud.message.service.application.message.send.dto.MessageContext;
import com.caidaocloud.message.service.application.msg.dto.NoticeMessageDto;
import com.caidaocloud.message.service.interfaces.vo.template.TemplateVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.groovy.util.Maps;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Map;

/**
 * 根据模版生产系统消息
 */
@Slf4j
@Service
public class SystemMsgHandler implements IMsgHandler {

    private static final Map<NoticeBusinessEnum, ArrayList<NoticeType>> noticeGroup = Maps.of(
            NoticeBusinessEnum.HR, Lists.newArrayList(),
            NoticeBusinessEnum.SYSTEM, Lists.newArrayList(),
            NoticeBusinessEnum.EVENT, Lists.newArrayList(),
            NoticeBusinessEnum.OTHER, Lists.newArrayList()
    );

    @Override
    public String getHandlerType() {
        return "2";
    }

    @Override
    public void doMsgHandler(MessageContext context) {
        TemplateVo templateVo = context.getTemplate();
        NoticeMessageDto noticeDto = new NoticeMessageDto();
        noticeDto.setTitle(templateVo.getTitle());
        noticeDto.setContent(templateVo.getContent());
        noticeDto.setContentType(ContentTypeEnum.PLAIN);
        noticeDto.setType(NoticeMsgTypeEnum.NOTICE);
        noticeDto.setAction(NoticeActionEnum.VIEW);
        noticeDto.setActionContent(null);
        noticeDto.setExt(context.getExt());
        noticeDto.setEmpIds(context.getEmpList());
        context.getMsgDto().setNoticeMsg(noticeDto);
    }

}
