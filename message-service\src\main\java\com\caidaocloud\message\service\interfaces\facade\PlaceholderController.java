package com.caidaocloud.message.service.interfaces.facade;

import java.util.List;

import com.caidaocloud.message.service.application.template.service.PlaceholderService;
import com.caidaocloud.message.service.interfaces.dto.template.PlaceholderDto;
import com.caidaocloud.message.service.interfaces.vo.template.PlaceholderItemVo;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/msg/placeholder/v1")
@Api(value = "/api/msg/placeholder/v1", description = "变量管理", tags = "v0.5")
public class PlaceholderController {
    @Resource
    private PlaceholderService placeholderService;

    @ApiOperation("新增")
    @PostMapping("/save")
    public Result save(@RequestBody PlaceholderDto dto) {
        placeholderService.saveOrUpdateObj(dto);
        return Result.ok(true);
    }

    @ApiOperation("新增")
    @PostMapping("/update")
    public Result update(@RequestBody PlaceholderDto dto) {
        placeholderService.saveOrUpdateObj(dto);
        return Result.ok(true);
    }

    @ApiOperation("列表")
    @GetMapping("/selectList")
    public Result<List<PlaceholderItemVo>> selectList(){
        return Result.ok(placeholderService.selectListData());
    }
}
