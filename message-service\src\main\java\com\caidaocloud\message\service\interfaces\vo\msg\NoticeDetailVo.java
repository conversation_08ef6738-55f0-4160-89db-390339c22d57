package com.caidaocloud.message.service.interfaces.vo.msg;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * created by: FoAng
 * create time: 20/8/2024 9:55 上午
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class NoticeDetailVo extends NoticeMessageVo {

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("业务ID")
    private String businessId;

    @ApiModelProperty("扩展字段")
    private Map<String, Object> ext;
}
