package com.caidaocloud.message.sdk.dto;

import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import com.caidaocloud.message.sdk.convert.ListStrDeserializer;
import com.caidaocloud.message.sdk.enums.NoticeRule;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.enums.NotificationCycleTypeEnum;
import com.caidaocloud.message.sdk.enums.NotificationMethodEnum;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Data
@Slf4j
public class MsgConfigDto {
    // 业务id
    private String bid;

    // 通知名称
    private String name;

    @ApiModelProperty("通知类型")
    private NoticeType msgType;

    // 通知对象
    // private List<NoticeTarget> notifyObject;

    // 匹配条件
    private ConditionTree condition;

    // 通知方式
    private NotificationMethodEnum func;

    // 通知天数
    private Integer day;

    // 手动推送通知日期
    private Long triggerDate;
    // 发送时间
    private Integer sendTime;

    // 通知周期
    private Integer loop;

    /**
     * 发送途径：1,2,3,4
     * 3：app 通知
     * 2：系统通知
     * 0：短信通知
     * 1：邮件通知
     * <p>
     * 多个发送途径，英文逗号分割
     */
    @ApiModelProperty("发送途径")
    @JsonDeserialize(using = ListStrDeserializer.class)
    private String channel;


    @ApiModelProperty("通知规则")
    private NoticeRule noticeRule;

    @ApiModelProperty("通知规则")
    private NoticeRule rule;

    /**
     * 循环通知，循环结束天数
     */
    private Integer loopDay;

    @ApiModelProperty("通知轮次")
    private String round;

    @ApiModelProperty("合并规则")
    private MergeRuleDto mergeRule;

    // 事件人过滤，false 不过滤，true 过滤
    private Boolean subjectFilter;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MsgConfigDto that = (MsgConfigDto) o;
        return Objects.equals(bid, that.bid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(bid);
    }

    public boolean checkMsgConfig(Long dataTime, long currentTimestamp) {
        MsgConfigDto msgConfig = this;
        if (dataTime == null) {
            return false;
        }
        long sendDate = currentTimestamp;
        NotificationCycleTypeEnum cycleType = NotificationCycleTypeEnum.getByName(msgConfig.getRound());
        switch (cycleType) {
        // 提前通知
        case ADVANCE:
            sendDate += TimeUnit.DAYS.toMillis(msgConfig.getDay());
            break;
        // 延后通知
        case DELAY:
            sendDate -= TimeUnit.DAYS.toMillis(msgConfig.getDay());
            break;
        case TODAY:
            break;
        default:
            log.warn("Unexpected value = {},msgId={}", cycleType, msgConfig.getBid());
            return false;
        }
        // 单次通知，判断结束日期 == 发送日期
        switch (msgConfig.getFunc()) {
        case ONCE:
            return sendDate == dataTime;
        // 循环通知
        case CYCLE:
            // 循环通知到 loopDay 天
            if (msgConfig.getLoopDay() != null && sendDate > dataTime + TimeUnit.DAYS.toMillis(msgConfig.getLoopDay())) {
                return false;
            }

            long diff = TimeUnit.MILLISECONDS.toDays(sendDate - dataTime);
            return diff >= 0 && diff % msgConfig.getLoop() == 0;
        //	合并发送
        case MERGE:
            // 合并发送时间范围
            long endDate = sendDate + TimeUnit.DAYS.toMillis(msgConfig.getMergeRule()
                    .getMergePeriod());
            return sendDate <= dataTime && dataTime <= endDate;
        default:
            return false;
        }
    }
}
