package com.caidaocloud.message.service.record.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * created by: FoAng
 * create time: 22/5/2024 5:32 下午
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "caidaocloud.log.record")
public class LogProviderProperties {

    /**
     * 日志存储方式：db、es
     */
    private String provider = "es";

    /**
     * 数据库类型
     */
    private String dbType;

    /**
     * 数据库schema
     */
    private String schema;

}
