package com.caidaocloud.message.service.domain.notice.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.sdk.enums.NoticeBusinessEnum;
import com.caidaocloud.message.service.application.notice.dto.NoticeMsgDto;
import com.caidaocloud.message.service.domain.base.entity.BaseDomainDo;
import com.caidaocloud.message.service.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.message.service.domain.base.service.BaseDomainServiceImpl;
import com.caidaocloud.message.service.domain.notice.entity.NoticeMessageDo;
import com.caidaocloud.message.service.infrastructure.repository.po.NoticeMessagePo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * created by: FoAng
 * create time: 15/8/2024 10:35 上午
 */
@Slf4j
@Service
@AllArgsConstructor
public class NoticeMessageDomainService extends BaseDomainServiceImpl<NoticeMessagePo, BasePage> {

    private NoticeMessageDo noticeMessageDo;

    @Override
    public BaseDomainDo<NoticeMessagePo> getDoService() {
        return noticeMessageDo;
    }

    public Map<String, Integer> getTypeUnReadCount() {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        Long empId = userInfo.getEmpId();
        return noticeMessageDo.getTypeUnRead(String.valueOf(empId));
    }

    public Map<String, Integer> getBusinessUnReadCount() {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        Long empId = userInfo.getEmpId();
        return noticeMessageDo.getBusinessUnRead(String.valueOf(empId));
    }

    public List<NoticeMessagePo> listTodoByBusinessId(String businessId) {
        return noticeMessageDo.listByBusinessId(NoticeBusinessEnum.TODO.getValue(), businessId).getItems();
    }

    public PageResult<NoticeMessagePo> pageByExtData(Long receiver, NoticeMsgDto dto) {
        return noticeMessageDo.pageByExtData(String.valueOf(receiver), dto);
    }

    public void markReadAction(List<String> bids, String businessType, boolean markRead) {
        noticeMessageDo.markReadAction(bids, businessType, markRead);
    }

    public void persistAllEsData() {
        noticeMessageDo.persistAllEsData();
    }
}
