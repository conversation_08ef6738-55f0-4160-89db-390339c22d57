package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.hr.service.dto.EmpWorkInfoDto;
import com.caidaocloud.hr.service.vo.EmpPrivateInfoVo;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MasterdataFeignClientFallback implements MasterdataFeignClient{

    @Override
    public Result<EmpWorkInfoDto> getEmpWorkInfo(String empId, Long dataTime) {
        return Result.fail("获取员工任职信息失败");
    }

    @Override
    public Result<EmpPrivateInfoVo> getPrivateInfo(String empId) {
        return Result.fail("获取员工个人信息失败");
    }

    @Override
    public Result<EmpWorkInfoDto> hrbpByEmpId(String empId, Long dataTime) {
        return Result.fail("获取员工HRBP员工任职信息失败");
    }

    @Override
    public Result<EmpWorkInfoDto> leaderByEmpId(String empId, Long dataTime) {
        return Result.fail("获取员工直线上级任职信息失败");
    }

    @Override
    public Result<EmpWorkInfoDto> orgLeaderByEmpId(String empId, Long dataTime) {
        return Result.fail("获取员工部门负责人任职信息失败");
    }

    @Override
    public Result<List<EmpWorkInfoDto>> getAllEmpList() {
        return Result.fail("获取所有员工任职信息失败");
    }

    @Override
    public Result<List<EmpPrivateInfoVo>> getAllGuardianList() {
        return Result.fail("获取所有员工监护人信息失败");
    }

    @Override
    public Result<List<EmpWorkInfoDto>> allHrbp(Long dataTime) {
        return Result.fail("获取所有HRBP员工任职信息失败");
    }

    @Override
    public Result<List<EmpWorkInfoDto>> allLeader(Long dataTime) {
        return Result.fail("获取所有部门领导人任职信息失败");
    }

    @Override
    public Result<List<EmpWorkInfoDto>> getEmpInfoByEmpIds(String empIds) {
        return Result.fail("获取系统指定人员任职信息失败");
    }

    @Override
    public Result<List<EmpWorkInfoDto>> getEmpWorkInfoByEmpIds(String empIds, Long dataTime) {
        return Result.fail();
    }

    @Override
    public Result<List<EmpPrivateInfoVo>> getAllPrivateInfoByEmpIds(String empIds) {
        return Result.fail("获取批量员工个人信息失败");
    }
}
