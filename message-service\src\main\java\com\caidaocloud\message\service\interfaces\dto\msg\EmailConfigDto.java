package com.caidaocloud.message.service.interfaces.dto.msg;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * EmailConfigDto
 *
 * <AUTHOR>
 * @date 2022/6/7 上午9:53
 */
@Data
@ApiModel("邮箱配置dto")
public class EmailConfigDto {
    @ApiModelProperty("是否启用")
    private EnumSimple status;
    /**
     * 邮箱模式
     * 0 smtp 常规模式
     * 1 adapter api 适配模式
     */
    @ApiModelProperty("邮箱模式")
    private EnumSimple emailModel;

    /**
     * SMTP服务器
     */
    @ApiModelProperty("SMTP服务器")
    private String emailSmtp;

    /**
     * 端口
     */
    @ApiModelProperty("端口")
    private Integer emailPort;

    /**
     * 用户名
     */
    @ApiModelProperty("发件邮箱")
    private String emailFrom;

    /**
     * 发件人昵称
     */
    @ApiModelProperty("发件人昵称")
    private String emailNick;


    @ApiModelProperty("账号")
    private String emailUser;

    /**
     * 密码
     */
    @ApiModelProperty("密码")
    private String emailPwd;

    /**
     * 是否开启SSL
     */
    @ApiModelProperty("是否开启SSL")
    private Boolean enableSsl = false;

    /**
     * 测试邮件的收件人
     */
    @ApiModelProperty("测试邮件的收件人")
    private String testMail;
}
