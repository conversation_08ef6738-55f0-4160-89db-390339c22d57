package com.caidaocloud.message.service.infrastructure.repository.po;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.service.domain.msg.entity.MessageLogRecordDo;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;

/**
 * 消息记录表
 * <AUTHOR>
 */
@Data
public class MessageLogRecordPo extends DataSimple {
    // 事件人信息
    private EmpSimple emp;

    /**
     * 事件人类型
     * 0:员工；1:候选人；2:外部人员; 3: 角色
     */
    private String type;

    /**
     * 消息流水号
     */
    private String mdc;

    /**
     * 消息来源
     */
    private String msgFrom;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息配置
     */
    private String msgConfig;

    /**
     * 发送途径
     */
    private String channel;

    /**
     * 发送状态
     */
    private EnumSimple status;

    public MessageLogRecordDo toEntity() {
        return ObjectConverter.convert(this, MessageLogRecordDo.class);
    }
}
