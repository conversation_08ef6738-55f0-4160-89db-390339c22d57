package com.caidaocloud.message.service.infrastructure.repository.po;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.service.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.message.service.domain.notice.entity.NoticeMessageDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * created by: FoAng
 * create time: 15/8/2024 2:47 下午
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class NoticeMessagePo extends BaseDomainDoImpl<NoticeMessagePo> {

    @ApiModelProperty("接收人")
    private EmpSimple receiver;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("摘要汇总")
    private String summary;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("文本类型：plain text, rich")
    private EnumSimple contentType;

    @ApiModelProperty("类型")
    private EnumSimple type;

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("业务ID")
    private String businessId;

    @ApiModelProperty("标记已读")
    private Boolean markRead;

    @ApiModelProperty("操作类型")
    private EnumSimple action;

    @ApiModelProperty("操作内容")
    private String actionContent;

    @ApiModelProperty("扩展字段")
    private String ext;
}
