package com.caidaocloud.message.service.application.feign.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class UserDetailInfoVo {
    @ApiModelProperty
    private String accountId;
    @ApiModelProperty("用户统一注册账号")
    private String account;
    @ApiModelProperty("userId")
    private Long userId;
    @ApiModelProperty("用户姓名")
    private String userName;
    @ApiModelProperty("员工ID")
    private Long empId;
    // @ApiModelProperty("工号")
    // private String workno;
    @ApiModelProperty("所属组织")
    private String organizeTxt;
    @ApiModelProperty("注册手机号")
    private String mobile;
    @ApiModelProperty("注册邮箱")
    private String email;
    @ApiModelProperty("账号状态：1 正常 2 停用 3 锁定")
    private Integer status;
    @ApiModelProperty("角色ID")
    private List<String> roleIds;
    @ApiModelProperty("角色")
    private String roleNames;
    @ApiModelProperty("钉钉userId")
    private String dingUserId;


}
