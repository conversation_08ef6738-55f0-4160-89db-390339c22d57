package com.caidaocloud.message.service.application.msg.cron;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.service.application.msg.dto.MessageConfigPageQueryDto;
import com.caidaocloud.message.service.application.msg.service.MsgConfigService;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.infrastructure.utils.DateUtil;
import com.caidaocloud.message.service.interfaces.vo.msg.MsgConfigVo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class MsgNoticeTaskService {
    @Value("${caidaocloud.tenant:}")
    private List<String> tenantList;
    @Resource
    private MsgConfigService msgConfigService;

    @XxlJob("messageNoticeJobHandler")
    public ReturnT<String> autoMessageNoticeJobHandler() {
        XxlJobHelper.log("XxlJob autoMsgNoticeJobHandler start");
        log.info("cronTask[Msg Notice]------------------------start execution,time {}", System.currentTimeMillis());

        if (null == tenantList || tenantList.isEmpty()) {
            log.info("Msg Notice: tenantList is empty, execution end");
            return ReturnT.SUCCESS;
        }

        // 遍历租户，进行消息提醒
        for (String tenantId : tenantList) {
            doStartMsgNoticeByTenantId(tenantId);
        }

        log.info("cronTask[Msg Notice]------------------------end execution,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob autoMsgNoticeJobHandler end");
        return ReturnT.SUCCESS;
    }

    private void doStartMsgNoticeByTenantId(String tenantId) {
        // 初始化用户上下文
        UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
        try {
            // 手动推送消息提醒
            MessageConfigPageQueryDto queryDto = new MessageConfigPageQueryDto();
            queryDto.setMsgType(NoticeType.MANUAL_PUSH.getIndex());
            queryDto.setTenantId(tenantId);
            queryDto.setPageNo(1);
            queryDto.setPageSize(200);
            doStartManualPushMsgNotice(queryDto);

            //手动推送解析模版
            queryDto.setMsgType(NoticeType.EMP_MANUAL_PUSH.getIndex());
            doStartManualPushMsgNotice(queryDto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            UserContext.removeSecurityUserInfo();
        }
    }

    /**
     * 手动推送消息
     *
     * @param queryDto
     * @throws Exception
     */
    private void doStartManualPushMsgNotice(MessageConfigPageQueryDto queryDto) throws Exception {
        // 查询【手动推送】配置信息
        PageResult<MsgConfigVo> pageResult = msgConfigService.getEnableMsgConfigPageList(queryDto);
        List<MsgConfigVo> msgConfigList = null;
        if (null == pageResult || null == (msgConfigList = pageResult.getItems()) || msgConfigList.isEmpty()) {
            log.debug("doStartManualPushMsgNotice getEnableMsgConfigList Empty tenantId={}", queryDto.getTenantId());
            return;
        }

        // 根据消息配置进行消息推送
        long nowDate = DateUtil.getCurrentDateMillis();
        for (MsgConfigVo msgConfig : msgConfigList) {
            if (null == msgConfig.getTriggerDate() || msgConfig.getTriggerDate() != nowDate) {
                log.info("doStartManualPushMsgNotice msgConfig is not notice bid={} ,triggerDate={},nowDate={}", msgConfig.getBid(), msgConfig.getTriggerDate(), nowDate);
                continue;
            }
            msgConfigService.doStartManualPushMsg(msgConfig.getBid());
        }

        // 递归分页查询
        if (msgConfigList.size() < queryDto.getPageSize()) {
            return;
        }
        queryDto.setPageNo(pageResult.getPageNo() + 1);
        doStartManualPushMsgNotice(queryDto);
    }
}
