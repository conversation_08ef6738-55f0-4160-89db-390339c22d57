[{"action": "create", "entityDef": {"identifier": "entity.message.EmailConfig", "name": "邮件服务设置", "owner": "message", "label": "emailSmtp", "timelineEnabled": false, "standardProperties": [{"property": "emailSmtp", "name": "SMTP服务器", "dataType": "String", "widgetType": "text", "rules": [{"type": "required", "value": "true"}]}, {"property": "emailPort", "name": "端口", "dataType": "Integer", "widgetType": "int"}, {"property": "emailFrom", "name": "用户名", "dataType": "String", "widgetType": "text", "rules": [{"type": "required", "value": "true"}]}, {"property": "emailPwd", "name": "密码", "dataType": "String", "widgetType": "text", "rules": [{"type": "required", "value": "true"}]}, {"property": "emailNick", "name": "发件人昵称", "dataType": "String", "widgetType": "text", "rules": [{"type": "required", "value": "true"}]}, {"property": "status", "name": "状态", "dataType": "Enum", "widgetType": "enum", "visible": true, "modifiable": false, "defaultValue": {"value": 0, "type": "Enum"}, "enumDef": [{"value": 0, "display": "未启用"}, {"value": 1, "display": "已启用"}]}, {"property": "enableSsl", "name": "是否开启SSL", "dataType": "Boolean", "widgetType": "boolean", "required": true}, {"property": "emailModel", "name": "邮箱模式", "dataType": "Enum", "widgetType": "enum", "visible": true, "modifiable": false, "defaultValue": {"value": 0, "type": "Enum"}, "enumDef": [{"value": 0, "display": "常规模式"}, {"value": 1, "display": "适配模式"}]}]}}]