package com.caidaocloud.message.service.domain.template.entity;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.service.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;
import com.caidaocloud.message.service.domain.template.repository.TemplateRepository;
import com.caidaocloud.message.service.infrastructure.utils.LangUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.caidaocloud.message.service.application.common.constant.LangCodeConstant.DUPLICATE_TEMPLATE_NAME;

@Data
@Service
public class TemplateDo extends BaseDomainDoImpl {
    /**
     * 模版名称
     */
    private String name;
    /**
     * 模版名称多语言
     */
    private String i18nName;

    /**
     * 模版内容
     */
    private String content;

    /**
     * 模版分类
     * 0 短信模版
     * 1 邮件模版
     * 2 系统模版
     * 3 APP模版
     */
    private EnumSimple category;
    /**
     * 模版多语言
     */
    private String i18nCategory;

    /**
     * 模版标题
     */
    private String title;

    /**
     * 钉钉模版类型： 链接消息，文本消息，流程消息
     */
    private EnumSimple templateType;

    /**
     * 钉钉模版URL
     */
    private String templateUrl;

    /**
     * 模版code
     */
    private String code;

    /**
     * 模版附件
     */
    private Attachment attachFile;

    /**
     * 消息设置
     */
    private String msgConfig;

    /**
     * 内置变量配置
     */
    private String variable;

    /**
     * 抄送对象
     * hrbp HRBP
     * leader 上级领导
     * orgLeader 部门负责人
     */
    private String copyObject;

    /**
     * 抄送邮箱
     */
    private String copyMail;

    /**
     * 密送对象
     * hrbp HRBP
     * leader 上级领导
     * orgLeader 部门负责人
     */
    private String blindCopyObject;
    /**
     * 密送邮箱
     */
    private String blindCopyMail;

    /**
     * 外部人员邮箱
     */
    private String externalMail;

    /**
     * 通知规则
     */
    private EnumSimple noticeRule;

    private final static String IDENTIFIER = "entity.message.Template";

    // paas不支持查询msg_config = null 的数据，默认保存为0
    public final static String DEFAULT_CONFIG = "0";

    @Resource
    private TemplateRepository repository;

    @Override
    public BaseRepository getRepository() {
        return repository;
    }

    @Override
    public String getDoIdentifier() {
        return IDENTIFIER;
    }

    @Override
    protected DataSimple build() {
        TemplateDo data = new TemplateDo();
        data.setIdentifier(IDENTIFIER);
        return data;
    }

    public List<TemplateDo> getAllByCategory(String category) {
        EnumSimple categoryEnum = new EnumSimple();
        categoryEnum.setValue(category);

        TemplateDo query = (TemplateDo) build();
        query.setCategory(categoryEnum);
        query.setMsgConfig(DEFAULT_CONFIG);
        return repository.selectList(query);

    }

    public void deleteByConfigId(String configId) {
        repository.deleteByConfigId(IDENTIFIER, configId);
    }

    public void batchSave(List<TemplateDo> list) {
        repository.batchInsert(IDENTIFIER, list);
    }

    public List<TemplateDo> getByConfigId(String bid) {
        TemplateDo query = (TemplateDo) build();
        query.setMsgConfig(bid);
        return repository.selectList(query);
    }

    public void checkName(TemplateDo data) {
        // 与消息设关联的模板不校验名称
        if (!DEFAULT_CONFIG.equals(data.getMsgConfig())) {
            return;
        }
        // 同一类别的模板名称不重复能
        PreCheck.preCheckArgument(repository
                .countName(IDENTIFIER, data) > 0, LangUtil.getMsg(DUPLICATE_TEMPLATE_NAME));
    }
}
