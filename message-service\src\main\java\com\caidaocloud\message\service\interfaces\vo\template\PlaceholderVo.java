package com.caidaocloud.message.service.interfaces.vo.template;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

@Data
@ApiModel("内置变量vo")
public class PlaceholderVo {
    /**
     * 占位符的显示名称
     */
    @ApiModelProperty("占位符的显示名称")
    private String text;

    /**
     * 占位符的列表字段名
     */
    @ApiModelProperty("占位符的列表字段名")
    private String name;

    /**
     * 占位符来源与哪个模型
     */
    @ApiModelProperty("模型")
    private String model;

    /**
     * 占位符的所属模型的属性名
     */
    @ApiModelProperty("字段")
    private String prop;

    @ApiModelProperty("类型")
    private String varType;
}
