package com.caidaocloud.message.service.application.msg.event;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import com.caidaocloud.message.service.application.log.dto.MessageLogRecordDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.message.service.application.message.notifier.service.NotifyTargetFactory;
import com.caidaocloud.message.service.application.msg.dto.MsgDto;
import com.caidaocloud.message.service.application.msg.service.MsgConfigService;
import com.caidaocloud.message.service.application.msg.service.MsgService;
import com.caidaocloud.message.service.application.sms.dto.SmsMessageDto;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.interfaces.vo.msg.MsgConfigDetailVo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * MsgSubscribe
 *
 * <AUTHOR>
 * @date 2022/6/9 下午2:30
 */
@Slf4j
@Component
public class MsgSubscribe {

    @Resource
    private MsgService msgService;

    @Resource
    private MsgConfigService configService;


    /**
     * 普通文本消息处理
     *
     * @param message
     */
    @RabbitHandler
    @RabbitListener(
        bindings = @QueueBinding(
            value = @Queue(value = "caidaocloud.message.plain.queue", durable = "true"),
            exchange = @Exchange(value = "message.fac.direct.exchange"),
            key = {"routingKey.message.plain.msg"}
        )
    )
    public void process(String message) {
        log.info("MsgSubscribe time={} message={}", System.currentTimeMillis(), message);

        try {
            MsgDto msgDto = FastjsonUtil.toObject(message, MsgDto.class);
            if (msgDto != null &&
                    msgDto.getEmailMsg() != null &&
                    StringUtils.isNotBlank(msgDto.getEmailMsg().getContent())) {
                var content = new String(Base64.getDecoder().decode(msgDto.getEmailMsg().getContent().getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
                msgDto.getEmailMsg().setContent(content);
            }

            SecurityUserInfo userInfo = new SecurityUserInfo();
            String tenantId = msgDto.getTenantId();
            userInfo.setTenantId(tenantId);
            Long userId = msgDto.getUserId();
            userId = userId == null ? 0L : userId;
            userInfo.setUserId(userId);
            SecurityUserUtil.setSecurityUserInfo(userInfo);

            UserInfo user = new UserInfo();
            user.setTenantId(tenantId);
            user.doSetUserId(userId);
            UserContext.setCurrentUser(user);
            replaceReceivers(msgDto);
            //记录消息日志
            MessageLogRecordDto messageLogRecordDto = msgService.saveMessageLogRecord(message, msgDto);
            msgDto.setMdc(messageLogRecordDto.getMdc());

            msgService.doMsg(msgDto);
        } catch (Exception e) {
            log.error("process plain msg err,{}", e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }

    private void replaceReceivers(MsgDto msg){
        if(StringUtils.isEmpty(msg.getMsgConfig())){
            return;
        }
        String msgConfigBid = msg.getMsgConfig();
        // 查询消息配置
        MsgConfigDetailVo msgConfig = configService.getDetail(msgConfigBid);
        val notifyObject = msgConfig.getNotifyObject();
        if(CollectionUtils.isEmpty(notifyObject)){
            return;
        }
        if(null == msg.getExt() || msg.getExt().get("receiversReplacedBy") == null){
            return;
        }
        val empId = Objects.toString(msg.getExt().get("receiversReplacedBy"));
        NotifyObjectDto notify = new NotifyObjectDto();
        notify.setEventSubject(empId);
        notify.setEmpIds(Lists.list(empId));
        notify.setSubject(empId);
        notify.setDataTime(System.currentTimeMillis());
        val notifiers = NotifyTargetFactory.getDistinctNotifier(notifyObject, notify);

        List<String> emails = Lists.list();
        List<PhoneSimple> phones = Lists.list();
        notifiers.forEach(notifier->{
            if(StringUtils.isNotEmpty(notifier.getCompanyEmail())){
                emails.add(notifier.getCompanyEmail());
            }else if(StringUtils.isNotEmpty(notifier.getEmail())){
                emails.add(notifier.getEmail());
            }
            if(null != notifier.getPhone() && StringUtils.isNotEmpty(notifier.getPhone().getValue())){
                phones.add(notifier.getPhone());
            }
        });
        if (msg.getMessage() != null) {
            msg.getMessage().setMobile(Lists.list());
            if(!phones.isEmpty()){
                msg.getMessage().getMobile().addAll(
                        phones.stream().map(it->{
                            SmsMessageDto.MobileDto mobile = new SmsMessageDto.MobileDto();
                            mobile.setMobile(it.getValue());
                            if(StringUtils.isNotEmpty(it.getCode())){
                                mobile.setCountryCode(it.getCode());
                            }
                            return mobile;
                        }).collect(Collectors.toList())
                );
            }
        }
        if(msg.getEmailMsg() != null && !emails.isEmpty()){
            msg.getEmailMsg().setTo(StringUtils.join(emails, ","));
        }
    }

    /**
     * 延迟队列消息处理
     *
     * @param message
     */
    @RabbitHandler
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "caidaocloud.message.delayed.queue", durable = "true"),
            exchange = @Exchange(value = "message.fac.delayed.exchange", delayed = "true"),
            key = {"routingKey.message.delayed.msg"})
    )
    public void processDelayed(String message) {
        log.info("-----processDelayed message={}", message);
        process(message);
    }
}
