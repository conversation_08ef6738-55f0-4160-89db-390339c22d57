package com.caidaocloud.message.service.infrastructure.repository.impl.msg;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.message.service.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.message.service.domain.msg.entity.EmpMsgTemplateRelDo;
import com.caidaocloud.message.service.domain.msg.repository.EmpMsgTemplateRelRepository;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
public class EmpMsgTemplateRelRepositoryImpl extends BaseRepositoryImpl<EmpMsgTemplateRelDo> implements EmpMsgTemplateRelRepository {
    private final static String IDENTIFIER = "entity.message.EmpMsgTemplateRel";

    @Override
    public List<EmpMsgTemplateRelDo> getListByEmpId(String empId) {
        DataFilter filter = getBaseFilter();
        filter = filter.andEq("empId", empId);
        PageResult<EmpMsgTemplateRelDo> pageResult = DataQuery.identifier(IDENTIFIER).decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1).filter(filter, EmpMsgTemplateRelDo.class);
        return getPageList(pageResult);
    }

    @Override
    public List<EmpMsgTemplateRelDo> getListByEmpList(List<String> empList) {
        DataFilter filter = getBaseFilter();
        filter = filter.andIn("empId", empList);
        PageResult<EmpMsgTemplateRelDo> pageResult = DataQuery.identifier(IDENTIFIER).decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1).filter(filter, EmpMsgTemplateRelDo.class);
        return getPageList(pageResult);
    }

    public void deleteByEmpIdAndTemplateId(String template, List<String> empList) {
        DataFilter filter = getBaseFilter();
        filter = filter.andEq("template", template).andIn("empId", empList);
        DataDelete.identifier(IDENTIFIER).batchDelete(filter);
    }

    public void batchInsert(List<EmpMsgTemplateRelDo> dataList) {
        DataInsert.identifier(IDENTIFIER).batchInsert(dataList);
    }

    @Override
    public void deleteTemplateId(String template) {
        DataFilter filter = getBaseFilter();
        filter = filter.andEq("template", template);
        DataDelete.identifier(IDENTIFIER).batchDelete(filter);
    }

    @Override
    public List<String> getListByEmpList(String msgConfig, BasePage page) {
        DataFilter filter = getBaseFilter();
        filter = filter.andEq("template", msgConfig);
        PageResult<Map<String, String>> pageData = DataQuery.identifier(IDENTIFIER).limit(page.getPageSize(), page.getPageNo())
                .filterProperties(filter, Lists.newArrayList("empId"), System.currentTimeMillis());
        List<Map<String, String>> pageList = null;
        if (null == pageData || null == (pageList = pageData.getItems()) || pageList.isEmpty()) {
            return Lists.newArrayList();
        }
        return pageData.getItems().stream().map(data -> data.get("empId")).collect(Collectors.toList());
    }

    @Override
    public List<String> getEmpListByEmpIds(String msgConfig, BasePage page, List<String> empIdList) {
        DataFilter filter = getBaseFilter();
        filter = filter.andEq("template", msgConfig).andIn("empId", empIdList);
        PageResult<Map<String, String>> pageData = DataQuery.identifier(IDENTIFIER).limit(page.getPageSize(), page.getPageNo())
                .filterProperties(filter, Lists.newArrayList("empId"), System.currentTimeMillis());
        List<Map<String, String>> pageList = null;
        if (null == pageData || null == (pageList = pageData.getItems()) || pageList.isEmpty()) {
            return Lists.newArrayList();
        }

        return pageData.getItems().stream().map(data -> data.get("empId")).distinct().collect(Collectors.toList());
    }
}
