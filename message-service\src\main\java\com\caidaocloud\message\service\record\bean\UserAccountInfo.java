package com.caidaocloud.message.service.record.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.zxp.esclientrhl.annotation.ESMapping;

import java.io.Serializable;

/**
 * created by: FoAng
 * create time: 28/5/2024 5:39 下午
 */
@Data
public class UserAccountInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户统一注册账号")
    @ESMapping
    private String account;
    @ApiModelProperty("注册手机号")
    @ESMapping
    private String mobNum;
    @ApiModelProperty("注册邮箱")
    @ESMapping
    private String email;
    @ApiModelProperty("用户ID")
    @ESMapping
    private Long userId;
    @ApiModelProperty("租户ID")
    @ESMapping
    private Long tenantId;
    @ApiModelProperty("用户姓名")
    @ESMapping
    private String userName;
    @ApiModelProperty("员工ID")
    @ESMapping
    private Long empId;

    private String workNo;
}
