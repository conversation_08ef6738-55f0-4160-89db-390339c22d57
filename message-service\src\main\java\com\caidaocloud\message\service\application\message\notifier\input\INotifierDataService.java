package com.caidaocloud.message.service.application.message.notifier.input;

import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.hr.service.vo.EmpPrivateInfoVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDataDto;
import com.caidaocloud.message.service.application.message.notifier.dto.RoleUserInfoDto;

import java.util.List;

/**
 * 获取通知对象详情数据接口
 */
public interface INotifierDataService {
    List<RoleUserInfoDto> getUserInfoByRoles(List<String> roleIds);

    List<EmpDataVo> getRecruiterByEmpIds(List<String> empIds, Long dataTime);

    List<EmpDataVo> getOrgLeadersByEmpIds(List<String> empIds, Long dataTime);

    List<EmpWorkInfoVo> getEmpDataInfoByEmpIds(List<String> empIds, Long dataTime);

    List<EmpPrivateInfoVo> getEmpGuardianByEmpIds(List<String> empIds, Long dataTime);

    List<EmpDataVo> getEmpDataByEmpIds(List<String> empIds, Long dataTime);

    List<EmpDataVo> getHrbpDataByEmpIds(NotifierDataDto request);

    List<EmpDataVo> getLeadersByEmpIds(NotifierDataDto request);

    List<EmpDataVo> getCandidateDataByEmpIds(List<String> empIds, Long dataTime);

    List<EmpDataVo> getCandidateOrgLeaders(NotifierDataDto request);

    List<EmpDataVo> getCandidateRecruiter(NotifierDataDto request);

    List<EmpDataVo> getCandidateRecruitment(NotifierDataDto request);
    List<EmpDataVo> getInitiatorDataByEmpIds(NotifierDataDto request);
}
