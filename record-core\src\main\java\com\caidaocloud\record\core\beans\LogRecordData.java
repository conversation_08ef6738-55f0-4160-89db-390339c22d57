package com.caidaocloud.record.core.beans;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * created by: FoAng
 * create time: 22/5/2024 1:48 下午
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LogRecordData {

    @ApiModelProperty("租户ID")
    private String tenantId;

    @ApiModelProperty("服务名称")
    private String serviceName;

    @ApiModelProperty("操作人")
    private Long operator;

    @ApiModelProperty("员工ID")
    private Long empId;

    @ApiModelProperty("操作来源")
    private String operatorSource;

    @ApiModelProperty("操作平台")
    private String operatorPlatform;

    @ApiModelProperty("请求uri")
    private String uri;

    @ApiModelProperty("请求菜单")
    private String menu;

    @ApiModelProperty("操作类型")
    private String category;

    @ApiModelProperty("操作摘要")
    private String action;

    @ApiModelProperty("操作详细内容")
    private String detail;

    @ApiModelProperty("操作状态")
    private Boolean status;

    @ApiModelProperty("操作时间")
    private Date createTime;

}
