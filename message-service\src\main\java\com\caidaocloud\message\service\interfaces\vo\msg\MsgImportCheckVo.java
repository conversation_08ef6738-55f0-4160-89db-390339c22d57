package com.caidaocloud.message.service.interfaces.vo.msg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@ApiModel("校验上传发起签署人员vo")
@Accessors(chain = true)
public class MsgImportCheckVo {
    @ApiModelProperty("验证成功的员工ID列表")
    private List<String> empList;
    @ApiModelProperty("验证成功的员工姓名+邮箱列表")
    private List<MsgEmpCheckDto> sucessTextList;
    @ApiModelProperty("验证失败的员工姓名+邮箱列表")
    private List<MsgEmpCheckDto> failTextList;
}
