package com.caidaocloud.message.service.domain.template.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.message.service.domain.base.entity.BaseDomainDo;
import com.caidaocloud.message.service.domain.base.service.BaseDomainServiceImpl;
import com.caidaocloud.message.service.domain.msg.entity.MsgConfigDo;
import com.caidaocloud.message.service.domain.template.entity.TemplateDo;
import com.caidaocloud.message.service.infrastructure.utils.LangUtil;
import com.caidaocloud.message.service.interfaces.dto.template.TemplateDto;
import com.caidaocloud.message.service.interfaces.dto.template.TemplateVariableDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.caidaocloud.message.service.application.common.constant.LangCodeConstant.*;
import static com.caidaocloud.message.service.domain.template.entity.TemplateDo.DEFAULT_CONFIG;

@Service
@Slf4j
public class TemplateDomainService extends BaseDomainServiceImpl<TemplateDo, BasePage> {
    @Resource
    private TemplateDo templateDo;
    @Resource
    private PlaceholderDomainService placeholderDomainService;

    @Override
    public BaseDomainDo<TemplateDo> getDoService() {
        return templateDo;
    }


    @Override
    public TemplateDo save(TemplateDo data) {
        if (data.getMsgConfig() == null) {
            data.setMsgConfig(DEFAULT_CONFIG);
        }
        checkName(data);
        return super.save(data);
    }

    @Override
    public TemplateDo updateById(TemplateDo data) {
        if (data.getMsgConfig() == null) {
            data.setMsgConfig(DEFAULT_CONFIG);
        }
        checkName(data);
        return super.updateById(data);
    }

    private void checkName(TemplateDo data) {
        templateDo.checkName(data);
    }

    /**
     * 查询模板分类下所有模板
     *
     * @param category
     * @return
     */
    public List<TemplateDo> getAllByCategory(String category) {
        return templateDo.getAllByCategory(category);
    }

    public void deleteByConfig(MsgConfigDo config) {
        templateDo.deleteByConfigId(config.getBid());
    }

    public void saveMsgTemplate(MsgConfigDo config, List<TemplateDto> templates) {
        PreCheck.preCheckNotNull(config.getBid(), LangUtil.getFormatMsg(code_70000, LangUtil.getMsg(code_70008)));
        // 删除已有消息设置的模板
        log.info("recreate msg template by config '{}'", config.getBid());
        deleteByConfig(config);

        Map<String, TemplateVariableDto> tvMap = placeholderDomainService.mapPlaceholder();
        // 批量插入新的模板
        List<TemplateDo> list = templates.stream().map(dto -> {
            TemplateDo data = ObjectConverter.convert(dto, TemplateDo.class);
            data.setBid(null);
            data.setMsgConfig(config.getBid());
            data.setCreateBy(config.getUpdateBy());
            data.setUpdateBy(config.getUpdateBy());
            data.setCreateTime(config.getUpdateTime());
            data.setUpdateTime(config.getUpdateTime());
            data.setDataEndTime(DateUtil.MAX_TIMESTAMP);
            if (dto.getCopyObject() != null) {
                data.setCopyObject(FastjsonUtil.toJson(dto.getCopyObject()));
            }
            if (dto.getBlindCopyObject() != null) {
                data.setBlindCopyObject(FastjsonUtil.toJson(dto.getBlindCopyObject()));
            }
            if (StringUtils.isNotEmpty(dto.getNoticeRule())) {
                EnumSimple noticeRule = new EnumSimple();
                noticeRule.setValue(dto.getNoticeRule());
                data.setNoticeRule(noticeRule);
            }
            if (dto.getTemplateType() != null) {
                EnumSimple enumSimple = new EnumSimple();
                enumSimple.setValue(dto.getTemplateType().getIndex());
                data.setTemplateType(enumSimple);
            }

            List<TemplateVariableDto> variableList = variablePparse(dto.getContent(), tvMap, dto.getVariable());
            variableList.addAll(variablePparse(dto.getTitle(), tvMap, dto.getVariable()));
            data.setVariable(FastjsonUtil.toJson(variableList));
            checkAttachment(data);

            return data;
        }).collect(Collectors.toList());
        templateDo.batchSave(list);
    }

    /**
     * 内置变量解析
     */
    public List<TemplateVariableDto> variablePparse(String content, Map<String, TemplateVariableDto> map, List<TemplateVariableDto> variable) {
        List<TemplateVariableDto> variableList =  Lists.list();
        if(StringUtil.isEmpty(content)){
            return variableList;
        }

        Map<String, TemplateVariableDto> collect = null == variable ? new HashMap<>() :
                variable.stream().collect(Collectors.toMap(TemplateVariableDto::getText, item -> item, (o1, o2) -> o1));

        String word = "#", substring, newWord = "";
        int len = word.length(), length = content.length(), dlnIndex = 0;

        while (dlnIndex < length) {
            dlnIndex = content.indexOf(word, dlnIndex);
            if (dlnIndex < 0) {
                // 未找到，则结束
                break;
            }
            int endIndex = content.indexOf(word, dlnIndex + len);
            if (endIndex < 0) {
                // 找到了结尾还未找到，则结束
                break;
            }
            substring = content.substring(dlnIndex + len, endIndex);
            dlnIndex = endIndex;
            if(substring.indexOf(".") > -1) {
                newWord = word + substring + word;
                TemplateVariableDto templateVariableDto = map.get(newWord);
                if(null != templateVariableDto){
                    variableList.add(templateVariableDto);
                } else if(newWord.startsWith("#审批人")){
                    TemplateVariableDto tv = new TemplateVariableDto();
                    TemplateVariableDto tempVal = collect.get(newWord);
                    if(null == tempVal){
                        log.warn("Incorrect built-in configuration of approver.");
                        continue;
                    }
                    tv.setText(newWord);
                    tv.setName(newWord);
                    tv.setVarType("0");
                    tv.setModel(StringUtil.isEmpty(tempVal.getModel()) ? tempVal.getProp() : tempVal.getModel());
                    tv.setProp("other.approverTxt");
                    variableList.add(tv);
                }
            }
        }
        return variableList.stream().distinct().collect(Collectors.toList());
    }

    public List<TemplateDo> getByConfig(MsgConfigDo data) {
        return templateDo.getByConfigId(data.getBid());
    }

    public void checkAttachment(TemplateDo data) {
        if (data.getAttachFile() == null) {
            return;
        }
        // 消息附件不能大于 10 个
        PreCheck.preCheckArgument(null != data.getAttachFile().getUrls()
                        && null != data.getAttachFile().getNames()
                        && (data.getAttachFile().getUrls().size() > 10 || data.getAttachFile().getNames().size() > 10),
                LangUtil.getMsg(code_70020));
        // todo 目前附件names在mysql中最长200字符，后续paas修改names长度后移除
        PreCheck.preCheckArgument(null != data.getAttachFile().getNames()
                && FastjsonUtil.toJson(data.getAttachFile().getNames()).length() > 500, LangUtil.getMsg(code_70021));
    }
}