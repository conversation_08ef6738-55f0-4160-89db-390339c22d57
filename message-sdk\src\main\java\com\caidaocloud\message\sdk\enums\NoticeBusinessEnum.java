package com.caidaocloud.message.sdk.enums;

import com.caidaocloud.util.StringUtil;
import lombok.Getter;

import java.util.Arrays;

/**
 * 消息业务类型
 * created by: FoAng
 * create time: 15/8/2024 11:15 上午
 */
@Getter
public enum NoticeBusinessEnum {

    TODO("待办", "TODO"),

    HR("人事", "HR"),

    APPROVAL("审批", "APPROVAL"),

    EVENT("事件", "EVENT"),

    SYSTEM("系统", "SYSTEM"),

    POLICY("公告", "POLICY"),

    OTHER("其他", "OTHER");

    final String label;

    final String value;

    NoticeBusinessEnum(String label, String value) {
        this.label = label;
        this.value = value;
    }

    public static NoticeBusinessEnum of(String value) {
        return Arrays.stream(values()).filter(it -> StringUtil.isNotEmpty(value) && it.value.equals(value))
                .findFirst().orElse(NoticeBusinessEnum.OTHER);
    }

}
