package com.caidaocloud.message.service.interfaces.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.service.application.announcement.dto.AnnouncementPageDto;
import com.caidaocloud.message.service.application.announcement.service.AnnouncementStatusService;
import com.caidaocloud.message.service.interfaces.vo.announcement.AnnouncementPageVo;
import com.caidaocloud.message.service.interfaces.vo.announcement.AnnouncementVo;
import com.caidaocloud.web.Result;
import com.caidaocloud.message.service.application.announcement.dto.AnnouncementDto;
import com.caidaocloud.message.service.application.announcement.service.AnnouncementApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 公告管理Controller
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Slf4j
@Api(tags = "公告管理")
@RestController
@RequestMapping("/api/msg/announcement/v1")
public class AnnouncementController {

    @Autowired
    private AnnouncementApplicationService announcementApplicationService;

    @Autowired
    private AnnouncementStatusService announcementStatusService;

    /**
     * 创建公告
     * 
     * @param dto 公告信息
     * @return 公告ID
     */
    @ApiOperation("创建公告")
    @PostMapping("save")
    public Result<String> createAnnouncement(@Valid @RequestBody AnnouncementDto dto) {
        try {
            log.info("接收到创建公告请求，名称：{}", dto.getName());
            String announcementId = announcementApplicationService.createAnnouncement(dto);
            return Result.ok(announcementId);
        } catch (Exception e) {
            log.error("创建公告失败", e);
            return Result.fail("创建公告失败：" + e.getMessage());
        }
    }

    /**
     * 更新公告
     * 
     * @param dto 公告信息
     * @return 是否成功
     */
    @ApiOperation("更新公告")
    @PutMapping("edit")
    public Result<Boolean> updateAnnouncement(@Valid @RequestBody AnnouncementDto dto) {
        try {
            log.info("接收到更新公告请求，ID：{}", dto.getBid());
            boolean success = announcementApplicationService.updateAnnouncement(dto);
            return Result.ok(success);
        } catch (Exception e) {
            log.error("更新公告失败，ID：{}", dto.getBid(), e);
            return Result.fail("更新公告失败：" + e.getMessage());
        }
    }

    /**
     * 删除公告
     * 
     * @param announcementId 公告ID
     * @return 是否成功
     */
    @ApiOperation("删除公告")
    @DeleteMapping("/delete")
    public Result<Boolean> deleteAnnouncement(
            @ApiParam(value = "公告ID", required = true) @RequestParam @NotBlank(message = "公告ID不能为空") String announcementId) {
        try {
            log.info("接收到删除公告请求，ID：{}", announcementId);
            boolean success = announcementApplicationService.deleteAnnouncement(announcementId);
            return Result.ok(success);
        } catch (Exception e) {
            log.error("删除公告失败，ID：{}", announcementId, e);
            return Result.fail("删除公告失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询公告详情
     * 
     * @param announcementId 公告ID
     * @return 公告详情
     */
    @ApiOperation("查询公告详情")
    @GetMapping("/detail")
    public Result<AnnouncementVo> getAnnouncementById(
            @ApiParam(value = "公告ID", required = true) @RequestParam @NotBlank(message = "公告ID不能为空") String announcementId) {
        try {
            log.info("接收到查询公告详情请求，ID：{}", announcementId);
            AnnouncementVo vo = announcementApplicationService.detail(announcementId);
            if (vo == null) {
                return Result.fail("公告不存在");
            }
            return Result.ok(vo);
        } catch (Exception e) {
            log.error("查询公告详情失败，ID：{}", announcementId, e);
            return Result.fail("查询公告详情失败：" + e.getMessage());
        }
    }

    @ApiOperation("公告列表")
    @PostMapping("page")
    public Result<PageResult<AnnouncementPageVo>> page(@RequestBody AnnouncementPageDto announcementPageDto) {
        return Result.ok(announcementApplicationService.page(announcementPageDto));
    }

    /**
     * 发布公告
     * 
     * @param announcementId 公告ID
     * @return 是否成功
     */
    @ApiOperation("发布公告")
    @PostMapping("/publish")
    public Result<Boolean> publishAnnouncement(
            @ApiParam(value = "公告ID", required = true) @RequestBody @NotBlank(message = "公告ID不能为空") AnnouncementDto dto) {
        String announcementId = dto.getBid();
        try {
            log.info("接收到发布公告请求，ID：{}", announcementId);
            boolean success = announcementStatusService.publishAnnouncement(announcementId);
            return Result.ok(success);
        } catch (Exception e) {
            log.error("发布公告失败，ID：{}", announcementId, e);
            return Result.fail("发布公告失败：" + e.getMessage());
        }
    }

    /**
     * 失效公告
     * 
     * @param announcementId 公告ID
     * @return 是否成功
     */
    @ApiOperation("失效公告")
    @PostMapping("/expire")
    public Result<Boolean> expireAnnouncement(
            @ApiParam(value = "公告ID", required = true) @RequestBody @NotBlank(message = "公告ID不能为空") AnnouncementDto dto) {
        String announcementId = dto.getBid();
        try {
            log.info("接收到失效公告请求，ID：{}", announcementId);
            boolean success = announcementStatusService.expireAnnouncement(announcementId);
            return Result.ok(success);
        } catch (Exception e) {
            log.error("失效公告失败，ID：{}", announcementId, e);
            return Result.fail("失效公告失败：" + e.getMessage());
        }
    }

    /**
     * 撤回公告
     * 
     * @param announcementId 公告ID
     * @return 是否成功
     */
    @ApiOperation("撤回公告")
    @PostMapping("/withdraw")
    public Result<Boolean> withdrawAnnouncement(
            @ApiParam(value = "公告ID", required = true) @RequestBody @NotBlank(message = "公告ID不能为空")AnnouncementDto dto) {
        String announcementId = dto.getBid();
        try {
            log.info("接收到撤回公告请求，ID：{}", announcementId);
            boolean success = announcementStatusService.withdrawAnnouncement(announcementId);
            return Result.ok(success);
        } catch (Exception e) {
            log.error("撤回公告失败，ID：{}", announcementId, e);
            return Result.fail("撤回公告失败：" + e.getMessage());
        }
    }
}
