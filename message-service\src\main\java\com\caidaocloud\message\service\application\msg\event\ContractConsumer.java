package com.caidaocloud.message.service.application.msg.event;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.service.application.message.send.MessageSendService;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.mq.rabbitmq.consumer.AbsMQConsumer;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class ContractConsumer extends AbsMQConsumer {
    @Resource
    private MessageSendService messageSendService;

    @Override
    public void process(String message) {
        log.info("-----------------mq receive msg info:{}", message);

        try {
            ContractEventEntity entity = FastjsonUtil.toObject(message, ContractEventEntity.class);
            String tenantId = entity.getTenantId();
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(tenantId);
            final Long userId = entity.getUserId();
            userInfo.setEmpId(0L);
            userInfo.setUserId(Long.valueOf(userId));
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            UserInfo user = new UserInfo();
            user.setTenantId(tenantId);
            user.setStaffId(0L);
            user.doSetUserId(userInfo.getUserId());
            UserContext.setCurrentUser(user);

            messageSendService.doConsumeMessage(entity);
        } catch (Exception e) {
            log.error("process message={} err,{}", message, e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }

    @Override
    public void onMessage(Message message) {
        super.onMessage(message);
        log.info("---- msg property:{}", message.getMessageProperties().toString());
    }

}
