package com.caidaocloud.message.service.record.feign.callback;

import com.caidaocloud.message.service.record.feign.ResourceFeign;
import com.caidaocloud.record.core.beans.AuthResourceUrl;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

/**
 * created by: FoAng
 * create time: 22/5/2024 4:37 下午
 */
@Component
public class ResourceFeignFallback implements ResourceFeign {

    @Override
    public Result<AuthResourceUrl> getResourceData(String url) {
        return Result.fail("获取资源失败");
    }
}
