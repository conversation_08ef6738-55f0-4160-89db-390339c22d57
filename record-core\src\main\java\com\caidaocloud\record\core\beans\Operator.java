package com.caidaocloud.record.core.beans;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class Operator {

    private Long operatorId;

	private Long empId;

	private String tenantId;

	public static Operator defaultOperator() {
		return new Operator().setOperatorId(-1L).setTenantId("-1").setEmpId(-1L);
	}
}
