package com.caidaocloud.message.service.domain.msg.entity;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.message.service.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;
import com.caidaocloud.message.service.domain.msg.repository.EmpMsgTemplateRelRepository;
import lombok.Data;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Data
@Service
public class EmpMsgTemplateRelDo extends BaseDomainDoImpl {
    /**
     * 员工
     */
    private String empId;
    /**
     * 模版
     */
    private String template;
    /**
     * 模版名称
     */
    private String templateTxt;

    private final static String IDENTIFIER = "entity.message.EmpMsgTemplateRel";

    @Resource
    private EmpMsgTemplateRelRepository empMsgTemplateRelRepository;

    @Override
    public BaseRepository getRepository() {
        return empMsgTemplateRelRepository;
    }

    @Override
    public String getDoIdentifier() {
        return IDENTIFIER;
    }

    @Override
    protected DataSimple build() {
        return new EmpMsgTemplateRelDo();
    }

    public List<EmpMsgTemplateRelDo> getListByEmpId(String empId) {
        return empMsgTemplateRelRepository.getListByEmpId(empId);
    }

    public List<EmpMsgTemplateRelDo> getListByEmpList(List<String> empList) {
        return empMsgTemplateRelRepository.getListByEmpList(empList);
    }

    public void deleteByEmpIdAndTemplateId(String template, List<String> empList) {
        if (null == empList || empList.isEmpty()) {
            return;
        }
        empMsgTemplateRelRepository.deleteByEmpIdAndTemplateId(template, empList);
    }

    public void batchInsert(List<EmpMsgTemplateRelDo> dataList) {
        empMsgTemplateRelRepository.batchInsert(dataList);
    }

    public void deleteTemplateId(String template) {
        empMsgTemplateRelRepository.deleteTemplateId(template);
    }

    public List<EmpMsgTemplateRelDo> getByEmpIds(List<String> empList) {
        return empMsgTemplateRelRepository.getListByEmpList(empList);
    }

    public List<String> selectByTemplate(String msgConfig, BasePage page) {
        return empMsgTemplateRelRepository.getListByEmpList(msgConfig, page);
    }

    public List<String> getEmpListByEmpIds(String msgConfig, BasePage page, List<String> empIdList) {
        return empMsgTemplateRelRepository.getEmpListByEmpIds(msgConfig, page, empIdList);
    }
}
