package com.caidaocloud.message.service.application.msg.dto;

import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.enums.NotificationCycleTypeEnum;
import com.caidaocloud.message.sdk.enums.NotificationMethodEnum;
import com.caidaocloud.message.service.application.common.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/15
 **/
@Data
@ApiModel("消息设置")
public class MessageConfigDto {

    @ApiModelProperty("业务id")
    private String bid;

    @ApiModelProperty("通知名称")
    private String name;

    @ApiModelProperty("通知类型")
    private NoticeType msgType;

    @ApiModelProperty("通知对象")
    private List<NoticeTarget> notifyObject;

    @ApiModelProperty("匹配条件")
    private Object condition;

    @ApiModelProperty("通知方式")
    private NotificationMethodEnum func;

    @ApiModelProperty("通知轮次")
    private NotificationCycleTypeEnum round;

    @ApiModelProperty("通知天数")
    private Integer day;

    @ApiModelProperty("发送时间")
    private Long sendTime;

    @ApiModelProperty("通知周期")
    private Integer loop;

    @ApiModelProperty("发送途径")
    private String channel;

    @ApiModelProperty("状态")
    private MsgConfigStatusEnum status;

    @ApiModelProperty("绑定消息模版bid")
    private String templateBid;

}
