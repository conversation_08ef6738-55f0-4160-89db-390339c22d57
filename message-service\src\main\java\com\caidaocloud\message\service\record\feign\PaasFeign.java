package com.caidaocloud.message.service.record.feign;

import com.caidaocloud.hrpaas.paas.common.dto.KvDto;
import com.caidaocloud.message.service.record.config.EnableLogFeignConfig;
import com.caidaocloud.message.service.record.feign.callback.PaasFeignFallback;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * created by: FoAng
 * create time: 22/5/2024 4:36 下午
 */
@FeignClient(value = "caidaocloud-hr-paas-service",
        fallback = PaasFeignFallback.class,
        configuration = EnableLogFeignConfig.class,
        contextId = "record-hr-paas-service")
public interface PaasFeign {

    @PostMapping(value = "/api/hrpaas/v1/kv/save")
    Result<KvDto> saveKv(@RequestBody KvDto kv);

    /**
     * 获取kv配置详情
     * @param key
     * @return
     */
    @GetMapping(value = "/api/hrpaas/v1/kv/detail")
    Result<String> getKv(@RequestParam String key);
}
