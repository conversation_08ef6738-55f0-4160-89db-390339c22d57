package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.hr.service.dto.EmpWorkInfoDto;
import com.caidaocloud.hr.service.vo.EmpPrivateInfoVo;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
    value = "caidaocloud-masterdata-service",
    fallback = MasterdataFeignClientFallback.class,
    configuration = FeignConfiguration.class,
    contextId = "MasterdataFeignClient"
)
@Deprecated
public interface MasterdataFeignClient {

    @GetMapping("/api/masterData/emp/v1/empWorkInfo")
    Result<EmpWorkInfoDto> getEmpWorkInfo(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime);

    @GetMapping("/api/masterData/emp/v1/getPrivateInfo")
    Result<EmpPrivateInfoVo> getPrivateInfo(@RequestParam("empId") String empId);

    @GetMapping("/api/masterData/emp/v1/hrbpByEmpId")
    Result<EmpWorkInfoDto> hrbpByEmpId(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime);

    @GetMapping("/api/masterData/emp/v1/leaderByEmpId")
    Result<EmpWorkInfoDto> leaderByEmpId(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime);

    @GetMapping("/api/masterData/emp/v1/orgLeaderByEmpId")
    Result<EmpWorkInfoDto> orgLeaderByEmpId(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime);

    @PostMapping("/api/masterData/emp/v1/getAllEmpList")
    Result<List<EmpWorkInfoDto>> getAllEmpList();

    @GetMapping("/api/masterData/emp/v1/getAllGuardianList")
    Result<List<EmpPrivateInfoVo>> getAllGuardianList();

    @GetMapping("/api/masterData/emp/v1/allHrbp")
    Result<List<EmpWorkInfoDto>> allHrbp(@RequestParam("dataTime") Long dataTime);

    @GetMapping("/api/masterData/emp/v1/allLeader")
    Result<List<EmpWorkInfoDto>> allLeader(@RequestParam("dataTime") Long dataTime);

    @GetMapping("/api/masterData/emp/v1/getEmpWorkInfoByEmpIds")
    Result<List<EmpWorkInfoDto>> getEmpInfoByEmpIds(@RequestParam("empIds") String empIds);

    @GetMapping("/api/masterData/emp/v1/getEmpInfoByEmpIds")
    Result<List<EmpWorkInfoDto>> getEmpWorkInfoByEmpIds(@RequestParam("empIds") String empIds,
        @RequestParam("dataTime") Long dataTime);

    @GetMapping("/api/masterData/emp/v1/getAllPrivateInfoByEmpIds")
    Result<List<EmpPrivateInfoVo>> getAllPrivateInfoByEmpIds(@RequestParam("empIds") String empIds);

}
