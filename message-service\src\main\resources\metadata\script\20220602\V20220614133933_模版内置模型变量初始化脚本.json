[{"action": "create", "entityDef": {"identifier": "entity.message.Placeholder", "name": "模版内置变量", "owner": "message", "label": "name", "timelineEnabled": false, "standardProperties": [{"property": "text", "name": "占位符显示名", "dataType": "String", "widgetType": "text", "rules": [{"type": "required", "value": "true"}]}, {"property": "name", "name": "占位符名称", "dataType": "String", "widgetType": "text", "rules": [{"type": "required", "value": "true"}]}, {"property": "model", "name": "模型", "dataType": "String", "widgetType": "text", "rules": [{"type": "required", "value": "true"}]}, {"property": "category", "name": "分类", "dataType": "String", "widgetType": "text", "rules": [{"type": "required", "value": "true"}]}, {"property": "varType", "name": "类型", "dataType": "Enum", "widgetType": "enum", "visible": true, "modifiable": false, "defaultValue": {"value": 0, "type": "Enum"}, "enumDef": [{"value": 0, "display": "查模型"}, {"value": 1, "display": "查消息"}]}, {"property": "prop", "name": "字段", "dataType": "String", "widgetType": "text", "rules": [{"type": "required", "value": "true"}]}, {"property": "custom", "name": "自定义字段", "dataType": "Boolean", "widgetType": "boolean"}]}}]