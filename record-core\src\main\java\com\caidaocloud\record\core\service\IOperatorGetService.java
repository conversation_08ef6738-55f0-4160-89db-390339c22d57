package com.caidaocloud.record.core.service;


import com.caidaocloud.record.core.beans.Operator;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import org.apache.commons.lang3.StringUtils;


public interface IOperatorGetService {

    /**
     * 可以在里面外部的获取当前登陆的用户，比如UserContext.getCurrentUser()
     * @return 转换成Operator返回
     */
    Operator getUser();

	/**
	 * 根据当前userId获取操作人员
	 *
	 * @param operatorId
	 * @return
	 */
	default Operator getUser(Long operatorId, String tenantId) {
		SecurityUserInfo securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
		if (securityUserInfo != null) {
			return new Operator().setOperatorId(operatorId)
					.setTenantId(StringUtils.isNotEmpty(securityUserInfo.getTenantId()) ? securityUserInfo.getTenantId() : tenantId)
					.setEmpId(0L);
		} else {
			return new Operator().setOperatorId(operatorId).setTenantId(tenantId);
		}
	}
}
