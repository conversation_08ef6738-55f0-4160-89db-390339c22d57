package com.caidaocloud.message.service.application.notice.dto;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.message.sdk.enums.NoticeBusinessEnum;
import com.caidaocloud.message.sdk.enums.NoticeMsgTypeEnum;
import com.caidaocloud.message.sdk.enums.NoticeType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Map;

/**
 * created by: FoAng
 * create time: 15/8/2024 10:54 上午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NoticeMsgDto extends BasePage implements Serializable {

    @ApiModelProperty("开始时间")
    private Long startTime;

    @ApiModelProperty("结束时间")
    private Long endTime;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("业务ID")
    private String businessId;

    @ApiModelProperty("类型")
    private NoticeMsgTypeEnum type;

    @ApiModelProperty("业务类型")
    private NoticeBusinessEnum businessType;

    @ApiModelProperty("是否已读")
    private Boolean markRead;

    @ApiModelProperty("扩展参数")
    private Map<String, Object> ext;
}
