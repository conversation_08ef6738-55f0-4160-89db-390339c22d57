package com.caidaocloud.record.core.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * created by: FoAng
 * create time: 29/5/2024 3:02 下午
 */
@Getter
public enum ResourceActionEnum {

    VIEW("查看"),
    ADD("新增"),
    IMPORT("导入"),
    DOWNLOAD("下载"),
    EDIT("编辑"),
    DELETE("删除");

    private final String resourceCategory;

    ResourceActionEnum(String resourceCategory) {
        this.resourceCategory = resourceCategory;
    }

    public static ResourceActionEnum resource(String resourceAction) {
        return Arrays.stream(values()).filter(it -> it.name().equalsIgnoreCase(resourceAction)).findFirst()
                .orElse(ResourceActionEnum.VIEW);
    }
}
