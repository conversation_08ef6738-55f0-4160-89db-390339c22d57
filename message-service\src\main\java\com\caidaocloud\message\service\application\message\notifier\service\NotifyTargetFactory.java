package com.caidaocloud.message.service.application.message.notifier.service;

import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class NotifyTargetFactory {
    public static final String PREFIX_ROLE = "Role$",
        ROLE_NOTIFY_TARGET = "AuthRole",
        PREFIX_WORKFLOW_APPROVER = "WorkflowApprover$",
        WORKFLOW_APPROVER_TARGET = "WorkflowApprover",
        ENTITY_PAAS_TARGET = "EmpRecruiter",
        PREFIX_CUSTOMIZED_TARGET = "CustomizedTarget_",
        CUSTOMIZED_TARGET = "CustomizedTarget";

    public static INotifyTargetService getNotifyTargetService(String notifyObj){
        INotifyTargetService service = INotifyTargetService.notifier.get(notifyObj);
        if(null == service){
            log.warn("Unsupported or unknown notification object. notifyTargetType={}", notifyObj);
        }

        return service;
    }

    public static List<NotifierDto> getNotifier(String notifyObjType, NotifyObjectDto notify){
        List<NotifierDto> objects = Lists.newArrayList();
        getNotifyTargetService(notifyObjType).getNotifier(objects, notify);
        return objects;
    }

    public static List<NotifierDto> getDistinctNotifier(List<String> noticeTargetList, NotifyObjectDto notify){
        List<NotifierDto> objects = Lists.newArrayList();
        if(null == noticeTargetList || noticeTargetList.isEmpty()){
            return objects;
        }

        List<String> roleIdList = new ArrayList<>(), workflowApproverList = new ArrayList<>();
        List<String> customized = Lists.newArrayList();
        noticeTargetList.forEach(notifyObjType -> {
            if(notifyObjType.indexOf(PREFIX_ROLE) > -1){
                roleIdList.add(notifyObjType.replace(PREFIX_ROLE, ""));
            } else if(notifyObjType.indexOf(PREFIX_WORKFLOW_APPROVER) > -1){
                workflowApproverList.add(notifyObjType.replace(PREFIX_WORKFLOW_APPROVER, ""));
            } else if(notifyObjType.indexOf(PREFIX_CUSTOMIZED_TARGET) > -1){
                customized.add(notifyObjType);
            }else{
                getNotifyTargetService(notifyObjType).getNotifier(objects, notify);
            }
        });

        log.info("获取发送消息对应人：{}",objects);

        if(!roleIdList.isEmpty()){
            notify.setEmpIds(roleIdList);
            getNotifyTargetService(ROLE_NOTIFY_TARGET).getNotifier(objects, notify);
        }
        if(!workflowApproverList.isEmpty()){
            notify.setEmpIds(workflowApproverList);
            getNotifyTargetService(WORKFLOW_APPROVER_TARGET).getNotifier(objects, notify);
        }
        if(!customized.isEmpty()){
            notify.setEmpIds(customized);
            getNotifyTargetService(CUSTOMIZED_TARGET).getNotifier(objects, notify);
        }
        // 排重
        Map<String, String> keyMap = new HashMap();
        List<NotifierDto> unique = new ArrayList<>(objects.size());
        String empId = null;
        for (NotifierDto notifier : objects){
            empId = notifier.getEmpId();
            if(null == empId){
                unique.add(notifier);
                continue;
            }

            if(keyMap.containsKey(empId)){
                continue;
            }

            keyMap.put(empId, empId);
            unique.add(notifier);
        }

        return unique;
    }

}
