package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.hr.service.dto.EmpDataQueryDto;
import com.caidaocloud.hr.service.dto.EmpFileAttachmentDto;
import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.hr.service.vo.organization.company.CompanyVo;
import com.caidaocloud.hr.service.vo.organization.company.org.OrgVo;
import com.caidaocloud.hr.service.vo.organization.post.PostVo;
import com.caidaocloud.message.service.interfaces.vo.msg.MsgImportDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class HrFeignClientFallback implements HrFeignClient{

    @Override
    public Result<List<EmpDataVo>> getEmpInfos(EmpDataQueryDto empDataQuery) {
        return Result.fail();
    }

    @Override
    public Result<EmpWorkInfoVo> getEmpWorkInfo(String empId, Long dataTime) {
        return Result.fail();
    }

    @Override
    public Result<List<EmpWorkInfoVo>> getEmpListByEmpIds(List<String> empIds) {
        return Result.fail();
    }

    @Override
    public Result<CompanyVo> getCompanyById(String bid) {
        return Result.fail();
    }

    @Override
    public Result<OrgVo> getOrgById(String bid, Long dataTime) {
        return Result.fail();
    }

    @Override
    public Result<String> getApprovers(String empId, String settingBids,boolean isFormOnboarging) {
        return Result.fail();
    }

    @Override
    public Result<List<Map>> getApproverSettings() {
        return Result.fail();
    }

    @Override
    public Result<EmpFileAttachmentDto> getEmpFileAttachment(String empId) {
        return Result.fail();
    }

    @Override
    public Result<List<MsgImportDto>> getEmpListByWorkno(List<String> worknoList) {
        return Result.fail();
    }

    @Override
    public Result<Map> getEmpTransferRecord(String empId) {
        return Result.fail();
    }

    @Override
    public Result<PostVo> getPost(String bid, Long dataTime) {
        return Result.fail();
    }
}
