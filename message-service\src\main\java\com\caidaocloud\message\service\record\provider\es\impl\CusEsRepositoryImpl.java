package com.caidaocloud.message.service.record.provider.es.impl;

import com.caidaocloud.message.service.record.bean.LogEsRecord;
import com.caidaocloud.message.service.record.provider.es.CusEsRepository;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.zxp.esclientrhl.index.ElasticsearchIndex;

import javax.annotation.Resource;

/**
 * created by: FoAng
 * create time: 27/5/2024 3:32 下午
 */
@Slf4j
@Component
public class CusEsRepositoryImpl implements CusEsRepository {

    @Resource
    private ElasticsearchIndex<LogEsRecord> elasticsearchIndex;

    @Override
    public boolean createEsIndex() {
        try {
            if (elasticsearchIndex.exists(LogEsRecord.class)) {
                return true;
            }
            elasticsearchIndex.createIndex(LogEsRecord.class);
            log.info("[es] create log record searchIndex success.....");
            return true;
        } catch (Exception e) {
            log.error("[es] create index error, {}", e.getMessage(), e);
        }
        return false;
    }

    @Override
    public void createEsIndex(String tenantId) {
        try {
            SecurityUserInfo securityUserInfo = new SecurityUserInfo();
            securityUserInfo.setTenantId(tenantId);
            securityUserInfo.setUserId(0L);
            securityUserInfo.setIsAdmin(false);
            securityUserInfo.setEmpId(0L);
            SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
            if (elasticsearchIndex.exists(LogEsRecord.class)) {
                log.info("[es] searchIndex already exists.....");
                return;
            }
            elasticsearchIndex.createIndex(LogEsRecord.class);
            log.info("[es] create log record searchIndex success.....");
        } catch (Exception e) {
            log.error("[es] createEsIndex failed, msg:{}", e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }
}
