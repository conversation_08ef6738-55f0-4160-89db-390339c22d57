package com.caidaocloud.message.service.application.common.service;

import com.caidaocloud.message.service.application.log.dto.MessageLogDto;
import com.caidaocloud.message.service.application.email.dto.EmailTmplateMessage;
import com.caidaocloud.message.service.application.email.dto.HtmlMailDto;

public interface RemoteAsynSendMailService {

    void send(HtmlMailDto sendMailUtil, String tenantId, EmailTmplateMessage tmplateMessage);

    @Deprecated
    void sendMail(HtmlMailDto sendMailUtil, String tenantId);

    void sendBatchMail(HtmlMailDto sendMailUtil, EmailTmplateMessage[] emails, String tenantId);

    String saveOrUpdateEmailRecord(MessageLogDto messageLogDto);
}
