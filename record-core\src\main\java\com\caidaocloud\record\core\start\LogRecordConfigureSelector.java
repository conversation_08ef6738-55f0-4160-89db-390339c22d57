package com.caidaocloud.record.core.start;

import com.caidaocloud.record.core.annotation.EnableLogRecord;
import com.caidaocloud.record.core.configure.LogRecordProxyAutoConfiguration;
import com.caidaocloud.record.core.configure.WebInterceptConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.AdviceMode;
import org.springframework.context.annotation.AdviceModeImportSelector;
import org.springframework.lang.Nullable;

/**
 * 配置configure选择器
 */
@Slf4j
public class LogRecordConfigureSelector extends AdviceModeImportSelector<EnableLogRecord> {

    private static final String ASYNC_EXECUTION_ASPECT_CONFIGURATION_CLASS_NAME =
            "org.springblade.core.log.starter.configuration.LogRecordProxyAutoConfiguration";
    private static final String REST_INTERCEPT_CLASS_NAME =
            "com.caidaocloud.record.core.configure.WebInterceptConfiguration";

    @Override
    @Nullable
    public String[] selectImports(AdviceMode adviceMode) {
		log.info("logRecord auto config start .....");
        switch (adviceMode) {
            case PROXY:
                return new String[]{LogRecordProxyAutoConfiguration.class.getName(), WebInterceptConfiguration.class.getName()};
            case ASPECTJ:
                return new String[]{ASYNC_EXECUTION_ASPECT_CONFIGURATION_CLASS_NAME, REST_INTERCEPT_CLASS_NAME};
            default:
                return null;
        }
    }
}
