package com.caidaocloud.message.service.domain.announcement.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.Data;

@Data
public class AnnouncementReceive extends DataSimple {

    private String announcementId;

    private EnumSimple receiveType;

    private String receiveValue;

    public static String identifier = "entity.message.AnnouncementReceive";

    public AnnouncementReceive() {
        setIdentifier(identifier);
        setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        setCreateTime(System.currentTimeMillis());
        setCreateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    }
}