package com.caidaocloud.message.service.application.sms.service;

import com.caidaocloud.message.service.application.feign.SmsAdapterFeignClient;
import com.caidaocloud.message.service.application.sms.dto.SmsMessageDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AdapterSmsNotifyService implements SmsNotifyService{
    @Resource
    private SmsAdapterFeignClient smsClient;

    @Override
    public String getMsgType() {
        return "adapter";
    }

    @Override
    public boolean sendMsg(SmsMsgSender sender) {
        SmsMessageDto sms = SmsMessageDto.bulid().setContent(sender.getContent()).addPhone(sender.getPhone(), sender.getArea());
        Result<String> smsResult = smsClient.sendMesage(sms);
        return null != smsResult && smsResult.isSuccess();
    }

    @Override
    public boolean batchSendMsg(List<SmsMsgSender> senderList) {
        if(null == senderList && senderList.isEmpty()){
            return true;
        }

        for (SmsMsgSender sender : senderList) {
            sendMsg(sender);
        }

        return true;
    }

}
