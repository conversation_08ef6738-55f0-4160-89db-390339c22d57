package com.caidaocloud.message.service.application.email.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.service.application.common.constant.LangCodeConstant;
import com.caidaocloud.message.service.application.common.enums.MsgType;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import com.caidaocloud.message.service.application.common.service.RemoteAsynSendMailService;
import com.caidaocloud.message.service.application.email.dto.EmailTmplateMessage;
import com.caidaocloud.message.service.application.email.dto.HtmlMailDto;
import com.caidaocloud.message.service.application.log.dto.MessageLogDto;
import com.caidaocloud.message.service.application.log.repository.IMessageLogRepository;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.domain.email.dto.LogEmailDto;
import com.caidaocloud.message.service.domain.email.service.LogEmailDomainService;
import com.caidaocloud.message.service.domain.msg.entity.EmailConfigDo;
import com.caidaocloud.message.service.infrastructure.utils.LangUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Slf4j
@Service
public class AsynSendMailService implements RemoteAsynSendMailService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsynSendMailService.class);
    private static final String SYS_CONFIG_EMAIL_ = "SYS_CONFIG_EMAIL_DEFAULT_";
    @Autowired
    private LogEmailDomainService logEmailDomainService;

    //阿里云mns是否启用
    @Value("${aliyun.mns.open:false}")
    private boolean open;

    //阿里云mns accountendpoint
    @Value("${aliyun.mns.accountendpoint:}")
    private String accountendpoint;

    //阿里云mns accesskeyid
    @Value("${aliyun.mns.accesskeyid:}")
    private String accesskeyid;

    //阿里云mns accesskeysecret
    @Value("${aliyun.mns.accesskeysecret:}")
    private String accesskeysecret;

    //阿里云mns accesskeysecret
    @Value("${SYS_TYPE:}")
    private String SYS_TYPE;

    @Resource
    private CacheService cacheService;

    @Resource
    private IMessageLogRepository messageLogRepository;

    @Override
    public void send(HtmlMailDto sendMailUtil, String tenantId, EmailTmplateMessage tmplateMessage) {
        if (StringUtils.isBlank(tmplateMessage.getTo())) {
            LOGGER.info("无收件人邮箱则跳过。。。");
            return;
        }

        String key = SYS_CONFIG_EMAIL_ + tenantId;
        String value = cacheService.getValue(key);
        EmailConfigDo cfgEmail = FastjsonUtil.toObject(value, EmailConfigDo.class);

        if ((cfgEmail == null || (cfgEmail != null && !cfgEmail.isDeleted())) && open) {
            LOGGER.info("------------调用阿里Queue,暂时未实现存放邮件----------------------");
        } else {
            LOGGER.info("------------调用sendEmail开始准备发送邮件----------------------");
            LOGGER.info("-----sendMailConfig:" + FastjsonUtil.toJson(sendMailUtil));
            sendEmail(sendMailUtil, tmplateMessage, tenantId);
        }
    }

    public synchronized void sendEmail(HtmlMailDto sendMailUtil, EmailTmplateMessage tmplateMessage, String tenantId) {
        modifyConfigEmail(sendMailUtil, tenantId);
        LogEmailDto sendEmailLog = new LogEmailDto();
        EnumSimple status = new EnumSimple();
        status.setValue("1");
        status.setText("success");
        sendEmailLog.setStatus(status);
        tmplateMessage.setTenantId(tenantId);

        String bid;
        MessageLogDto messageLogDto = new MessageLogDto();
        try {
            messageLogDto.setType(MsgType.EMAIL);
            messageLogDto.setReceiver(tmplateMessage.getTo());
            messageLogDto.setMessage(FastjsonUtil.toJson(tmplateMessage));
            messageLogDto.setStatus(SendStatusEnum.SUCESS);
            bid = saveOrUpdateEmailRecord(messageLogDto);
        } catch (Exception e) {
            log.error("saveOrUpdateEmailRecord occur error, {} \n {}", e.getMessage(), e);
            String msg = LangUtil.getMsg(LangCodeConstant.code_70006);
            throw new ServerException(msg);
        }
        tmplateMessage.setLogEmailId(bid);
        try {
            LOGGER.info("开始发送：{}", tmplateMessage.getTo());
            sendMailUtil.send2(tmplateMessage);
            LOGGER.info("发件人：" + sendMailUtil.getFrom() + " 收件人：" + tmplateMessage.getTo());
        } catch (Exception e) {
            LOGGER.error("sendEmail err,{}", e.getMessage(), e);
            //发送邮件失败，记录发送失败日志to db
            messageLogDto.setStatus(SendStatusEnum.FAIL);
            messageLogDto.setErrorMsg(e.getMessage());
            saveOrUpdateEmailRecord(messageLogDto);
            LOGGER.info("发送邮件失败，msg=" + e.getMessage());
            throw new ServerException("发送邮件失败");
        }
    }

    /**
     * 废除，不能循环调用
     *
     * @param sendMailUtil
     * @param tenantId
     */
    @Override
    public void sendMail(HtmlMailDto sendMailUtil, String tenantId) {
        try {
            if (StringUtils.isBlank(sendMailUtil.getTo())) {
                throw new ServerException("收件人邮箱不能为空");
            }

            modifyConfigEmail(sendMailUtil, tenantId);
            EmailTmplateMessage tmplateMessage = new EmailTmplateMessage();
            tmplateMessage.setTo(sendMailUtil.getTo());
            tmplateMessage.setContent(sendMailUtil.getContent());
            tmplateMessage.setSubject(sendMailUtil.getSubject());
            tmplateMessage.setTenantId(tenantId);
            this.send(sendMailUtil, tenantId, tmplateMessage);
        } catch (Exception e) {
            LOGGER.error("Deprecated sendMail err,{}", e.getMessage(), e);
        }
    }

    @Override
    public void sendBatchMail(HtmlMailDto sendMailUtil, EmailTmplateMessage[] emails, String tenantId) {
        int i = 0;
        try {
            LOGGER.info("Send Batch Mail-----------------------" + emails.length);
            if (emails != null && emails.length > 0) {
                //modifyConfigEmail(sendMailUtil, emails[0].getBelongId());
                for (i = 0; i < emails.length; i++) {
                    EmailTmplateMessage tmplateMessage = emails[i];
                    this.send(sendMailUtil, tmplateMessage.getTenantId(), tmplateMessage);
                    Thread.sleep(500);
                }
            }
        } catch (Exception e) {
            if (i > 0) {
                i--;
            }

            for (i = 0; i < emails.length; i++) {
                MessageLogDto messageLogDto = new MessageLogDto();
                messageLogDto.setErrorMsg("抛出异常，中断发送！ " + i + ":" + e.getMessage());
                messageLogDto.setStatus(SendStatusEnum.FAIL);
                saveOrUpdateEmailRecord(messageLogDto);
            }
            LOGGER.error("sendBatchMail err,{}", e.getMessage(), e);
        }
    }

    /**
     * 切换邮箱账号
     *
     * @param sendMailUtil
     * @param tenantId
     */
    private void modifyConfigEmail(HtmlMailDto sendMailUtil, String tenantId) {
        String key = null;
        EmailConfigDo cfgEmail = null;
        tenantId = tenantId == null ? UserContext.getTenantId() : tenantId;
        if (tenantId == null) {
            key = SYS_CONFIG_EMAIL_ + 0;
            String cacheValue = cacheService.getValue(key);
            cfgEmail = FastjsonUtil.toObject(cacheValue, EmailConfigDo.class);
        } else {
            key = SYS_CONFIG_EMAIL_ + tenantId;
            String cacheValue = cacheService.getValue(key);
            cfgEmail = FastjsonUtil.toObject(cacheValue, EmailConfigDo.class);
            if (cfgEmail == null || (cfgEmail != null && !cfgEmail.isDeleted())) {
                key = SYS_CONFIG_EMAIL_ + 0;
                cacheValue = cacheService.getValue(key);
                cfgEmail = FastjsonUtil.toObject(cacheValue, EmailConfigDo.class);
            }
        }
        if (cfgEmail != null) {
            sendMailUtil.setPassword(cfgEmail.getEmailPwd());
            sendMailUtil.setSmtp(cfgEmail.getEmailSmtp());
            sendMailUtil.setFrom(cfgEmail.getEmailFrom());
            sendMailUtil.setNick(cfgEmail.getEmailNick());
            if (cfgEmail.getEmailPort() != null) {
                sendMailUtil.setPort(cfgEmail.getEmailPort().toString());
            }
        } else {
            sendMailUtil.setNick("CAIDAO ehr");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveOrUpdateEmailRecord(MessageLogDto messageLogDto) {
        String bid = messageLogRepository.saveOrUpdateOfLog(messageLogDto);
        messageLogDto.setBid(bid);
        return bid;
    }

}
