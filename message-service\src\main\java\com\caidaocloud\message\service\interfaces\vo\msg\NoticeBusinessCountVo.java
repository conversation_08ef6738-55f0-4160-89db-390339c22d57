package com.caidaocloud.message.service.interfaces.vo.msg;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 消息未读数量
 * created by: FoAng
 * create time: 21/8/2024 2:26 下午
 */
@Data
public class NoticeBusinessCountVo implements Serializable {

    @ApiModelProperty("人事通知")
    private int hr;

    @ApiModelProperty("审批通知")
    private int approval;

    @ApiModelProperty("事件通知")
    private int event;

    @ApiModelProperty("系统通知")
    private int system;

    @ApiModelProperty("政策通知")
    private int policy;

    @ApiModelProperty("其他")
    private int other;

}
