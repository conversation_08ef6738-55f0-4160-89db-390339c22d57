package com.caidaocloud.message.service.interfaces.facade;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.message.sdk.dto.MsgConfigItemVo;
import com.caidaocloud.message.sdk.enums.NoticeRule;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.enums.NotificationCycleTypeEnum;
import com.caidaocloud.message.sdk.enums.NotificationMethodEnum;
import com.caidaocloud.message.service.application.msg.dto.MessageConfigPageQueryDto;
import com.caidaocloud.message.service.application.msg.event.CommonMsgPublish;
import com.caidaocloud.message.service.application.msg.service.EmpMsgTemplateRelService;
import com.caidaocloud.message.service.application.msg.service.MsgConfigService;
import com.caidaocloud.message.service.application.msg.service.NoticeTargetService;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.domain.msg.entity.MsgConfigDo;
import com.caidaocloud.message.service.infrastructure.utils.ExcelUtils;
import com.caidaocloud.message.service.infrastructure.utils.LangUtil;
import com.caidaocloud.message.service.interfaces.dto.msg.MsgConfigDto;
import com.caidaocloud.message.service.interfaces.dto.msg.MsgConfigQueryDto;
import com.caidaocloud.message.service.interfaces.dto.template.EmpMsgConfigDto;
import com.caidaocloud.message.service.interfaces.vo.base.ConditionVo;
import com.caidaocloud.message.service.interfaces.vo.msg.*;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.vo.ImportExcelProcessVo;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.caidaocloud.message.service.application.common.constant.LangCodeConstant.*;

/**
 * MsgConfigController
 *
 * <AUTHOR>
 * @date 2022/6/6 下午6:06
 */
@Slf4j
@RestController
@RequestMapping("/api/msg/config/v1")
@Api(value = "/api/msg/config/v1", description = "消息设置", tags = "v0.2")
public class MsgConfigController {
    @Resource
    private MsgConfigService msgConfigService;
    @Resource
    private CacheService cacheService;
    @Resource
    private CommonMsgPublish commonMsgPublish;
    @Resource
    private EmpMsgTemplateRelService empMsgTemplateRelService;
    @Resource
    private NoticeTargetService noticeTargetService;

    @ApiOperation("通知种类")
    @GetMapping("/noticeTypeSelectList")
    public Result<List<KeyValue>> getNoticeType() {
        List<KeyValue> collect = Arrays.stream(NoticeType.values()).map(type -> {
            KeyValue kv = new KeyValue();
            kv.setText(type.getName());
            kv.setValue(type);
            return kv;
        }).collect(Collectors.toList());
        return Result.ok(collect);
    }

    @ApiOperation("通知规则下拉列表")
    @GetMapping("/rule/list")
    public Result<List<KeyValue>> getRuleList() {
        List<KeyValue> collect = Arrays.stream(NoticeRule.values()).map(type -> {
            KeyValue kv = new KeyValue();
            kv.setText(type.text);
            kv.setValue(type);
            return kv;
        }).collect(Collectors.toList());
        return Result.ok(collect);
    }


    @ApiOperation("通知对象")
    @GetMapping("/noticeTargetSelectList")
    public Result<List<KeyValue>> getNoticeTarget() {
        return Result.ok(noticeTargetService.getNoticeTarget());
    }

    @ApiOperation("抄送对象")
    @GetMapping("/copyTargetSelectList")
    public Result<List<KeyValue>> getCopyTarget() {
        return Result.ok(msgConfigService.getCopyTarget());
    }

    @ApiOperation(value = "分组匹配条件")
    @GetMapping("/condition")
    public Result<List<ConditionVo>> conditionList() {
        List list = msgConfigService.getConditionListV2();
        return Result.ok(ObjectConverter.convertList(list, ConditionVo.class));
    }

    @ApiOperation(value = "获取启用的消息配置")
    @GetMapping("/enable")
    public Result<MsgConfigVo> getEnableMsgConfig(@RequestParam("type")
                                                  @ApiParam(value = "通知种类") NoticeType type) {
        return Result.ok(msgConfigService.getEnabledMsgConfigByType(type));
    }

    @ApiOperation(value = "获取员工对应的消息配置")
    @PostMapping("/queryList")
    public Result<List<MsgConfigVo>> getMsgConfig(@RequestBody EmpMsgConfigDto queryDto) {
        return Result.ok(msgConfigService.getMsgConfig(queryDto.getType(), queryDto.getEmpList()));
    }

    @ApiOperation(value = "获取启用的消息配置列表")
    @GetMapping("/configList")
    public Result<List<MsgConfigVo>> getEnableMsgConfigList(@RequestParam("type") @ApiParam(value = "通知种类") String type) {
        return Result.ok(msgConfigService.getEnableMsgConfigList(type));
    }

    @ApiOperation(value = "获取启用的消息配置列表")
    @GetMapping("/configListSequence")
    public Result<String> getEnableMsgConfigListSequence(@RequestParam("type") @ApiParam(value = "通知种类") String type) {
        List<MsgConfigVo> list = msgConfigService.getEnableMsgConfigList(type);
        if (CollectionUtils.isEmpty(list)) {
            return Result.ok(null);
        }
        return Result.ok(FastjsonUtil.toJson(list));
    }

    @ApiOperation(value = "获取消息配置下拉列表")
    @GetMapping("/selectList")
    public Result<List<KeyValue>> selectList(@RequestParam(value = "type", required = false) @ApiParam(value = "通知种类") String type) {
        List<MsgConfigVo> configList = msgConfigService.selectList(type);
        return Result.ok(convertKeyValue(configList));
    }

    @ApiOperation(value = "获取消息配置下拉列表")
    @GetMapping("/msgSelectList")
    public Result<List<KeyValue>> getMsgConfigSelectList(@RequestParam("type") @ApiParam(value = "通知种类") NoticeType type) {
        List<MsgConfigVo> configList = msgConfigService.getEnableMsgConfigList(type.getIndex());
        return Result.ok(convertKeyValue(configList));
    }

    private List<KeyValue> convertKeyValue(List<MsgConfigVo> configList) {
        List<KeyValue> list = configList.stream().map(config -> {
            KeyValue kv = new KeyValue();
            kv.setText(config.getName());
            kv.setValue(config.getBid());
            return kv;
        }).collect(Collectors.toList());
        return list;
    }

    @ApiOperation(value = "获取启用的消息配置分页列表")
    @PostMapping("/configPageList")
    public Result<PageResult<MsgConfigVo>> getEnableMsgConfigPageList(@RequestBody MessageConfigPageQueryDto queryDto) {
        return Result.ok(msgConfigService.getEnableMsgConfigPageList(queryDto));
    }

    @ApiOperation(value = "新增")
    @PostMapping("/save")
    @LogRecordAnnotation(menu = "管理中心-消息管理-消息设置", category = "新增", success = "新增了{{#msgConfigDto.getTemplateName()}}")
    public Result save(@RequestBody MsgConfigDto msgConfigDto) {
        msgConfigDto.setBid(null);
        msgConfigService.saveOrUpdateObj(msgConfigDto);
        return Result.ok(true);
    }

    @ApiOperation(value = "编辑")
    @PostMapping("/update")
    @LogRecordAnnotation(menu = "管理中心-消息管理-消息设置", category = "编辑", success = "编辑了{{#msgConfigDto.getTemplateName()}}")
    public Result update(@RequestBody MsgConfigDto msgConfigDto) {
        PreCheck.preCheckNotNull(msgConfigDto.getBid(), LangUtil.getMsg(code_70007));
        msgConfigService.saveOrUpdateObj(msgConfigDto);
        return Result.ok(true);
    }

    @ApiOperation("手动刷新匹配关系")
    @GetMapping("/refresh")
    public Result refresh(@RequestParam(value = "bid", required = false) String bid) {
        msgConfigService.refresh(bid);
        return Result.success();
    }

    @ApiOperation("刷新员工模版关系")
    @GetMapping("/singleRefresh")
    public Result templateRelRefresh(@RequestParam(required = false, value = "bid") String bid) {
        String progress = UUID.randomUUID().toString().replaceAll("-", "");
        ImportExcelProcessVo processObj = new ImportExcelProcessVo(progress, 300, 0, 0, 0, 0);
        String tenantId = UserContext.getTenantId();
        String progressKey = String.format("template_rel_refresh_progress_%s_%s", tenantId, progress);
        cacheService.cacheValue(progressKey, FastjsonUtil.toJson(processObj), 1800);
        commonMsgPublish.templateRefreshMessagePublish(bid, tenantId, UserContext.getUserId(), progress);
        return Result.ok(progress);
    }

    @ApiOperation("员工模版关系刷新进度")
    @GetMapping("/progress")
    public Result<ImportExcelProcessVo> progress(@RequestParam(required = false, value = "progress") String progress) {
        String tenantId = UserContext.getTenantId();
        String progressKey = String.format("msg_template_rel_refresh_progress_%s_%s", tenantId, progress);
        String value = cacheService.getValue(progressKey);
        if (StringUtil.isEmpty(value)) {
            return Result.ok(new ImportExcelProcessVo(progress, 300, 300, 0, 300, 0));
        }

        return Result.ok(FastjsonUtil.toObject(value, ImportExcelProcessVo.class));
    }

    @ApiOperation(value = "列表")
    @PostMapping("/list")
    public Result<PageResult<MsgConfigListVo>> list(@RequestBody MsgConfigQueryDto msgConfigQueryDto) {
        PageResult<MsgConfigDo> page = msgConfigService.getPage(msgConfigQueryDto);
        List<MsgConfigDo> items = page.getItems();
        Map<String, String> notifyObjectMap = null != items && !items.isEmpty() ?
                noticeTargetService.getNotifyObjectMap() : new HashMap<>();
        return Result.ok(new PageResult<>(msgConfigService.toVoList(items, notifyObjectMap), page.getPageNo(),
                page.getPageSize(), page.getTotal()));
    }

    @ApiOperation("主键获取列表")
    @GetMapping("/list/ids")
    public Result<List<MsgConfigItemVo>> listByBids(@RequestParam("bids") @ApiParam("消息设置bids") String bids) {
        List<MsgConfigDo> configDoList = msgConfigService.selectByIds(Arrays.asList(bids.split(",")));
        List<MsgConfigItemVo> configItemVoList = Optional.ofNullable(configDoList)
                .map(it -> it.stream().map(o1 -> {
                    MsgConfigItemVo itemVo = ObjectConverter.convert(o1, MsgConfigItemVo.class);
                    // 消息类型
                    if (null != o1.getMsgType() && !StringUtils.isEmpty(o1.getMsgType().getValue())) {
                        NoticeType noticeType = NoticeType.getEnumByIndex(o1.getMsgType().getValue());
                        itemVo.setMsgType(noticeType);
                    }
                    // 通知方式
                    if (o1.getFunc() != null && StringUtils.isNotBlank(o1.getFunc().getValue())) {
                        NotificationMethodEnum funcType = NotificationMethodEnum.getEnumByVal(o1.getFunc().getValue());
                        itemVo.setFunc(funcType);
                    }
                    // 消息规则
                    itemVo.setRule(o1.getRule() == null || StringUtils.isEmpty(o1.getRule().getValue()) ? null :
                            NoticeRule.getByName(o1.getRule().getValue()));
                    // 循环方式
                    NotificationCycleTypeEnum type = NotificationCycleTypeEnum.getByValue(o1.getRound().getValue());
                    if (type == null) {
                        itemVo.setRound(null);
                        log.warn("round of msg config is empty");
                        return itemVo;
                    }
                    itemVo.setRound(type);
                    itemVo.setDayTxt(type.format(o1.getDay(), "天"));
                    return itemVo;
                }).collect(Collectors.toList())).orElse(Lists.newArrayList());
        return Result.ok(configItemVoList);
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail")
    @LogRecordAnnotation(menu = "管理中心-消息管理-消息设置", category = "查看", success = "查看了{{#detail.name}}")
    public Result<MsgConfigDetailVo> detail(@RequestParam("bid") @ApiParam(value = "消息设置bid") String bid) {
        MsgConfigDetailVo detail = msgConfigService.getDetail(bid);
        LogRecordContext.putVariable("detail", detail);
        return Result.ok(detail);
    }

    @ApiOperation(value = "复制")
    @PostMapping("/copy")
    public Result copy(@RequestBody MsgConfigDto dto) {
        PreCheck.preCheckNotNull(dto.getBid(), LangUtil.getFormatMsg(code_70000, LangUtil.getMsg(code_70008)));
        msgConfigService.copy(dto.getBid());
        return Result.ok(true);
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/delete")
    @LogRecordAnnotation(menu = "管理中心-消息管理-消息设置", category = "删除", success = "删除了{msgName{#bid}}")
    public Result delete(@RequestParam("bid") @ApiParam(value = "消息设置bid") String bid) {
        // PreCheck.preCheckNotNull(msgConfigDto.getBid(), LangUtil.getMsg(code_70007));
        // msgConfigService.saveOrUpdateObj(msgConfigDto);
        empMsgTemplateRelService.deleteTemplateId(bid);
        msgConfigService.delete(bid);
        return Result.ok(true);
    }

    @ApiOperation(value = "启用")
    @PostMapping("/enable")
    @LogRecordAnnotation(menu = "管理中心-消息管理-消息设置", category = "启用", success = "启用了{msgName{#dto.bid}}")
    public Result enable(@RequestBody MsgConfigDto dto) {
        PreCheck.preCheckNotNull(dto.getBid(), LangUtil.getFormatMsg(code_70000, LangUtil.getMsg(code_70008)));
        msgConfigService.enable(dto.getBid());
        return Result.ok(true);
    }

    @ApiOperation(value = "停用")
    @PostMapping("/disable")
    @LogRecordAnnotation(menu = "管理中心-消息管理-消息设置", category = "停用", success = "停用了{msgName{#dto.bid}}")
    public Result disable(@RequestBody MsgConfigDto dto) {
        PreCheck.preCheckNotNull(dto.getBid(), LangUtil.getFormatMsg(code_70000, LangUtil.getMsg(code_70008)));
        msgConfigService.disable(dto.getBid());
        return Result.ok(true);
    }

    @ApiOperation("验证批量导入人员信息")
    @PostMapping("/import")
    public Result<MsgImportCheckVo> importPerson(MultipartFile file) {
        List<MsgImportDto> dataList = null;
        try {
            dataList = ExcelUtils.importNoTitleExcel(file, MsgImportDto.class);
        } catch (Exception e) {
            log.error("importSignCheck err,{}", e.getMessage(), e);
        }

        return Result.ok(msgConfigService.importCheck(dataList));
    }


}
