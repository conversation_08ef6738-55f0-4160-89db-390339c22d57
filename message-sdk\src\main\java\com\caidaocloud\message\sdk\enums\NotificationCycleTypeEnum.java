package com.caidaocloud.message.sdk.enums;

/**
 *
 * <AUTHOR>
 * @date  2022/6/15
 **/
public enum NotificationCycleTypeEnum {

    ADVANCE("提前", "0"){
        @Override
        public String format(Integer day, String unit) {
            StringBuilder sb = new StringBuilder();
            return sb.append(txt).append(day).append(unit).toString();
        }
    },
    TODAY("当天", "1"){
        @Override
        public String format(Integer day, String unit) {
            return txt;
        }
    },
    DELAY("延后", "2"){
        @Override
        public String format(Integer day, String unit) {
            StringBuilder sb = new StringBuilder();
            return sb.append(txt).append(day).append(unit).toString();
        }
    };

    public String txt;

    public String value;

    NotificationCycleTypeEnum(String txt, String value) {
        this.txt = txt;
        this.value = value;
    }

   public abstract String format(Integer day,String unit);

    public static NotificationCycleTypeEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (NotificationCycleTypeEnum e : NotificationCycleTypeEnum.values()) {
            if (e.value.equals(value)) {
                return e;
            }
        }
        return null;
    }

    @Deprecated
    public static NotificationCycleTypeEnum getbyName(String value) {
        if (value == null) {
            return null;
        }
        for (NotificationCycleTypeEnum e : NotificationCycleTypeEnum.values()) {
            if (e.value.equals(value)) {
                return e;
            }
        }
        return null;
    }

    public static NotificationCycleTypeEnum getByName(String value) {
        if (value == null) {
            return null;
        }
        for (NotificationCycleTypeEnum e : NotificationCycleTypeEnum.values()) {
            if (e.name().equals(value)) {
                return e;
            }
        }
        return null;
    }
}
