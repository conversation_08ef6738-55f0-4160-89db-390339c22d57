package com.caidaocloud.record.core.configure;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigurationImportFilter;
import org.springframework.boot.autoconfigure.AutoConfigurationMetadata;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * created by: FoAng
 * create time: 2021-11-10 14:19
 */
@Slf4j
public class MyExclusionFilter implements AutoConfigurationImportFilter {

	private static final Set<String> SHOULD_SKIP = new HashSet<>(
		Arrays.asList("org.springblade.core.log.starter.configuration.LogRecordProxyAutoConfiguration",
				"com.caidaocloud.record.core.configure.WebInterceptConfiguration"));

	@Override
	public boolean[] match(String[] classNames, AutoConfigurationMetadata metadata) {
		boolean[] matches = new boolean[classNames.length];
		for(int i = 0; i< classNames.length; i++) {
			matches[i] = !SHOULD_SKIP.contains(classNames[i]);
		}
		return matches;
	}
}
