package com.caidaocloud.message.service.application.email.service;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.message.service.application.common.constant.CacheCodeConstant;
import com.caidaocloud.message.service.application.common.enums.MsgType;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import com.caidaocloud.message.service.application.common.service.RemoteAsynSendMailService;
import com.caidaocloud.message.service.application.email.dto.EmailTmplateMessage;
import com.caidaocloud.message.service.application.email.dto.HtmlMailDto;
import com.caidaocloud.message.service.application.email.dto.MailMsgSender;
import com.caidaocloud.message.service.application.log.dto.MessageLogDto;
import com.caidaocloud.message.service.application.log.repository.IMessageLogRepository;
import com.caidaocloud.message.service.application.msg.event.CommonMessage;
import com.caidaocloud.message.service.domain.msg.entity.EmailConfigDo;
import com.caidaocloud.message.service.domain.msg.service.EmailConfigDomainService;
import com.caidaocloud.message.service.infrastructure.utils.FileUtils;
import com.caidaocloud.message.service.infrastructure.utils.HolderUtil;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.oss.service.OssService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.util.List;

/**
 * SmtpEmailNotify
 * <AUTHOR>
 * @date 2022/6/6 下午3:26
 */
@Slf4j
@Service
public class SmtpEmailNotify implements EmailNotify {
    @Resource
    private EmailConfigDomainService configDomainService;
    @Resource
    private RemoteAsynSendMailService sendMailService;
    @NacosValue("${caidaocloud.msg.email.openNative:false}")
    private boolean openNative = false;
    @NacosValue("${caidaocloud.msg.email.cached:false}")
    private boolean cached = false;
    @NacosValue("${caidaocloud.msg.email.openSSl:true}")
    private boolean openSSl = true;
    @Resource
    private IMessageLogRepository messageLogRepository;
    @Resource
    private OssService ossService;
    @Resource
    private RedisLimiter redisLimiter;
    @Resource
    private MqMessageProducer<CommonMessage> producer;

    @Override
    public String getEmailType() {
        return "smtp";
    }

    @Override
    public boolean sendMsg(MailMsgSender sender) {
        String tenantId = sender.getTenantId();
        HtmlMailDto mailObj = getHtmlMail();
        if(openNative){
            sendEmailMsg(sender, mailObj);
            return true;
        }

        EmailTmplateMessage tmplateMessage = ObjectConverter.convert(sender, EmailTmplateMessage.class);
        sendMailService.send(mailObj, tenantId, tmplateMessage);
        return true;
    }

    private HtmlMailDto getHtmlMail(){
        Object config = HolderUtil.get(CacheCodeConstant.STMP_EMAIL_CONFIG);
        if(null != config){
            return (HtmlMailDto) config;
        }

        List<EmailConfigDo> configList = configDomainService.selectList();
        if(null == configList || configList.isEmpty()){
            throw new ServerException();
        }
        EmailConfigDo data = configList.get(0);
        HtmlMailDto mailUtil = new HtmlMailDto();
        mailUtil.setPassword(data.getEmailPwd());
        mailUtil.setSmtp(data.getEmailSmtp());
        mailUtil.setUser(data.getEmailFrom());
        mailUtil.setFrom(data.getEmailFrom());
        mailUtil.setNick(data.getEmailNick());
        mailUtil.setPort(String.valueOf(data.getEmailPort()));
        return mailUtil;
    }

    @Override
    public boolean batchSendMsg(List<MailMsgSender> senderList) {
        return false;
    }

    public void sendEmailMsg(MailMsgSender sender, HtmlMailDto mailObj){
        if(isLimit(sender, mailObj)){
            return;
        }

        emailSend(sender, mailObj);
    }

    private boolean isLimit(MailMsgSender sender, HtmlMailDto mailObj){
        // 每秒钟内只允许5次邮件发送
        boolean allowed = redisLimiter.isAllowed(mailObj.getFrom(), 5, 5, 1);
        if(!allowed){
            sender.setCycle(sender.getCycle() + 1);
            if(sender.getCycle() > 100){
                // 如果 100 次后，还不能发送，则丢弃
                log.error("The mailbox frequency limit has been exceeded, and the message has not been successfully sent after 100 minutes. The message is discarded. msgSender={}",
                        FastjsonUtil.toJson(sender));
                return true;
            }
            // 延迟 3s 发送
            publishDelayEmailMsg(sender, 3000);
            return true;
        }

        // 每分钟内只允许200次邮件发送
        allowed = redisLimiter.isAllowed(mailObj.getFrom(), 3, 180, 3);
        if(!allowed){
            sender.setCycle(sender.getCycle() + 1);
            if(sender.getCycle() > 100){
                // 如果 100 次后，还不能发送，则丢弃
                log.error("The mailbox frequency limit has been exceeded, and the message has not been successfully sent after 100 minutes. The message is discarded. msgSender={}",
                        FastjsonUtil.toJson(sender));
                return true;
            }
            // 延迟 10s 发送
            publishDelayEmailMsg(sender, 10000);
            return true;
        }

        return false;
    }

    private void emailSend(MailMsgSender sender, HtmlMailDto mailObj){
        MessageLogDto messageLogDto = new MessageLogDto();
        messageLogDto.setType(MsgType.EMAIL);
        messageLogDto.setReceiver(sender.getTo());
        messageLogDto.setMessage(FastjsonUtil.toJson(sender));
        messageLogDto.setStatus(SendStatusEnum.SUCESS);
        JavaMailSender jms = null;
        try {
            jms = MailSenderPool.borrowObj(mailObj.getSmtp(), mailObj.getPort(),
                    mailObj.getUser(), mailObj.getPassword(), null);
            // 邮件发送
            MimeMessage message= jms.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "utf-8");
            // 发信人
            helper.setFrom(mailObj.getFrom());
            // 收信人
            helper.setTo(sender.getTo().contains(";") ? sender.getTo().split(";") : new String[]{sender.getTo()});
            //抄送人
            if(StringUtil.isNotEmpty(sender.getCc())){
                helper.setCc(sender.getCc().contains(";") ? sender.getCc().split(";") : new String[]{sender.getCc()});
            }
            if (StringUtil.isNotEmpty(sender.getBcc())) {
                helper.setBcc(sender.getBcc().contains(";") ? sender.getBcc()
                        .split(";") : new String[] {sender.getBcc()});
            }
            // 主题
            helper.setSubject(sender.getSubject());
            // 内容
            helper.setText(sender.getContent(), true);
            // 加入附件
            addAttachments(helper, sender);
            // 发送
            String bid = messageLogRepository.saveOrUpdateOfLog(messageLogDto);
            messageLogDto.setBid(bid);
            jms.send(message);
            log.info("java mail sender = {} send success.", FastjsonUtil.toJson(sender));
        } catch (Exception e){
            log.error("java mail sender err,{}", e.getMessage(), e);
            messageLogDto.setErrorMsg(e.getMessage());
            messageLogDto.setStatus(SendStatusEnum.FAIL);
            messageLogRepository.saveOrUpdateOfLog(messageLogDto);
            throw new ServerException("邮件发送失败！");
        } finally {
            if(null != jms){
                try {
                    MailSenderPool.returnObj(mailObj.getSmtp(), jms, mailObj.getUser(), mailObj.getPassword(), mailObj.getPort());
                } catch (Exception ex){
                    log.error("MailSenderPool returnObj err,{}", ex.getMessage(), ex);
                }
            }
        }
    }

    @SneakyThrows
    private void addAttachments(MimeMessageHelper helper, MailMsgSender sender) {
        if (StringUtils.isEmpty(sender.getAffix())) {
            return;
        }
        String[] urls = sender.getAffix().split(",");
        String[] names = sender.getAffixName().split(",");
        for (int i = 0; i < urls.length; i++) {
            String url = urls[i];
            String name = names[i];
            helper.addAttachment(name, new ByteArrayResource(FileUtils.inputStreamToByteArray(ossService.getInputStream(url))));
        }
    }

    public void publishDelayEmailMsg(MailMsgSender sender, Integer delayTime) {
        CommonMessage msg = new CommonMessage();
        String msgBody = FastjsonUtil.toJson(sender);
        log.info("publishDelayEmailMsg msg sender={}", msgBody);
        msg.setBody(msgBody);
        msg.setExchange("message.limiter.exchange");
        msg.setRoutingKey("routingKey.message.limiter.delayed.email");
        producer.convertAndSend(msg, delayTime);
    }
}
