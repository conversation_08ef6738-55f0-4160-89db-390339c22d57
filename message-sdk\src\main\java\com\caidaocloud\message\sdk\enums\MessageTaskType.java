package com.caidaocloud.message.sdk.enums;

/**
 * <AUTHOR>
 * @Description 任务类型 钉钉:1 企业微信:2 飞书:3
 * @Date 2023/3/1 下午5:15
 * @Version 1.0
 **/
public enum MessageTaskType {

    DING_DING_TODO(1, "钉钉待办任务", "ding_ding_todo"),

    ENTERPRISE_WECHAT(2, "企业微信", "enterprise_wechat"),

    ANONYMOUS_LETTER(3, "飞书 ", "anonymous_letter");

    private Integer index;
    private String name;
    private String enName;

    MessageTaskType(Integer index, String name, String enName){
        this.index = index;
        this.name = name;
        this.enName = enName;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }
}
