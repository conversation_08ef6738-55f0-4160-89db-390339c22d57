package com.caidaocloud;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.message.service.application.message.notifier.service.*;
import com.caidaocloud.message.service.application.template.service.analysis.datasource.EmpTransferDataReader;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class NotifyTargetServiceTest {
    @Autowired
    private NotifyUserRoleService notifyUserRoleService;
    @Autowired
    private NotifyHrbpService notifyHrbpService;
    @Autowired
    private NotifyRecruiterService notifyRecruiterService;
    @Resource
    private NotifyLeaderService notifyLeaderService;
    @Resource
    private NotifyOrgLeaderService notifyOrgLeaderService;
    @Resource
    private NotifyAssignEmpService notifyAssignEmpService;
    @Resource
    private NotifyGuardianService notifyGuardianService;

    @Test
    public void testNotifyGuardianService(){
        List<NotifierDto> notifier = new ArrayList<>();
        NotifyObjectDto obj = new NotifyObjectDto();
        obj.setType(1);
        obj.setEmpIds(Lists.newArrayList("1482707864942594", "1482707864942594"));
        obj.setDataTime(System.currentTimeMillis());
        notifyGuardianService.getNotifier(notifier, obj);
        System.out.println("testNotifyGuardianService----------" + FastjsonUtil.toJson(notifier));
    }

    @Test
    public void testNotifyAssignEmpService(){
        List<NotifierDto> notifier = new ArrayList<>();
        NotifyObjectDto obj = new NotifyObjectDto();
        obj.setType(1);
        obj.setEmpIds(Lists.newArrayList("1482707864942594", "1482707864942594"));
        obj.setDataTime(System.currentTimeMillis());
        notifyAssignEmpService.getNotifier(notifier, obj);
        System.out.println("testNotifyAssignEmpService----------" + FastjsonUtil.toJson(notifier));
    }

    @Test
    public void testNotifyOrgLeaderService(){
        List<NotifierDto> notifier = new ArrayList<>();
        NotifyObjectDto obj = new NotifyObjectDto();
        obj.setType(1);
        obj.setEmpIds(Lists.newArrayList("1482707864942594", "1482707864942594"));
        obj.setDataTime(System.currentTimeMillis());
        notifyOrgLeaderService.getNotifier(notifier, obj);
        System.out.println("testNotifyOrgLeaderService----------" + FastjsonUtil.toJson(notifier));
    }

    @Test
    public void testNotifyLeaderService(){
        List<NotifierDto> notifier = new ArrayList<>();
        NotifyObjectDto obj = new NotifyObjectDto();
        obj.setType(1);
        obj.setEmpIds(Lists.newArrayList("1482707864942594", "1482707864942594"));
        obj.setDataTime(System.currentTimeMillis());
        notifyLeaderService.getNotifier(notifier, obj);
        System.out.println("testNotifyLeaderService----------" + FastjsonUtil.toJson(notifier));
    }

    @Test
    public void testNotifyRecruiterService(){
        List<NotifierDto> notifier = new ArrayList<>();
        NotifyObjectDto obj = new NotifyObjectDto();
        obj.setType(1);
        obj.setEmpIds(Lists.newArrayList("1482707864942594", "1482706714212353"));
        obj.setDataTime(System.currentTimeMillis());
        notifyRecruiterService.getNotifier(notifier, obj);
        System.out.println("testNotifyRecruiterService----------" + FastjsonUtil.toJson(notifier));
    }

    @Test
    public void testNotifyHrbpService(){
        List<NotifierDto> notifier = new ArrayList<>();
        NotifyObjectDto obj = new NotifyObjectDto();
        obj.setType(3);
        obj.setEmpIds(Lists.newArrayList("1482707864942594", "1482706714212353"));
        obj.setDataTime(System.currentTimeMillis());
        notifyHrbpService.getNotifier(notifier, obj);
        System.out.println("testNotifyHrbpService----------" + FastjsonUtil.toJson(notifier));
    }

    @Test
    public void testNotifyUserRole(){
        List<NotifierDto> notifier = new ArrayList<>();
        NotifyObjectDto obj = new NotifyObjectDto();
        obj.setType(3);
        obj.setEmpIds(Lists.newArrayList("35", "76", "1"));
//        notifyUserRoleService.getNotifier(notifier, obj);

        System.out.println("----------" + FastjsonUtil.toJson(notifier));
    }

    @Before
    public void initUser(){
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        userInfo.setUserId(0L);
        userInfo.setEmpId(1482707864942594L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);

        UserInfo user = new UserInfo();
        user.setTenantId(String.valueOf(11));
        user.doSetUserId(0L);
        user.setStaffId(1482707864942594L);
        UserContext.setCurrentUser(user);
    }

    @Test
    public void testEmpTransferRecord(){
        Map<String, Object> map = new HashMap<>();
        String identifier = "entity.hr.EmpTransferRecord";
        String empId = "1717681320269825";
        Map<String, String> ext = new HashMap<>();
        int type = 0;
        SpringUtil.getBean(EmpTransferDataReader.class).getData(map, identifier, empId, ext, type);
        System.out.println("testEmpTransferRecord----------" + FastjsonUtil.toJson(map));
    }
}
