package com.caidaocloud.message.service.interfaces.vo.base;

import com.caidaocloud.condition.tree.ConditionCallType;
import com.caidaocloud.condition.tree.ConditionOperator;
import com.caidaocloud.condition.tree.ValueComponent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ConditionVo {
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("编码")
    private String code;
    @ApiModelProperty("比较符号")
    private List<ConditionOperator> operators;
    @ApiModelProperty("调用方式")
    private ConditionCallType type;
    @ApiModelProperty("组件")
    private ValueComponent component;
    @ApiModelProperty("枚举清单")
    private List<ComponentValueVo> componentValueEnum;
    @ApiModelProperty("数据源地址")
    private String dataSourceAddress;
    @ApiModelProperty("查询参数")
    private Map<String, String> dataSourceParams;
}
