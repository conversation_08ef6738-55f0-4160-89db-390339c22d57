package com.caidaocloud.message.service.application.message.notifier.dto;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class NotifyObjectDto {
    // 通知对象ID
    private List<String> empIds;
    // 通知对象类型; 0:员工；1:候选人；2:外部人员; 3: 角色
    private int type = 0;
    // 查询时间轴
    private Long dataTime;
    // 监护人
    private boolean guardian = false;
    // 事件人
    private String eventSubject;
    /**
     * 查询员工
     */
    public NotifyObjectDto staff(){
        this.type = 0;
        return this;
    }

    /**
     * 查询候选人
     */
    public NotifyObjectDto candidate(){
        this.type = 1;
        return this;
    }

    /**
     * 查询外部人员
     */
    public NotifyObjectDto outside(){
        this.type = 2;
        return this;
    }

    public NotifyObjectDto setSubject(String subject){
        this.eventSubject = subject;
        this.setEmpIds(Lists.newArrayList(subject));
        return this;
    }
}
