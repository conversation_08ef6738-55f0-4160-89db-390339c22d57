package com.caidaocloud.message.service.infrastructure.repository.impl.announcement;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.message.service.application.announcement.dto.AnnouncementPageDto;
import com.caidaocloud.message.service.domain.announcement.entity.Announcement;
import com.caidaocloud.message.service.domain.announcement.enums.AnnouncementStatus;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementRepository;
import com.caidaocloud.message.service.domain.base.repository.BaseRepositoryImpl;
import org.apache.commons.lang3.StringUtils;

import org.springframework.stereotype.Repository;

/**
 * 公告Repository实现类
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Repository
public class AnnouncementRepositoryImpl extends BaseRepositoryImpl<Announcement> implements AnnouncementRepository {
	@Override
	public PageResult<Announcement> selectPage(AnnouncementPageDto dto) {
		DataFilter filter = getBaseFilter();
		filter = filter.andRegexIf("name", dto.getName(), () -> StringUtils.isNotEmpty(dto.getName()))
				.andEqIf("type$dictValue", dto.getType(), () -> StringUtils.isNotEmpty(dto.getType()));
		if (dto.getStatus() != null) {
			filter = filter.andEq("status", String.valueOf(dto.getStatus().getValue()));
		}
		if (dto.getStartTime() != null) {
			filter = filter.andGe("effectiveTime", String.valueOf(dto.getStartTime()));
		}
		if (dto.getEndTime() != null) {
			filter = filter.andLe("effectiveTime", String.valueOf(dto.getEndTime()));
		}
		if (!dto.getExpired()) {
			filter = filter.andNe("status", String.valueOf(AnnouncementStatus.EXPIRED.getValue()));
		}
	return	DataQuery.identifier(Announcement.identifier).limit(dto.getPageSize(), dto.getPageNo())
				.filter(filter, Announcement.class, "id desc", System.currentTimeMillis());
	}
}
