package com.caidaocloud.message.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/3/6
 */
@Data
@ApiModel("合并发送配置dto")
public class MergeRuleDto {
	@ApiModelProperty("合并发送日，1-31")
	private Integer mergeDay;
	@ApiModelProperty("合并发送时间")
	private Integer mergeTime;
	@ApiModelProperty("合并周期")
	private Integer mergePeriod;
}
