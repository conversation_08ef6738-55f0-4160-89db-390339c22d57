package com.caidaocloud.message.service.application.announcement.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.message.service.domain.announcement.enums.AnnouncementStatus;
import com.caidaocloud.message.service.domain.announcement.enums.ReceiveType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@ApiModel(description = "公告分页Dto")
public class AnnouncementPageDto extends BasePage {
	@ApiModelProperty(value = "公告名称")
	private String name;

	@ApiModelProperty(value = "公告类型")
	private String type;


	@ApiModelProperty(value = "生效时间开始时间")
	private Long startTime;

	@ApiModelProperty(value = "生效时间结束时间")
	private Long endTime;

	@ApiModelProperty("发布状态")
	private AnnouncementStatus status;

	@ApiModelProperty(value = "显示失效公告")
	private Boolean expired = false;

}
