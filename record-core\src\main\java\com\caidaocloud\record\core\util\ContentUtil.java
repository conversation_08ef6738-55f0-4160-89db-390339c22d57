package com.caidaocloud.record.core.util;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.record.core.annotation.LogRecordField;
import com.caidaocloud.record.core.beans.ModifiedField;
import com.caidaocloud.record.core.ext.IDataTypeParse;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ContentUtil {

    public static final List<String> IGNORE_FIELDS = Lists.newArrayList("id",
            "identifier", "bid", "tenantId", "createTime", "createBy", "updateTime", "updateBy", "dataStartTime",
            "dataEndTime", "deleted");

    public static IDataTypeParse dataParseService;

    @Autowired
    public void setDataParseService(IDataTypeParse dataParseService) {
        ContentUtil.dataParseService = dataParseService;
        PreCheck.preCheckNotNull(dataParseService, "parse service should not be empty");
    }

    public static <T> String getDiffContent(T obj1, T obj2, String... fieldNames) {
        Map<String, ModifiedField> differences = Maps.newHashMap();
        Map<String, ModifiedField> mapContent = getDiff(obj1, obj2, differences, fieldNames);
        return mapContent.keySet().stream().map(it -> {
            ModifiedField field = mapContent.get(it);
            if (!Objects.toString(field.getOldValue()).equals(Objects.toString(field.getNewValue()))) {
                return String.format("%s:[%s]修改为[%s]", it,field.getOldValue(), field.getNewValue());
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.joining(","));
    }

    public static <T> String getDiffFiledContent(T origin, T newData) {
        Map<String, ModifiedField> differences = Maps.newHashMap();
        Map<String, ModifiedField> mapContent = getDiff(origin, newData, differences);
        return String.join(",", mapContent.keySet());
    }

    public static <T> Map<String, ModifiedField> getDiff(T origin, T newData, Map<String, ModifiedField> differences, String... fields) {
        if (origin == null || newData == null) {
            return differences;
        } else {
            Class<?> clazz = origin.getClass();
            Field[] allFields = clazz.getDeclaredFields();
            List<String> filterFields = fields == null ? Lists.newArrayList() : Lists.newArrayList(fields);
            for (Field field : allFields) {
                if (!field.isAnnotationPresent(LogRecordField.class) && (CollectionUtils.isEmpty(filterFields))) {
                    continue;
                }
                LogRecordField annotation = field.getAnnotation(LogRecordField.class);
                field.setAccessible(true);
                try {
                    Object value1 = field.get(origin);
                    Object value2 = field.get(newData);
                    String fieldName = getFieldName(field);
                    if (!IGNORE_FIELDS.contains(fieldName) && (CollectionUtils.isEmpty(filterFields) || filterFields.contains(fieldName))) {
                        if (value1 == null && value2 == null) {
                            continue;
                        }
                        if (value1 == null || value2 == null) {
                            String objectContent = getObjectParseContent(annotation, field, value1 == null ? value2 : value1);
                            differences.put(fieldName, new ModifiedField(fieldName, value1 == null ? "" : objectContent,
                                    value2 == null ? "" : objectContent));
                            continue;
                        }
                        if (field.getType().isArray()) {
                            String beforeArray = getArrayParseContent(annotation, field, value1);
                            String afterArray = getArrayParseContent(annotation, field, value2);
                            differences.put(fieldName, new ModifiedField(fieldName, beforeArray, afterArray));
                        } else if (Collection.class.isAssignableFrom(field.getType())) {
                            String beforeCollect = getArrayParseContent(annotation, field, value1);
                            String afterCollect = getArrayParseContent(annotation, field, value2);
                            differences.put(fieldName, new ModifiedField(fieldName, beforeCollect, afterCollect));
                        } else {
                            String beforeData = getStandardContent(annotation, field, value1);
                            String afterData = getStandardContent(annotation, field, value2);
                            differences.put(fieldName, new ModifiedField(fieldName, beforeData, afterData));
                        }
                    }
                } catch (IllegalAccessException e) {
                    log.error("获取字段详情失败:{}", e.getMessage(), e);
                }
            }
            return differences;
        }
    }

    private static String getObjectParseContent(LogRecordField annotation, Field field, Object data) {
        boolean supportType = dataParseService.supportParse(annotation, data);
        if (supportType) {
            return dataParseService.parseField(annotation, data);
        } else if (field.getType().isPrimitive()) {
           return Objects.toString(data);
        } else if (field.getType().isArray()) {
            return getArrayParseContent(annotation, field, data);
        } else if (Collection.class.isAssignableFrom(field.getType())) {
            return getCollectParseContent(annotation, field, (Collection<?>)data);
        }
        return Objects.toString(data);
    }

    private static String getStandardContent(LogRecordField annotation, Field field, Object data) {
        boolean supportType = dataParseService.supportParse(annotation, data);
        if (supportType) {
            return dataParseService.parseField(annotation, data);
        } else if (field.getType().isPrimitive()) {
            return Objects.toString(data);
        }
        return Objects.toString(data);
    }

    private static String getArrayParseContent(LogRecordField annotation, Field field, Object data) {
        int length = Array.getLength(data);
        List<String> allData = Lists.newArrayList();
        for (int i = 0; i < length; i++) {
            Object element = Array.get(data, i);
            allData.add(getStandardContent(annotation, field, element));
        }
        return String.join(",", allData);
    }

    public static <T extends Collection<?>> String getCollectParseContent(LogRecordField annotation, Field field, T data) {
        Iterator<?> iterator = data.iterator();
        List<String> allData = Lists.newArrayList();
        while (iterator.hasNext()) {
            allData.add(getStandardContent(annotation, field, iterator.next()));
        }
        return String.join(",", allData);
    }

    private static String getFieldName(Field field) {
        LogRecordField annotation = field.getAnnotation(LogRecordField.class);
        if (annotation != null && !annotation.value().isEmpty()) {
            return annotation.value();
        } else {
            return field.getName();
        }
    }

}
