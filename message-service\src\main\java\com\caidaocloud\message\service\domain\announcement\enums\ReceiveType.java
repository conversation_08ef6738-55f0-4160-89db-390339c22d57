package com.caidaocloud.message.service.domain.announcement.enums;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;

public enum ReceiveType {
    ALL_PERSONNEL(0, "所有人员"),
    DESIGNATED_DEPARTMENT(1, "指定部门"),
    DESIGNATED_AND_SUB_DEPARTMENTS(2, "指定部门及下属部门"),
    DESIGNATED_PERSONNEL(3, "指定人员");

    private final int value;
    private final String display;

    ReceiveType(int value, String display) {
        this.value = value;
        this.display = display;
    }

    public int getValue() {
        return value;
    }

    public String getDisplay() {
        return display;
    }

    public static ReceiveType fromValue(int value) {
        for (ReceiveType type : ReceiveType.values()) {
            if (type.value == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid ReceiveType value: " + value);
    }

    public EnumSimple toEnumSimple(){
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(String.valueOf(value));
        return enumSimple;
    }
}