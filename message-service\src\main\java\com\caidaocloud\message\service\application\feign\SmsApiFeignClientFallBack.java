package com.caidaocloud.message.service.application.feign;

import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * created  FoAng
 * create time: 29/8/2022 3:13 下午
 */
@Component
public class SmsApiFeignClientFallBack implements SmsApiFeignClient{

    @Override
    public String getAccessToken(String url, Map params) {
        return null;
    }

    @Override
    public String doSendSms(String url, String accessToken, JSONObject object) {
        return null;
    }
}
