package com.caidaocloud.message.sdk.convert;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 7/6/2023 5:36 下午
 */
@Slf4j
public class ListStrDeserializer extends JsonDeserializer<String> {

    @Override
    public String deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JacksonException {
        String originalValue = jsonParser.getValueAsString();
        try {
            JSONArray jsonArray = JSONArray.parseArray(originalValue);
            if (jsonArray != null && !jsonArray.isEmpty()) {
                return Arrays.stream(jsonArray.toArray()).map(Objects::toString).collect(Collectors.joining(","));
            }
        } catch (Exception e) {
            log.error("ListStrDeserializer fail:{}", e.getMessage(), e);
            return originalValue;
        }
        return originalValue;
    }
}
