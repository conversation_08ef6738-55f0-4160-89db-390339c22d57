package com.caidaocloud.message.service.interfaces.dto.template;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/6/16
 */
@Data
@ApiModel("占位符dto")
public class TemplateVariableDto {
    @ApiModelProperty("模型名称")
    private String model;
    @ApiModelProperty("占位符的显示名称")
    private String text;
    @ApiModelProperty("属性名")
    private String prop;
    @ApiModelProperty("占位符的列表字段名")
    private String name;
    @ApiModelProperty("字段占位替换后的显示类型：0 默认原生展示;1 yyyy-MM-dd 展示;2 yyyy-MM-dd HH:mm:ss")
    private String varType;
}
