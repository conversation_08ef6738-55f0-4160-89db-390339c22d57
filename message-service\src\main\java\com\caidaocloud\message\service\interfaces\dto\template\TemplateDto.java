package com.caidaocloud.message.service.interfaces.dto.template;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.sdk.enums.DingtalkTemplateTypeEnum;
import com.caidaocloud.message.service.application.common.enums.EmailCopyTarget;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Data
@ApiModel("消息模版dto")
public class TemplateDto {
    private String bid;

    @ApiModelProperty("模版名称")
    private String name;

    @ApiModelProperty("模版名称多语言")
    private Map<String,Object> i18nName;

    @ApiModelProperty("模版内容")
    private String content;

    /**
     * 模版分类
     * 0 短信模版
     * 1 邮件模版
     * 2 系统模版
     * 3 APP模版
     * 4 钉钉模板
     */
    @ApiModelProperty("模版分类")
    private EnumSimple category;


    @ApiModelProperty("模版多语言")
    private Map<String,Object> i18nCategory;

    /**
     * 模版标题
     */
    @ApiModelProperty("模版标题")
    private String title;


    /**
     * 钉钉模版类型： 链接消息，文本消息，流程消息
     */
    @ApiModelProperty("模版类型")
    private DingtalkTemplateTypeEnum templateType;

    /**
     * 钉钉模版URL
     */
    @ApiModelProperty("钉钉模版URL")
    private String templateUrl;


    /**
     * 模版code
     */
    @ApiModelProperty("模版code")
    private String code;

    /**
     * 模版附件
     */
    @ApiModelProperty("模版附件")
    private Attachment attachFile;

    /**
     * 消息设置
     */
    @ApiModelProperty("消息设置")
    private String msgConfig;

    @ApiModelProperty("占位符dto")
    private List<TemplateVariableDto> variable;

    @ApiModelProperty("抄送对象\n" +
            "hrbp HRBP\n" +
            "leader 上级领导\n" +
            "orgLeader 部门负责人")
    private List<String> copyObject;

    @ApiModelProperty("抄送邮箱")
    private String copyMail;

    @ApiModelProperty("密送对象\n" +
            "hrbp HRBP\n" +
            "leader 上级领导\n" +
            "orgLeader 部门负责人")
    private List<String> blindCopyObject;

    @ApiModelProperty("密送邮箱")
    private String blindCopyMail;

    @ApiModelProperty("外部人员邮箱")
    private String externalMail;

    @ApiModelProperty("通知规则")
    private String noticeRule;

    public static String getCategoryTxt(EnumSimple category) {
        return Optional.ofNullable(category).map(it -> {
            String categoryValue = category.getValue();
            if (StringUtils.isNotEmpty(categoryValue)) {
                switch (categoryValue) {
                    case "0":
                        return "短信模板";
                    case "1":
                        return "邮件模板";
                    case "2":
                        return "系统模版";
                    case "3":
                        return "APP模版";
                    case "4":
                        return "钉钉模板";
                    default:
                        return "消息模板";
                }
            }
            return null;
        }).orElse("消息模板");
    }

    public String getTemplateName() {
        return Optional.ofNullable(name).orElseGet(() -> (String) this.i18nName.getOrDefault("default",
                ""));
    }
}
