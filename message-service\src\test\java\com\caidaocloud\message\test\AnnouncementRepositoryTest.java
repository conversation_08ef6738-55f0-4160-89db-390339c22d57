package com.caidaocloud.message.test;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.domain.announcement.entity.Announcement;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementContent;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementReceive;
import com.caidaocloud.message.service.domain.announcement.enums.AnnouncementStatus;
import com.caidaocloud.message.service.domain.announcement.enums.ReceiveType;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementContentRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementReceiveRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementRepository;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 公告Repository层数据访问测试类
 * 专门测试insert、update操作是否成功
 *
 * <AUTHOR> Zhou
 * @date 2025/7/11
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class AnnouncementRepositoryTest {

    @Resource
    private AnnouncementRepository announcementRepository;

    @Resource
    private AnnouncementContentRepository announcementContentRepository;

    @Resource
    private AnnouncementReceiveRepository announcementReceiveRepository;

    @Before
    public void setUp() {
        // 设置测试用户信息
        SecurityUserInfo userInfo = new SecurityUserInfo();
        // userInfo.setUserId(11L);
        userInfo.setTenantId("11");
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }

    /**
     * 测试公告基本信息的insert操作
     */
    @Test
    public void testAnnouncementInsert() {
        log.info("开始测试公告基本信息insert操作");

        // 创建测试数据
        Announcement announcement = createTestAnnouncement();

        // 执行insert操作
        Announcement savedAnnouncement = announcementRepository.insert(announcement);

        // 验证insert结果
        Assert.assertNotNull("保存后的公告对象不应该为空", savedAnnouncement);
        Assert.assertNotNull("公告ID不应该为空", savedAnnouncement.getBid());
        Assert.assertTrue("公告ID应该不为空字符串", StringUtils.hasText(savedAnnouncement.getBid()));
        Assert.assertEquals("公告名称应该一致", announcement.getName(), savedAnnouncement.getName());
        Assert.assertEquals("公告类型应该一致", announcement.getType().getValue(), savedAnnouncement.getType().getValue());
        Assert.assertEquals("公告状态应该一致", announcement.getStatus().getValue(), savedAnnouncement.getStatus().getValue());

        log.info("公告基本信息insert操作成功，ID：{}", savedAnnouncement.getBid());

        // 验证数据库中的数据
        Announcement dbAnnouncement = announcementRepository.selectById(savedAnnouncement.getBid(), Announcement.identifier);
        Assert.assertNotNull("从数据库查询的公告不应该为空", dbAnnouncement);
        Assert.assertEquals("数据库中的公告名称应该一致", announcement.getName(), dbAnnouncement.getName());
        Assert.assertEquals("数据库中的公告类型应该一致", announcement.getType().getValue(), dbAnnouncement.getType().getValue());

        log.info("数据库验证通过，公告insert操作成功");
    }

    /**
     * 测试公告基本信息的update操作
     */
    @Test
    public void testAnnouncementUpdate() {
        log.info("开始测试公告基本信息update操作");

        // 先插入一条数据
        Announcement announcement = createTestAnnouncement();
        Announcement savedAnnouncement = announcementRepository.insert(announcement);
        String announcementId = savedAnnouncement.getBid();

        // 修改数据
        savedAnnouncement.setName("更新后的公告名称");
        DictSimple newType = new DictSimple();
        newType.setValue("updated-type");
        savedAnnouncement.setType(newType);
        savedAnnouncement.setIsTop(true);

        // 执行update操作
        int updateResult = announcementRepository.updateById(savedAnnouncement);

        // 验证update结果
        Assert.assertEquals("update操作应该返回成功", 0, updateResult); // BaseRepository的updateById返回0表示成功

        log.info("公告基本信息update操作成功，ID：{}", announcementId);

        // 验证更新后的数据
        Announcement updatedAnnouncement = announcementRepository.selectById(announcementId, Announcement.identifier);
        Assert.assertNotNull("更新后的公告不应该为空", updatedAnnouncement);
        Assert.assertEquals("更新后的公告名称应该一致", "更新后的公告名称", updatedAnnouncement.getName());
        Assert.assertEquals("更新后的公告类型应该一致", "updated-type", updatedAnnouncement.getType().getValue());
        Assert.assertTrue("更新后的置顶状态应该为true", updatedAnnouncement.getIsTop());

        log.info("数据库验证通过，公告update操作成功");
    }

    /**
     * 测试公告内容的insert操作
     */
    @Test
    public void testAnnouncementContentInsert() {
        log.info("开始测试公告内容insert操作");

        // 先创建公告基本信息
        Announcement announcement = createTestAnnouncement();
        Announcement savedAnnouncement = announcementRepository.insert(announcement);
        String announcementId = savedAnnouncement.getBid();

        // 创建公告内容
        AnnouncementContent content = createTestAnnouncementContent(announcementId);

        // 执行insert操作
        AnnouncementContent savedContent = announcementContentRepository.insert(content);

        // 验证insert结果
        Assert.assertNotNull("保存后的公告内容不应该为空", savedContent);
        Assert.assertNotNull("公告内容ID不应该为空", savedContent.getBid());
        Assert.assertEquals("公告ID应该一致", announcementId, savedContent.getAnnouncementId());
        Assert.assertEquals("公告内容应该一致", content.getContent(), savedContent.getContent());

        log.info("公告内容insert操作成功，ID：{}", savedContent.getBid());

        // 验证数据库中的数据
        AnnouncementContent dbContent = announcementContentRepository.findByAnnouncementId(announcementId);
        Assert.assertNotNull("从数据库查询的公告内容不应该为空", dbContent);
        Assert.assertEquals("数据库中的公告内容应该一致", content.getContent(), dbContent.getContent());

        log.info("数据库验证通过，公告内容insert操作成功");
    }

    /**
     * 测试公告内容的update操作
     */
    @Test
    public void testAnnouncementContentUpdate() {
        log.info("开始测试公告内容update操作");

        // 先创建公告和内容
        Announcement announcement = createTestAnnouncement();
        Announcement savedAnnouncement = announcementRepository.insert(announcement);
        String announcementId = savedAnnouncement.getBid();

        AnnouncementContent content = createTestAnnouncementContent(announcementId);
        AnnouncementContent savedContent = announcementContentRepository.insert(content);

        // 修改内容
        savedContent.setContent("更新后的公告内容");
        Attachment newAttachment = new Attachment();
        newAttachment.setNames(Lists.list("updated.pdf"));
        newAttachment.setUrls(Lists.list("http://test.com/updated.pdf"));
        savedContent.setAttachment(newAttachment);

        // 执行update操作
        int updateResult = announcementContentRepository.updateById(savedContent);

        // 验证update结果
        Assert.assertEquals("update操作应该返回成功", 0, updateResult);

        log.info("公告内容update操作成功，ID：{}", savedContent.getBid());

        // 验证更新后的数据
        AnnouncementContent updatedContent = announcementContentRepository.findByAnnouncementId(announcementId);
        Assert.assertNotNull("更新后的公告内容不应该为空", updatedContent);
        Assert.assertEquals("更新后的内容应该一致", "更新后的公告内容", updatedContent.getContent());
        Assert.assertEquals("更新后的附件名称应该一致", "updated.pdf", updatedContent.getAttachment().getNames());

        log.info("数据库验证通过，公告内容update操作成功");
    }

    /**
     * 测试公告接收者的批量insert操作
     */
    @Test
    public void testAnnouncementReceiveBatchInsert() {
        log.info("开始测试公告接收者批量insert操作");

        // 先创建公告基本信息
        Announcement announcement = createTestAnnouncement();
        Announcement savedAnnouncement = announcementRepository.insert(announcement);
        String announcementId = savedAnnouncement.getBid();

        // 创建接收者列表
        List<AnnouncementReceive> receiveList = createTestAnnouncementReceiveList(announcementId);

        // 执行批量insert操作
        announcementReceiveRepository.batchInsert(receiveList);

        log.info("公告接收者批量insert操作成功，数量：{}", receiveList.size());

        // 验证数据库中的数据
        List<AnnouncementReceive> dbReceiveList = announcementReceiveRepository.findByAnnouncementId(announcementId);
        Assert.assertNotNull("从数据库查询的接收者列表不应该为空", dbReceiveList);
        Assert.assertEquals("数据库中的接收者数量应该一致", receiveList.size(), dbReceiveList.size());

        // 验证每个接收者的数据
        for (int i = 0; i < receiveList.size(); i++) {
            AnnouncementReceive expected = receiveList.get(i);
            AnnouncementReceive actual = dbReceiveList.get(i);
            Assert.assertEquals("接收者值应该一致", expected.getReceiveValue(), actual.getReceiveValue());
            Assert.assertEquals("接收者类型应该一致", expected.getReceiveType().getValue(), actual.getReceiveType().getValue());
        }

        log.info("数据库验证通过，公告接收者批量insert操作成功");
    }

    /**
     * 创建测试用的公告实体
     */
    private Announcement createTestAnnouncement() {
        Announcement announcement = new Announcement();
        announcement.setName("测试公告");
        
        DictSimple type = new DictSimple();
        type.setValue("4001");
        announcement.setType(type);
        
        announcement.setReleaseTime(System.currentTimeMillis());
        announcement.setEffectiveTime(System.currentTimeMillis());
        announcement.setExpiryTime(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L);
        
        announcement.setReceiveType(ReceiveType.ALL_PERSONNEL.toEnumSimple());
        announcement.setIsTop(false);
        announcement.setStatus(AnnouncementStatus.UNPUBLISHED.toEnumSimple());
        announcement.setDomain(Arrays.asList("test.domain.com"));
        
        return announcement;
    }

    /**
     * 创建测试用的公告内容实体
     */
    private AnnouncementContent createTestAnnouncementContent(String announcementId) {
        AnnouncementContent content = new AnnouncementContent();
        content.setAnnouncementId(announcementId);
        content.setContent("这是测试公告的内容");
        
        Attachment attachment = new Attachment();
        attachment.setNames(Lists.list(
                "test.pdf"));
        attachment.setUrls(Lists.list("http://test.com/test.pdf"));

        content.setAttachment(attachment);
        
        return content;
    }

    /**
     * 创建测试用的公告接收者列表
     */
    private List<AnnouncementReceive> createTestAnnouncementReceiveList(String announcementId) {
        AnnouncementReceive receive1 = new AnnouncementReceive();
        receive1.setAnnouncementId(announcementId);
        receive1.setReceiveType(ReceiveType.ALL_PERSONNEL.toEnumSimple());
        receive1.setReceiveValue("all");

        AnnouncementReceive receive2 = new AnnouncementReceive();
        receive2.setAnnouncementId(announcementId);
        receive2.setReceiveType(ReceiveType.DESIGNATED_PERSONNEL.toEnumSimple());
        receive2.setReceiveValue("user123");

        return Arrays.asList(receive1, receive2);
    }
}
