package com.caidaocloud.message.service.domain.announcement.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.service.application.announcement.dto.AnnouncementPageDto;
import com.caidaocloud.message.service.domain.announcement.entity.Announcement;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;

import java.util.List;

/**
 * 公告Repository接口
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
public interface AnnouncementRepository extends BaseRepository<Announcement> {
	PageResult<Announcement> selectPage(AnnouncementPageDto announcementPageDto);

	/**
	 * 查询已发布且生效的公告
	 * 
	 * @param currentTime 当前时间
	 * @return 公告列表
	 */
	List<Announcement> findPublishedAndEffectiveAnnouncements(Long currentTime);

	/**
	 * 查询已发布且过期的公告
	 * 
	 * @param currentTime 当前时间
	 * @return 公告列表
	 */
	List<Announcement> findPublishedAndExpiredAnnouncements(Long currentTime);

}
