package com.caidaocloud.message.service.domain.announcement.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.service.application.announcement.dto.AnnouncementPageDto;
import com.caidaocloud.message.service.domain.announcement.entity.Announcement;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;

/**
 * 公告Repository接口
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
public interface AnnouncementRepository extends BaseRepository<Announcement> {
	PageResult<Announcement> selectPage(AnnouncementPageDto announcementPageDto);

	// TODO: 根据需要添加特定的查询方法
    
}
