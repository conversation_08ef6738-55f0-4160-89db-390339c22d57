package com.caidaocloud.message.service.application.feign;


import com.caidaocloud.message.service.application.feign.dto.UserBaseInfoQueryParam;
import com.caidaocloud.message.service.application.feign.dto.UserDetailInfoVo;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class UserServiceFeignClentFallback implements UserServiceFeignClent {


    @Override
    public Result<List<UserDetailInfoVo>> queryListByMobilesAndEmails(UserBaseInfoQueryParam queryParam) {

        return Result.fail();
    }

    @Override
    public Result<UserDetailInfoVo> getUserById(Long userId) {
        return Result.fail();
    }

    @Override
    public Result<List<UserDetailInfoVo>> getUserByIds(List<Long> userIds) {
        return Result.fail();
    }

    @Override
    public Result<UserDetailInfoVo> getUserByEmpId(Long empId) {
        return Result.fail();
    }
}
