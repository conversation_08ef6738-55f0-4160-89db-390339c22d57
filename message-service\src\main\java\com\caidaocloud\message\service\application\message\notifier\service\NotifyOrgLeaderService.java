package com.caidaocloud.message.service.application.message.notifier.service;

import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.message.service.application.common.enums.NoticeTarget;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDataDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifierDto;
import com.caidaocloud.message.service.application.message.notifier.dto.NotifyObjectDto;
import com.caidaocloud.message.service.application.message.notifier.input.NotifierDataFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 查询 部门负责人
 */
@Service
public class NotifyOrgLeaderService implements INotifyTargetService{

    @Override
    public String getNotifyTarget() {
        return NoticeTarget.ORG_LEADER.toString();
    }

    @Override
    public void getNotifier(List<NotifierDto> notifier, NotifyObjectDto obj) {
        List<EmpDataVo> orgLeaders = null;
        if(1 == obj.getType()){
            orgLeaders = NotifierDataFactory.getNotifierService()
                    .getCandidateOrgLeaders(NotifierDataDto.bulid(obj));
        } else {
            orgLeaders = NotifierDataFactory.getNotifierService()
                    .getOrgLeadersByEmpIds(obj.getEmpIds(), obj.getDataTime());
        }

        if(null == orgLeaders || orgLeaders.isEmpty()){
            return;
        }

        notifier.addAll(convertEmpDataList(orgLeaders));
    }
}
