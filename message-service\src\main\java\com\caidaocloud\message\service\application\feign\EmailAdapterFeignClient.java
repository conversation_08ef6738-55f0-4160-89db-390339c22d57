package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.message.service.domain.email.dto.EmailTemplateBatchDto;
import com.caidaocloud.message.service.domain.email.dto.EmailTemplateDto;
import com.caidaocloud.message.service.domain.email.dto.SameEmailTemplateDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(value = "EmailHttpApiClient",
        url = "${caidaocloud.msg.email.url:}",
        fallback = EmailAdapterFeignClientFallback.class,
        configuration = FeignConfiguration.class)
public interface EmailAdapterFeignClient {

    /**
     * 发送单邮件
     *
     * @param emailTemplateDto
     * @return
     */
    @Headers("Content-Type:multipart/form-data")
    @PostMapping(value = "api/adapterDisney/email/v1/emailSingleSend", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result emailSingleSend(EmailTemplateDto emailTemplateDto);

    /**
     * 批量发送邮件
     *
     * @param emailTemplateBatchDto
     * @return
     */
    @Headers("Content-Type:multipart/form-data")
    @PostMapping(value = "api/adapterDisney/email/v1/emailBatchSend", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result emailBatchSend(EmailTemplateBatchDto emailTemplateBatchDto);

    /**
     * 相同邮件批量发送
     *
     * @param sameEmailTemplateDto
     * @return
     */
    @PostMapping(value = "api/adapterDisney/email/v1/sameEmailBatchSend", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result sameEmailBatchSend(SameEmailTemplateDto sameEmailTemplateDto);

}