package com.caidaocloud.message.service.domain.base.repository;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 规范定义简单的增删改查
 *
 * <AUTHOR>
 * @date 2021-11-24
 */
public interface BaseRepository<T> {
    default T insert(T data) {
        return null;
    }

    default int deleteById(Serializable id) {
        return 0;
    }

    default int deleteByMap(Map<String, Object> map) {
        return 0;
    }

    default int delete(T data) {
        return 0;
    }

    default int delete(T data, Long dateTime) {
        return 0;
    }

    int deleteBatchIds(String identifier, List<String> ids);

    default int updateById(T data) {
        return 0;
    }

    default int update(T query, T data) {
        return 0;
    }

    default T selectById(Serializable id) {
        return null;
    }

    default T selectById(String id, String identifier) {
        return null;
    }

    default T selectById(String id, String identifier, Long dateTime) {
        return null;
    }

    default T selectOneByTenantId(String identifier, String tenantId) {
        return null;
    }

    default List<T> selectBatchIds(List<String> ids, String identifier) {
        return null;
    }

    default List<T> selectBatchIds(List<String> ids, String identifier, String tenantId) {
        return null;
    }

    default List<T> selectBatchIds(List<String> ids, String identifier, Long dateTime) {
        return null;
    }

    default List<T> selectByMap(Map<String, Object> map) {
        return null;
    }

    default T selectOne(T data) {
        return null;
    }

    default Integer selectCount(T data) {
        return null;
    }

    default List<T> selectList(T data) {
        return null;
    }

    default List<T> selectList(T data, Long dateTime) {
        return null;
    }

    default List<Map<String, Object>> selectMaps(T data) {
        return null;
    }

    default List<Object> selectObjs(T data) {
        return null;
    }

    default PageResult<T> selectPage(BasePage page, T data) {
        return null;
    }

    default PageResult<T> selectPage(BasePage page, T data, Long dateTime) {
        return null;
    }

    default PageResult<T> selectPage(BasePage page, T data, String keywords, Long dateTime) {
        return null;
    }

    default PageResult<Map<String, Object>> selectMapsPage(BasePage page, T data) {
        return null;
    }

}
