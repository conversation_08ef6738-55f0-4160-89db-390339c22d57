package com.caidaocloud.message.test;

import com.caidaocloud.message.service.application.email.dto.EmailTmplateMessage;
import com.caidaocloud.message.service.application.email.dto.HtmlMailDto;

public class HtmlMailTest {
    public static void main(String[] args) {
        HtmlMailDto m = new HtmlMailDto();
        m.setSmtp("smtp.mxhichina.com");
        m.setPassword("123!@#qwe");
        m.setFrom("<EMAIL>");
        //m.setAffix("/Volumes/win/develop/apache-tomcat-8.0.23/webapps/open_cloud_rmt/upload/template/模板-薪资201501.xlsx", "ab232");
//		m.setAffix("/Volumes/win/develop/apache-tomcat-8.0.23/webapps/open_cloud_rmt/upload/template/PostgreSQL 性能优化培训 3 DAY.pdf", "ab232");
//		m.setContent("测试附件");
        m.setNick("nick323232323");
//		m.setSubject("Test CC");
//		m.setTo("<EMAIL>");
        EmailTmplateMessage tm = new EmailTmplateMessage();
        tm.setTo("<EMAIL>");
        tm.setSubject("Test CC");
//		tm.setCc("<EMAIL>,<EMAIL>");
        tm.setContent(" JavaMail API (compat) » 1.5.0-b01");
//        m.send2(tm);
        System.out.println("success");
    }
}
