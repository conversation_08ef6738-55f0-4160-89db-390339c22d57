package com.caidaocloud.message.test;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.sdk.dto.MsgNoticeDto;
import com.caidaocloud.message.sdk.event.publish.MsgPublish;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.message.send.MessageSendService;
import com.caidaocloud.message.service.application.msg.event.ContractEventEntity;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class MqMsgSendTest {
    @Resource
    private MsgPublish msgPublish;
    @Resource
    private MessageSendService messageSendService;

    @Test
    public void mqMsgSend(){
        MsgNoticeDto messageDto = new MsgNoticeDto();
        messageDto.setTenantId("33");
        messageDto.setUserId(0L);
        messageDto.setSubjects(Lists.newArrayList("1508159433734198"));
        messageDto.setCreateTime(System.currentTimeMillis());
        messageDto.setExt(null);
        messageDto.setType(1);
        messageDto.setMsgConfig("1508706877110289");
//        this.msgPublish.msgNoticePublish(messageDto, 0);
    }

    @Test
    public void testSendMsgNoticeDto(){
        String mqMsg = "{\"createTime\":1664524704007,\"msgConfig\":\"1509602065956452\",\"msgFrom\":\"OnBoarding\",\"subjects\":[\"1510262476922103\"],\"tenantId\":\"33\",\"type\":1,\"userId\":1443538899605506}";
        MsgNoticeDto mn = FastjsonUtil.toObject(mqMsg, MsgNoticeDto.class);
//        this.msgPublish.msgNoticePublish(mn, 0);
        System.out.println("111");
    }

    @Test
    public void testReceive(){
        String message = "{\"createTime\":1664523988582,\"msgConfig\":\"1453494690879503\",\"subjects\":[\"1509535982893742\"],\"tenantId\":\"33\",\"userId\":0}";
        log.info("testReceive-----------------mq receive msg info:{}", message);
        try {
            ContractEventEntity entity = FastjsonUtil.toObject(message, ContractEventEntity.class);
            String tenantId = entity.getTenantId();
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(tenantId);
            final Long userId = entity.getUserId();
            userInfo.setEmpId(0L);
            userInfo.setUserId(Long.valueOf(userId));
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            UserInfo user = new UserInfo();
            user.setTenantId(tenantId);
            user.setStaffId(0L);
            user.doSetUserId(userInfo.getUserId());
            UserContext.setCurrentUser(user);

//            messageSendService.doConsumeMessage(entity);
        } catch (Exception e) {
            log.error("process message={} err,{}", message, e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }

    @Before
    public void initUser(){
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("33");
        userInfo.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);

        UserInfo user = new UserInfo();
        user.setTenantId(String.valueOf(33));
        user.setStaffId(0L);
        user.doSetUserId(0L);
        UserContext.setCurrentUser(user);
    }
}
