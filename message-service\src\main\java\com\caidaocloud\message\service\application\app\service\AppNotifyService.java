package com.caidaocloud.message.service.application.app.service;

import com.caidaocloud.message.service.application.app.dto.AppMsgSender;

import java.util.List;

public interface AppNotifyService extends NotifyService{

    default void send(AppMsgSender sender){
        sendMsg(sender);
    }

    boolean sendMsg(AppMsgSender sender);

    default void batchSend(List<AppMsgSender> senderList){
        batchSendMsg(senderList);
    }

    boolean batchSendMsg(List<AppMsgSender> senderList);
}
