package com.caidaocloud.message.service.domain.template.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.service.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;
import com.caidaocloud.message.service.domain.template.repository.PlaceholderRepository;
import lombok.Data;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 内置占位符
 */
@Data
@Service
public class PlaceholderDo extends BaseDomainDoImpl {
    /**
     * 占位符的显示名称
     */
    private String text;

    /**
     * 占位符的列表字段名
     */
    private String name;

    /**
     * 占位符来源与哪个模型
     */
    private String model;

    /**
     * 分类
     */
    private String category;

    /**
     * 占位符的key
     */
    private String prop;

    /**
     * 是否是自定义字段
     */
    private boolean custom;

    /**
     * 字段占位替换后的显示类型：0 默认原生展示;1 yyyy-MM-dd 展示;2 yyyy-MM-dd HH:mm:ss
     */
    private EnumSimple varType;

    private final static String IDENTIFIER = "entity.message.Placeholder";

    @Resource
    private PlaceholderRepository repository;

    @Override
    public BaseRepository getRepository() {
        return repository;
    }

    @Override
    public String getDoIdentifier() {
        return IDENTIFIER;
    }

    @Override
    protected DataSimple build() {
        PlaceholderDo data = new PlaceholderDo();
        data.setIdentifier(IDENTIFIER);
        return data;
    }
}
