package com.caidaocloud.record.core.service.impl;

import com.caidaocloud.record.core.beans.AuthResourceUrl;
import com.caidaocloud.record.core.service.IUrlResourceService;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;

import java.util.List;

/**
 * created by: FoAng
 * create time: 29/5/2024 10:16 上午
 */
@Slf4j
@AllArgsConstructor
public class DefaultUrlResourceService extends IUrlResourceService {

    @Override
    public List<String> excludeFilterUrls() {
        return Lists.newArrayList();
    }

    @Override
    public List<AuthResourceUrl> urlResources() {
        return Lists.newArrayList();
    }
}
