package com.caidaocloud;

import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.feign.DingTalkFeignClient;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/2/6 上午10:52
 * @Version 1.0
 **/
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class DingTalkFeignClientTest {


    @Resource
    private DingTalkFeignClient dingTalkFeignClient;




    @Test
    public void testText(){
        String msg = "{\"msgtype\":\"oa\",\"oa\":{\"message_url\":\"http://dingtalk.com\",\"head\":{\"bgcolor\":\"FFBBBBBB\",\"text\":\"头部标题\"},\"body\":{\"title\":\"正文标题\",\"form\":[{\"key\":\"姓名:\",\"value\":\"张三\"},{\"key\":\"年龄:\",\"value\":\"20\"},{\"key\":\"身高:\",\"value\":\"1.8米\"},{\"key\":\"体重:\",\"value\":\"130斤\"},{\"key\":\"学历:\",\"value\":\"本科\"},{\"key\":\"爱好:\",\"value\":\"打球、听音乐\"}],\"rich\":{\"num\":\"15.6\",\"unit\":\"元\"},\"content\":\"大段文本大段文本大段文本大段文本大段文本大段文本\",\"image\":\"@lADOADmaWMzazQKA\",\"file_count\":\"3\",\"author\":\"李四 \"}}}";


        msg = "{\"msgtype\":\"action_card\",\"action_card\":{\"title\":\"是透出到会话列表和通知的文案\",\"markdown\":\"支持markdown格式的正文内容\",\"single_title\":\"查看详情\",\"single_url\":\"https://www.baidu.com\"}}";
//        msg = "{\"msgtype\":\"action_card\",\"action_card\":{\"title\":\"是透出到会话列表和通知的文案\",\"markdown\":\"**我是标题pp** \\n 我是正文内容 \\n ##二号标题**加粗哈哈**\",\"single_title\":\"查看详情\",\"single_url\":\"https://www.baidu.com\"}}";

        msg = "{\n" +
                "    \"msgtype\": \"markdown\",\n" +
                "    \"markdown\": {\n" +
                "        \"title\": \"首屏会话透出的展示内容\",\n" +
                "        \"text\": \"# 这是支持markdown的文本   \\n   ## 标题2    \\n   * 列表1   \\n  ![alt 啊](https://img.alicdn.com/tps/TB1XLjqNVXXXXc4XVXXXXXXXXXX-170-64.png)\"\n" +
                "    }\n" +
                "}";
        msg = "{\n" +
                "    \"msgtype\":\"markdown\",\n" +
                "    \"markdown\":{\n" +
                "        \"title\":\"首屏会话透出的展示内容111111title\",\n" +
                "        \"text\":\"  列表1  \\n  列表2  \\n   列表3\\n\"\n" +
                "    }\n" +
                "}";
        Map msgMap = FastjsonUtil.toObject(msg, Map.class);
        Map reqMap = new HashMap();

        reqMap.put("agent_id", "2388290964");
        reqMap.put("userid_list", "113551601213537756");
        reqMap.put("msg", msgMap);

        System.out.println(reqMap.get("msg"));
        Map<String, String> ssoToken = dingTalkFeignClient.getDingTalkToken("dinguorrgiqy0v4gxtva", "ZRuKUtxYOF7IWtNKrL_r7FT3QKaQDtcp4hntKGoAOMlwsh4VzglGF4wyaR0ui_i5");
        String accessToken = ssoToken.get("access_token");


        Map map = dingTalkFeignClient.pushMsg(reqMap, accessToken);
        System.out.println("============" + FastjsonUtil.toJson(map));
    }


    @Test
    public void testGetUserId(){
        Map<String, String> ssoToken = dingTalkFeignClient.getDingTalkToken("dinguorrgiqy0v4gxtva", "ZRuKUtxYOF7IWtNKrL_r7FT3QKaQDtcp4hntKGoAOMlwsh4VzglGF4wyaR0ui_i5");
        String accessToken = ssoToken.get("access_token");

        Map map = dingTalkFeignClient.getUserId("18332559937", accessToken);
        System.out.println(FastjsonUtil.toJson(map));
    }

    @Test
    public void testGetUserId22(){
        Map<String, String> ssoToken = dingTalkFeignClient.getDingTalkToken("dinguorrgiqy0v4gxtva", "ZRuKUtxYOF7IWtNKrL_r7FT3QKaQDtcp4hntKGoAOMlwsh4VzglGF4wyaR0ui_i5");
        String accessToken = ssoToken.get("access_token");

        System.out.println(accessToken);
    }

    public static void main(String[] args) {
        System.out.println("90900-");
        String ss = "";
        System.out.println();

    }


}

 
    
    
    
    