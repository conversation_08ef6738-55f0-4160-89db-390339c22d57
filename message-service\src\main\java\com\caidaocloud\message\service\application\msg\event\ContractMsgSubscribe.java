package com.caidaocloud.message.service.application.msg.event;

import com.caidaocloud.message.sdk.event.constant.MQConstant;
import com.caidaocloud.mq.rabbitmq.ConsumerGenerate;
import com.caidaocloud.mq.rabbitmq.consumer.DynamicConsumer;
import com.caidaocloud.mq.rabbitmq.factory.ExchangeType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 后续将逐渐作废
 */
@Slf4j
@Deprecated
@Component
public class ContractMsgSubscribe {

    @Autowired
    private ConsumerGenerate consumerGenerate;
    @Autowired
    private ContractConsumer contractConsumer;

    @Value("${caidaocloud.tenant:}")
    private List<String> tenantList;

    private final static String DIRECT_EXCHANGE = "message.fac.direct.exchange";

    private final static String DELAY_EXCHANGE = "message.fac.delayed.exchange";

    // 文件签署发起
    private final static String SEND_ROUTINGKEY = "routingKey.message.esign.contract.send";
    // 文件签署完成
    private final static String CONTRACT_SIGN_COMPLETE = "routingKey.message.esign.contract.complete";
    // 电子证明-盖章完成提醒
    private final static String CERT_SIGN_COMPLETE = "routingKey.message.esign.certsign.complete";
    // 文件签署催办
    private final static String CONTRACT_SIGN_URGE = "routingKey.message.esign.contract.urge";
    // 文件签署撤回
    private final static String CONTRACT_SIGN_REVOKE = "routingKey.message.esign.contract.revoke";
    // 文件签署作废
    private final static String CANCEL_ROUTINGKEY = "routingKey.message.esign.contract.cancel";
    // 文件未签署提醒消息
    private final static String SIGNURGE_ROUTINGKEY = "routingKey.message.delayed.esign.contract.signurge";
    // 合同管理-发起新签
    private final static String CONTRACT_MANAGE_NEW = "routingKey.message.contract.new";
    // 合同管理-发起续签
    private final static String CONTRACT_MANAGE_RENEW = "routingKey.message.contract.renew";
    // 合同管理-发起改签
    private final static String CONTRACT_MANAGE_AMENDMENT = "routingKey.message.contract.amendment";

    private final static String QUEUE_CONTRACT_SEND = "message.queue.contract.send";

    private final static String QUEUE_CONTRACT_CANCEL = "message.queue.contract.cancel";

    private final static String QUEUE_CONTRACT_DELAY_URGE = "message.queue.contract.sign.delay.urge";

    private final static String QUEUE_CONTRACT_COMPLETE = "message.queue.contract.complete";

    private final static String QUEUE_CERT_SIGN_COMPLETE = "message.queue.certsign.complete";

    private final static String QUEUE_CONTRACT_URGE = "message.queue.contract.urge";

    private final static String QUEUE_CONTRACT_REVOKE = "message.queue.contract.revoke";

    private final static String QUEUE_CONTRACT_NEW = "message.queue.contract.new";

    private final static String QUEUE_CONTRACT_RENEW = "message.queue.contract.renew";

    private final static String QUEUE_CONTRACT_AMENDMENT = "message.queue.contract.amendment";

    @PostConstruct
    public void init() throws Exception {
        if (null == tenantList || tenantList.isEmpty()) {
            log.info("tenantList is empty, execution end");
            return;
        }

        for (String tenantId : tenantList) {
            initQueue(tenantId);
        }
    }

    public void initQueue(String tenantId){
        try {
            // 文件签署发起
            DynamicConsumer dynamicConsumerSend = consumerGenerate.genConsumer(ExchangeType.DIRECT, contractConsumer, DIRECT_EXCHANGE, String.format("%s_%s", QUEUE_CONTRACT_SEND, tenantId),
                    String.format("%s_%s", SEND_ROUTINGKEY, tenantId), false, true, true);
            dynamicConsumerSend.start();
            // 文件签署作废
            DynamicConsumer dynamicConsumerCancel = consumerGenerate.genConsumer(ExchangeType.DIRECT, contractConsumer, DIRECT_EXCHANGE,
                    String.format("%s_%s", QUEUE_CONTRACT_CANCEL, tenantId), CANCEL_ROUTINGKEY + "_" + tenantId, false, true, true);
            dynamicConsumerCancel.start();
            // 文件签署未签署提醒
            DynamicConsumer dynamicConsumerSignUrge = consumerGenerate.genConsumer(ExchangeType.DELAY, contractConsumer, DELAY_EXCHANGE,
                    String.format("%s_%s", QUEUE_CONTRACT_DELAY_URGE, tenantId), String.format("%s_%s", SIGNURGE_ROUTINGKEY, tenantId),
                    false, true, true);
            dynamicConsumerSignUrge.start();
            // 文件签署完成
            DynamicConsumer contractSignComplete = consumerGenerate.genConsumer(ExchangeType.DIRECT,
                    contractConsumer, DIRECT_EXCHANGE, String.format("%s_%s", QUEUE_CONTRACT_COMPLETE, tenantId),
                    String.format("%s_%s", CONTRACT_SIGN_COMPLETE, tenantId), false, true, true);
            contractSignComplete.start();
            // 电子证明-盖章完成提醒
            DynamicConsumer certSignComplete = consumerGenerate.genConsumer(ExchangeType.DIRECT,
                    contractConsumer, DIRECT_EXCHANGE, String.format("%s_%s", QUEUE_CERT_SIGN_COMPLETE, tenantId),
                    String.format("%s_%s", CERT_SIGN_COMPLETE, tenantId), false, true, true);
            certSignComplete.start();
            // 文件签署催办
            DynamicConsumer contractUrge = consumerGenerate.genConsumer(ExchangeType.DIRECT, contractConsumer, DIRECT_EXCHANGE,
                    String.format("%s_%s", QUEUE_CONTRACT_URGE, tenantId), String.format("%s_%s", CONTRACT_SIGN_URGE, tenantId), false,
                    true, true);
            contractUrge.start();
            // 文件签署撤回
            DynamicConsumer contractRevoke = consumerGenerate.genConsumer(ExchangeType.DIRECT, contractConsumer, DIRECT_EXCHANGE,
                    String.format("%s_%s", QUEUE_CONTRACT_REVOKE, tenantId), String.format("%s_%s", CONTRACT_SIGN_REVOKE, tenantId),
                    false, true, true);
            contractRevoke.start();
            // 合同新签
            DynamicConsumer dynamicConsumerContractNew = consumerGenerate.genConsumer(ExchangeType.DIRECT, contractConsumer, DIRECT_EXCHANGE,
                    String.format("%s_%s", QUEUE_CONTRACT_NEW, tenantId), String.format("%s_%s", CONTRACT_MANAGE_NEW, tenantId),
                    false, true, true);
            dynamicConsumerContractNew.start();
            // 合同续签
            DynamicConsumer dynamicConsumerContractReNew = consumerGenerate.genConsumer(ExchangeType.DIRECT, contractConsumer, DIRECT_EXCHANGE,
                    String.format("%s_%s", QUEUE_CONTRACT_RENEW, tenantId), String.format("%s_%s", CONTRACT_MANAGE_RENEW, tenantId),
                    false, true, true);
            dynamicConsumerContractReNew.start();
            // 合同改签
            DynamicConsumer dynamicConsumerContractAmendment = consumerGenerate.genConsumer(ExchangeType.DIRECT, contractConsumer, DIRECT_EXCHANGE,
                    String.format("%s_%s", QUEUE_CONTRACT_AMENDMENT, tenantId), String.format("%s_%s", CONTRACT_MANAGE_AMENDMENT, tenantId),
                    false, true, true);
            dynamicConsumerContractAmendment.start();
            // 平台租户级的消息发送队列
            DynamicConsumer platformTenantMQ = consumerGenerate.genConsumer(ExchangeType.DELAY, contractConsumer,
                    MQConstant.EXCHANGE,
                    String.format(MQConstant.PLATFORM_TENANT_SEPARATOR, MQConstant.QUEUE_PLATFORM_TENANT, tenantId),
                    String.format(MQConstant.PLATFORM_TENANT_SEPARATOR, MQConstant.ROUTING_KEY, tenantId),
                    false, true, true);
            platformTenantMQ.start();
        } catch (Exception e) {
            log.error("init createQueue err,{}", e.getMessage(), e);
        }
    }
}
