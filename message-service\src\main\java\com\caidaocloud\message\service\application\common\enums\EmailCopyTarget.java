package com.caidaocloud.message.service.application.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 邮件抄送对象
 */
public enum EmailCopyTarget {
    HRBP("hrbp", "HRBP", "HRBP"),
    LEADER("leader", "直接上级", "Direct superior"),
    ORG_LEADER("org<PERSON>eader", "部门负责人", "Department leader"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("EmpRecruiter", "招聘顾问", "Employee Recruiter"),
    COMMON_INITIATOR("COMMON_INITIATOR","发起人","Initiator");

    private String index;
    private String name;
    private String enName;

    EmailCopyTarget(String index, String name, String enName) {
        this.index = index;
        this.name = name;
        this.enName = enName;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public static EmailCopyTarget getEnumByIndex(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        EmailCopyTarget[] values = EmailCopyTarget.values();
        for (EmailCopyTarget noticeTarget : values) {
            if (noticeTarget.index.equals(value)) {
                return noticeTarget;
            }
        }
        return null;
    }
}
