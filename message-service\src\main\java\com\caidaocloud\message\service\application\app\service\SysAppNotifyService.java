package com.caidaocloud.message.service.application.app.service;

import com.caidaocloud.message.service.application.app.dto.AppMsgSender;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysAppNotifyService implements AppNotifyService{
    @Override
    public boolean sendMsg(AppMsgSender sender) {
        return false;
    }

    @Override
    public boolean batchSendMsg(List<AppMsgSender> senderList) {
        return false;
    }

    @Override
    public String getMsgType() {
        return "caidaoapp";
    }
}
