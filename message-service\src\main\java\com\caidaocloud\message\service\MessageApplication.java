package com.caidaocloud.message.service;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.zxp.esclientrhl.annotation.EnableESTools;

@SpringBootApplication
@EnableCircuitBreaker
@EnableDiscoveryClient
@EnableFeignClients
@EnableESTools
@ComponentScan(basePackages = {"com.caidaocloud.message.service.*", "com.caidaocloud"})
public class MessageApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(MessageApplication.class, args);
    }
}
