package com.caidaocloud.message.service.interfaces.dto.msg;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * EmailConfigDto
 *
 * <AUTHOR>
 * @date 2022/6/7 上午9:53
 */
@Data
@ApiModel("消息设置查询dto")
public class MsgConfigQueryDto extends BasePage {
    @ApiModelProperty("消息通知名称")
    private String name;
    @ApiModelProperty("指定人消息刷新")
    private String empId;
    @ApiModelProperty("消息状态")
    private String status;
    @ApiModelProperty("消息状态")
    private boolean filterEmptyCondition = false;
}
