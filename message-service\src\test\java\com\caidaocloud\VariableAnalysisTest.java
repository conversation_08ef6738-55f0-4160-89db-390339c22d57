package com.caidaocloud;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.domain.template.service.PlaceholderDomainService;
import com.caidaocloud.message.service.interfaces.dto.template.TemplateVariableDto;
import com.caidaocloud.message.service.interfaces.vo.msg.MsgConfigDetailVo;
import com.caidaocloud.message.service.interfaces.vo.template.TemplateVo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class VariableAnalysisTest {
    @Resource
    private PlaceholderDomainService placeholderDomainService;

    @Test
    public void testVariable() {
        //test1();

//        Map<String, TemplateVariableDto> tvMap = placeholderDomainService.mapPlaceholder();
        System.out.println();
        System.out.println("===============");
        String contentHtml = "<p>候选#人编号#：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人任职信息.工号#\">#候选人任职信息.工号#</span></p><p>候选人姓名：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人个人信息.姓名#\">#候选人个人信息.姓名#</span></p><p>所属组织：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人任职信息.所属组织#\">#候选人任职信息.所属组织#</span></p><p>岗位：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人任职信息.岗位#\">#候选人任职信息.岗位#</span></p><p>职务：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人任职信息.职务#\">#候选人任职信息.职务#</span></p><p>入职日期：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人任职信息.入职日期#\">#候选人任职信息.入职日期#</span></p><p>预计毕业日期：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人任职信息.预计毕业日期#\">#候选人任职信息.预计毕业日期#</span></p><p>合同公司：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人任职信息.合同公司#\">#候选人任职信息.合同公司#</span></p><p>合同类型：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人任职信息.合同类型#\">#候选人任职信息.合同类型#</span></p><p>职业健康体检：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人任职信息.职业健康体检要求#\">#候选人任职信息.职业健康体检要求#</span></p><p>健康证：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人任职信息.健康证#\">#候选人任职信息.健康证#</span></p><p>姓名拼音：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人任职信息.姓名拼音#\">#候选人任职信息.姓名拼音#</span></p><p>入职步骤：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人步骤名称.中文#\">#候选人步骤名称.中文#</span></p><p>入职步骤英文：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人步骤名称.英文#\">#候选人步骤名称.英文#</span></p><p>手机号：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人个人信息.手机号#\">#候选人个人信息.手机号#</span></p><p>邮箱：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人个人信息.邮箱#\">#候选人个人信息.邮箱#</span></p><p>英文名：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人个人信息.英文名#\">#候选人个人信息.英文名#</span></p><p>证件类型：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人个人信息.证件类型#\">#候选人个人信息.证件类型#</span></p><p>证件号：<span data-w-e-type=\"variableLabel\" data-w-e-is-void data-w-e-is-inline data-link=\"\" data-labelName=\"#候选人个人信息.证件号#\">#候选人个人信息.证件号#</span></p><p><br></p>\n";
        System.out.println(contentHtml);

        String word = "#";
        int len = word.length(), length = contentHtml.length(), dlnIndex = 0;
        List<TemplateVariableDto> variableDtos = new ArrayList<>();
        while (dlnIndex < length) {
            dlnIndex = contentHtml.indexOf(word, dlnIndex);
            if(dlnIndex < 0){
                // 未找到，则结束
                break;
            }

            int endIndex = contentHtml.indexOf(word, dlnIndex  + len);
            if(endIndex < 0){
                // 找到了结尾还未找到，则结束
                break;
            }
            String substring = contentHtml.substring(dlnIndex + len, endIndex);
            if(substring.indexOf(".") > -1){
                System.out.println(substring);
//                TemplateVariableDto templateVariableDto = tvMap.get("#" + substring + "#");
//                variableDtos.add(templateVariableDto);
            }

            dlnIndex = endIndex;
        }

        List<TemplateVariableDto> collect = variableDtos.stream().distinct().collect(Collectors.toList());
        System.out.println(FastjsonUtil.toJson(collect));
    }

    public static void test1(){
        String htmlStr = "{\"data\":{\"bid\":\"1515985159837698\",\"name\":\"发起入职-测试字段带出\",\"msgType\":\"ON_BOARDING_START_ENTRY\",\"notifyObject\":[\"EVENT_EMP\"],\"condition\":null,\"func\":\"0\",\"round\":null,\"day\":null,\"triggerDate\":null,\"sendTime\":null,\"loop\":null,\"channel\":[\"1\"],\"templates\":[{\"bid\":\"1515985160886275\",\"name\":\"发起入职-测试字段带出\",\"content\":\"<p>候选人编号：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人任职信息.工号#\\\">#候选人任职信息.工号#</span></p><p>候选人姓名：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人个人信息.姓名#\\\">#候选人个人信息.姓名#</span></p><p>所属组织：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人任职信息.所属组织#\\\">#候选人任职信息.所属组织#</span></p><p>岗位：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人任职信息.岗位#\\\">#候选人任职信息.岗位#</span></p><p>职务：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人任职信息.职务#\\\">#候选人任职信息.职务#</span></p><p>入职日期：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人任职信息.入职日期#\\\">#候选人任职信息.入职日期#</span></p><p>预计毕业日期：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人任职信息.预计毕业日期#\\\">#候选人任职信息.预计毕业日期#</span></p><p>合同公司：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人任职信息.合同公司#\\\">#候选人任职信息.合同公司#</span></p><p>合同类型：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人任职信息.合同类型#\\\">#候选人任职信息.合同类型#</span></p><p>职业健康体检：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人任职信息.职业健康体检要求#\\\">#候选人任职信息.职业健康体检要求#</span></p><p>健康证：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人任职信息.健康证#\\\">#候选人任职信息.健康证#</span></p><p>姓名拼音：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人任职信息.姓名拼音#\\\">#候选人任职信息.姓名拼音#</span></p><p>入职步骤：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人步骤名称.中文#\\\">#候选人步骤名称.中文#</span></p><p>入职步骤英文：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人步骤名称.英文#\\\">#候选人步骤名称.英文#</span></p><p>手机号：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人个人信息.手机号#\\\">#候选人个人信息.手机号#</span></p><p>邮箱：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人个人信息.邮箱#\\\">#候选人个人信息.邮箱#</span></p><p>英文名：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人个人信息.英文名#\\\">#候选人个人信息.英文名#</span></p><p>证件类型：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人个人信息.证件类型#\\\">#候选人个人信息.证件类型#</span></p><p>证件号：<span data-w-e-type=\\\"variableLabel\\\" data-w-e-is-void data-w-e-is-inline data-link=\\\"\\\" data-labelName=\\\"#候选人个人信息.证件号#\\\">#候选人个人信息.证件号#</span></p><p><br></p>\",\"params\":null,\"category\":{\"text\":\"邮件模版\",\"value\":\"1\"},\"title\":\"发起入职-测试字段带出发起入职-测试字段带出-sit\",\"code\":null,\"attachFile\":{\"names\":[],\"urls\":[]},\"msgConfig\":\"1515985159837698\",\"variable\":[{\"model\":\"entity.onboarding.EmpWorkInfo\",\"text\":\"#候选人任职信息.工号#\",\"prop\":\"empWorkInfo.workno\",\"name\":\"工号\",\"varType\":\"0\"},{\"model\":\"entity.onboarding.EmpWorkInfo\",\"text\":\"#候选人任职信息.所属组织#\",\"prop\":\"empWorkInfo.organizeTxt\",\"name\":\"所属组织\",\"varType\":\"0\"},{\"model\":\"entity.onboarding.EmpWorkInfo\",\"text\":\"#候选人任职信息.岗位#\",\"prop\":\"empWorkInfo.postTxt\",\"name\":\"岗位\",\"varType\":\"0\"},{\"model\":\"entity.onboarding.EmpWorkInfo\",\"text\":\"#候选人任职信息.职务#\",\"prop\":\"empWorkInfo.jobTxt\",\"name\":\"职务\",\"varType\":\"0\"},{\"model\":\"entity.onboarding.EmpWorkInfo\",\"text\":\"#候选人任职信息.入职日期#\",\"prop\":\"empWorkInfo.hireDate\",\"name\":\"入职日期\",\"varType\":\"0\"},{\"model\":\"entity.onboarding.EmpWorkInfo\",\"text\":\"#候选人任职信息.预计毕业日期#\",\"prop\":\"empWorkInfo.expectGraduateDate\",\"name\":\"预计毕业日期\",\"varType\":\"0\"},{\"model\":\"entity.onboarding.EmpWorkInfo\",\"text\":\"#候选人任职信息.合同公司#\",\"prop\":\"empWorkInfo.company\",\"name\":\"合同公司\",\"varType\":\"0\"},{\"model\":\"entity.onboarding.EmpWorkInfo\",\"text\":\"#候选人任职信息.合同类型#\",\"prop\":\"empWorkInfo.contractType\",\"name\":\"合同类型\",\"varType\":\"0\"},{\"model\":\"entity.onboarding.EmpWorkInfo\",\"text\":\"#候选人任职信息.职业健康体检要求#\",\"prop\":\"empWorkInfo.healthy\",\"name\":\"职业健康体检要求\",\"varType\":\"0\"},{\"model\":\"entity.onboarding.EmpWorkInfo\",\"text\":\"#候选人任职信息.健康证#\",\"prop\":\"empWorkInfo.healthCertificate\",\"name\":\"健康证\",\"varType\":\"0\"},{\"model\":\"entity.onboarding.EmpWorkInfo\",\"text\":\"#候选人任职信息.姓名拼音#\",\"prop\":\"empWorkInfo.ext.namePinyin\",\"name\":\"姓名拼音\",\"varType\":\"0\"},{\"model\":\"entity.onboarding.Step\",\"text\":\"#候选人步骤名称.中文#\",\"prop\":\"entry.step.cnTxt\",\"name\":\"步骤名称.中文\",\"varType\":\"0\"},{\"model\":\"entity.onboarding.Step\",\"text\":\"#候选人步骤名称.英文#\",\"prop\":\"entry.step.enTxt\",\"name\":\"步骤名称.英文\",\"varType\":\"0\"},{\"model\":\"entity.onboarding.EmpPrivateInfo\",\"text\":\"#候选人个人信息.姓名#\",\"prop\":\"empPrivateInfo.phone\",\"name\":\"姓名\",\"varType\":\"1\"},{\"model\":\"entity.onboarding.EmpPrivateInfo\",\"text\":\"#候选人个人信息.邮箱#\",\"prop\":\"empPrivateInfo.email\",\"name\":\"邮箱\",\"varType\":\"1\"},{\"model\":\"entity.onboarding.EmpPrivateInfo\",\"text\":\"#候选人个人信息.英文名#\",\"prop\":\"empPrivateInfo.enName\",\"name\":\"英文名\",\"varType\":\"0\"},{\"model\":\"entity.onboarding.EmpPrivateInfo\",\"text\":\"#候选人个人信息.证件类型#\",\"prop\":\"empPrivateInfo.cardType.text\",\"name\":\"证件类型\",\"varType\":\"0\"},{\"model\":\"entity.onboarding.EmpPrivateInfo\",\"text\":\"#候选人个人信息.证件号#\",\"prop\":\"empPrivateInfo.cardNo\",\"name\":\"证件号\",\"varType\":\"0\"}],\"copyObject\":null,\"copyMail\":null,\"externalMail\":null,\"noticeRule\":\"1\"}],\"emps\":null},\"code\":0,\"msg\":\"success\",\"serverTime\":1665225506688,\"success\":true}";

        System.out.println(htmlStr);

        Result result = FastjsonUtil.toObject(htmlStr, Result.class);
        MsgConfigDetailVo msgConfigDetailVo = FastjsonUtil.toObject(FastjsonUtil.toJson(result.getData()), MsgConfigDetailVo.class);;
        List<TemplateVo> templates = msgConfigDetailVo.getTemplates();
        TemplateVo templateVo = templates.get(0);

        String content = templateVo.getContent();
        System.out.println();
        System.out.println("--------");
        System.out.println();
        System.out.println(content);


        System.out.println("--------");
        System.out.println();
        System.out.println();
        String word = "data-labelName=\"";
        int len = word.length(), length = content.length(), dlnIndex = 0;

        while (dlnIndex < length) {
            dlnIndex = content.indexOf(word, dlnIndex);
            if(dlnIndex < 0){
                // 未找到，则结束
                break;
            }

            int endIndex = content.indexOf("\"", dlnIndex  + len);
            if(endIndex < 0){
                // 找到了结尾还未找到，则结束
                break;
            }
            String substring = content.substring(dlnIndex + len, endIndex);
            System.out.println(substring);
            dlnIndex = endIndex;
        }
/*
        dlnIndex = content.indexOf(word);
        int endIndex = content.indexOf("\"", dlnIndex  + len);
        String substring = content.substring(dlnIndex + len, endIndex);
        System.out.println(substring);*/
    }

    @Before
    public void initUser(){
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("33");
        userInfo.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);

        UserInfo user = new UserInfo();
        user.setTenantId(String.valueOf(33));
        user.doSetUserId(0L);
        UserContext.setCurrentUser(user);
    }
}
