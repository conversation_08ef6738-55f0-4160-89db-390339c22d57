package com.caidaocloud.message.service.infrastructure.repository.impl.msg;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.service.application.common.enums.MsgConfigStatusEnum;
import com.caidaocloud.message.service.application.msg.dto.MessageConfigPageQueryDto;
import com.caidaocloud.message.service.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.message.service.domain.msg.entity.MsgConfigDo;
import com.caidaocloud.message.service.domain.msg.repository.MsgConfigRepository;
import com.caidaocloud.message.service.infrastructure.repository.po.MsgConfigPo;
import com.caidaocloud.message.service.interfaces.dto.msg.MsgConfigQueryDto;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * MsgConfigRepositoryImpl
 *
 * <AUTHOR>
 * @date 2022/6/6 下午6:04
 */
@Repository
public class MsgConfigRepositoryImpl extends BaseRepositoryImpl<MsgConfigDo> implements MsgConfigRepository {

    private final static String IDENTIFIER = "entity.message.MsgConfig";

    @Override
    public MsgConfigDo selectById(String bid, String identifier) {
        MsgConfigPo po = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .oneOrNull(bid, MsgConfigPo.class);
        return po == null ? null : po.toEntity();
    }

    @Override
    public MsgConfigDo insert(MsgConfigDo data) {
        String dataId = DataInsert.identifier(data.getIdentifier()).insert(MsgConfigPo.fromEntity(data));
        data.setBid(dataId);
        return data;
    }

    @Override
    public int updateById(MsgConfigDo data) {
        DataUpdate.identifier(data.getIdentifier()).update(MsgConfigPo.fromEntity(data));
        return 0;
    }

    @Override
    public List<MsgConfigDo> selectByMsgTypeAndStatus(NoticeType noticeType, MsgConfigStatusEnum status) {
        return getMsgConfigByTypeAndIds(null, noticeType, status);
    }

    @Override
    public PageResult<MsgConfigDo> selectPage(BasePage page, MsgConfigDo data) {
        MsgConfigQueryDto queryDto = (MsgConfigQueryDto) page;
        DataFilter dataFilter = getBaseFilter();

        if (StringUtils.isNotBlank(queryDto.getName())) {
            dataFilter = dataFilter.andRegex("name", queryDto.getName());
        }

        if(StringUtils.isNotEmpty(queryDto.getStatus())){
            dataFilter = dataFilter.andEq("status", queryDto.getStatus());
        }

        if(queryDto.isFilterEmptyCondition()){
            dataFilter = dataFilter.andNe("condition", "")
                .andNe("condition", null);
        }

        dataFilter = (DataFilter) queryDto.doDataFilter(queryDto.getFilters(), dataFilter);
        PageResult<MsgConfigPo> result = DataQuery.identifier(data.getIdentifier()).queryInvisible().decrypt()
                .specifyLanguage()
                .limit(page.getPageSize(), page.getPageNo())
                .filter(dataFilter, MsgConfigPo.class);
        return new PageResult<>(Sequences.sequence(result.getItems()).map(MsgConfigPo::toEntity)
                .toList(), result.getPageNo(), result.getPageSize(), result.getTotal());
    }

    @Override
    public List<MsgConfigDo> getMsgConfigByTypeAndIds(List<String> ids, NoticeType noticeType, MsgConfigStatusEnum status) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString())
                .andEq("msgType", noticeType.getIndex())
                .andEq("status", status.value);
        if (CollectionUtils.isNotEmpty(ids)) {
            dataFilter = dataFilter.andIn("bid", ids);
        }
        List<MsgConfigPo> list = DataQuery.identifier(IDENTIFIER).decrypt().specifyLanguage()
                .queryInvisible()
                .filter(dataFilter, MsgConfigPo.class).getItems();
        return Sequences.sequence(list).map(MsgConfigPo::toEntity).toList();
    }

    @Override
    public List<MsgConfigDo> getEnableMsgConfigList(String noticeType) {
        List<MsgConfigPo> list = DataQuery.identifier(IDENTIFIER).decrypt().specifyLanguage()
                .queryInvisible()
                .limit(5000, 1)
                .filter(DataFilter.eq("tenantId", UserContext.getTenantId())
                                .andEq("deleted", Boolean.FALSE.toString())
                                .andEq("msgType", noticeType)
                                .andEq("status", MsgConfigStatusEnum.ENABLED.value),
                        MsgConfigPo.class).getItems();
        return Sequences.sequence(list).map(MsgConfigPo::toEntity).toList();
    }

    @Override
    public PageResult<MsgConfigDo> selectEnableMsgConfigPageList(MessageConfigPageQueryDto queryDto) {
        PageResult<MsgConfigPo> result = DataQuery.identifier(IDENTIFIER).decrypt().specifyLanguage()
                .queryInvisible()
                .limit(queryDto.getPageSize(), queryDto.getPageNo())
                .filter(DataFilter.eq("tenantId", UserContext.getTenantId())
                        .andEq("deleted", Boolean.FALSE.toString())
                        .andEq("msgType", queryDto.getMsgType())
                        .andEq("status", MsgConfigStatusEnum.ENABLED.value), MsgConfigPo.class);
        return new PageResult<>(Sequences.sequence(result.getItems()).map(MsgConfigPo::toEntity)
                .toList(), result.getPageNo(), result.getPageSize(), result.getTotal());
    }

    @Override
    public List<MsgConfigDo> getOnboardingSelectList(List<String> types) {
        List<MsgConfigPo> list = DataQuery.identifier(IDENTIFIER).decrypt().specifyLanguage()
                .queryInvisible()
                .limit(5000, 1)
                .filter(DataFilter.eq("tenantId", UserContext.getTenantId())
                                .andEq("deleted", Boolean.FALSE.toString())
                                .andIn("msgType", types)
                                .andEq("status", MsgConfigStatusEnum.ENABLED.value),
                        MsgConfigPo.class).getItems();
        return Sequences.sequence(list).map(MsgConfigPo::toEntity).toList();
    }

}