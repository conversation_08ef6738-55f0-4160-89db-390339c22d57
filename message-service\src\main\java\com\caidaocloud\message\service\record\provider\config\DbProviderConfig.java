package com.caidaocloud.message.service.record.provider.config;

import com.caidaocloud.message.service.record.mybatis.MybatisPlusConfiguration;
import com.caidaocloud.message.service.record.properties.LogProviderProperties;
import com.caidaocloud.message.service.record.provider.RecordRepository;
import com.caidaocloud.message.service.record.provider.db.IRecordDbService;
import com.caidaocloud.message.service.record.provider.db.RecordDbRepository;
import com.caidaocloud.message.service.record.provider.db.TableInitService;
import com.caidaocloud.message.service.record.provider.db.impl.RecordDbServiceImpl;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * created by: FoAng
 * create time: 2/7/2024 3:58 下午
 */
@Configuration
@ConditionalOnProperty(name = "caidaocloud.log.record.provider", havingValue = "db")
@ImportAutoConfiguration(MybatisPlusConfiguration.class)
public class DbProviderConfig {

    @Bean
    public IRecordDbService recordDbService() {
        return new RecordDbServiceImpl();
    }

    @Bean("db")
    @ConditionalOnBean(IRecordDbService.class)
    public RecordRepository dbRecordRepository() {
        return new RecordDbRepository(recordDbService());
    }

    @Bean
    @ConditionalOnBean(IRecordDbService.class)
    public TableInitService tableInitService(DataSource dataSource, LogProviderProperties providerProperties) {
        return new TableInitService(dataSource, providerProperties, recordDbService());
    }
}
