package com.caidaocloud.record.core.ext.parse;

import com.caidaocloud.record.core.annotation.LogRecordField;
import com.caidaocloud.record.core.ext.FieldType;
import com.caidaocloud.record.core.ext.IDataTypeParse;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * created by: FoAng
 * create time: 23/5/2024 4:20 下午
 */
public abstract class AbsDataTypeParse implements IDataTypeParse {


    @Override
    public boolean supportParse(LogRecordField annotation, Object data) {
        return Optional.ofNullable(annotation).map(LogRecordField::type)
                .orElse(FieldType.resolveType(data)) != null;
    }

    @Override
    public String parseField(LogRecordField annotation, Object data) {
        if (data == null) return "";
        FieldType fieldType = Optional.ofNullable(annotation.type())
                .orElse(FieldType.resolveDefaultType(data));
        if (fieldType == FieldType.STRING) {
            return Objects.toString(data);
        } else if (fieldType == FieldType.DATE) {
            return parseDate(data, annotation.format());
        } else if (fieldType == FieldType.TIMESTAMP) {
            return parseTimeStamp(data, annotation.format());
        } else if (fieldType == FieldType.JSON) {
            return FastjsonUtil.toJson(data);
        } else if (fieldType.getLinkClass() != null) {
            Class<?> componentClass = fieldType.getLinkClass();
            return parseComponentSimpleData(data, componentClass);
        } else if (fieldType == FieldType.ENUM) {
            return ((Enum<?>) data).name();
        } else if (fieldType == FieldType.BOOLEAN) {
            return (Boolean) data ? "是" : "否";
        }
        return Objects.toString(data);
    }

    /**
     * 解析组件结构
     * @param field
     * @return
     */
    abstract String parseComponentSimpleData(Object field, Class<?> componentClass);

    public String parseTimeStamp(Object field, String formatStr) {
        return DateUtil.format((Date) field, formatStr);
    }

    public String parseDate(Object field, String formatStr) {
        return DateUtil.format(new Date((Long) field), formatStr);
    }

}
