package com.caidaocloud.message.test;

import com.caidaocloud.message.service.domain.template.entity.PlaceholderDo;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.HashMap;
import java.util.Map;

public class ExpressionParserTest {
    public static void main(String[] args) {
        String content = "我是测试内容，#员工任职信息.姓名#hhh我似懂非懂##员工个人信息.手机号#sds";
        Map<String, String> stringMap = new HashMap<>();
        stringMap.put("#员工任职信息.姓名#", "涛哥");
        stringMap.put("#员工个人信息.手机号#", "abc");

        stringMap.forEach((k, v) -> {
            System.out.println(content.replaceAll(k, v));
        });

        // #员工任职信息.姓名#   员工任职信息   name

        PlaceholderDo test = new PlaceholderDo();
        test.setCustom(false);
        test.setModel("entity.hr.EmpWorkInfo");
        test.setText("#员工任职信息.姓名#");
        test.setName("姓名");
        test.setProp("empWorkInfo.name");

        ExpressionParser parser = new SpelExpressionParser();
        StandardEvaluationContext context = new StandardEvaluationContext();
        context.setVariable("empWorkInfo", test);
        //System.out.println(tests.stream().map(Tester::getId).collect(Collectors.toList())); // LIKE THIS
        System.out.println(parser.parseExpression(content).getValue(context));
    }
}
