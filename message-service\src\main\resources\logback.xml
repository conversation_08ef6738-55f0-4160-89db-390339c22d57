<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="rollingFile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./logs/caidao-message-service.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./logs/caidao-message-service.%d{yyyy-MM-dd}-%i.log
            </fileNamePattern>
            <TimeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <MaxFileSize>10MB</MaxFileSize>
            </TimeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="errorAlarm" class="com.caidaocloud.config.ErrorLogRollingPolicy">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <layout class="ch.qos.logback.classic.html.HTMLLayout">
            <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </layout>
        <cyclicBufferTracker class="ch.qos.logback.core.spi.CyclicBufferTracker">
            <bufferSize>1</bufferSize>
        </cyclicBufferTracker>
    </appender>

    <!--<logger name="com.caidaocloud.message.service.application.feign" level="DEBUG"/>-->

    <root level="info">
        <appender-ref ref="console" />
        <appender-ref ref="rollingFile" />
        <!--<appender-ref ref="errorAlarm" />-->
    </root>
</configuration>
