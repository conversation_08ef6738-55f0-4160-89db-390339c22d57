package com.caidaocloud.message.service.interfaces.facade;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.sdk.enums.DingtalkTemplateTypeEnum;
import com.caidaocloud.message.service.application.template.service.TemplateService;
import com.caidaocloud.message.service.domain.template.entity.TemplateDo;
import com.caidaocloud.message.service.interfaces.dto.template.TemplateDto;
import com.caidaocloud.message.service.interfaces.dto.template.TemplateQueryDto;
import com.caidaocloud.message.service.interfaces.vo.template.TemplateVo;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.caidaocloud.message.service.domain.template.entity.TemplateDo.DEFAULT_CONFIG;

@Slf4j
@RestController
@RequestMapping("/api/msg/template/v1")
@Api(value = "/api/msg/template/v1", description = "消息模版", tags = "v0.4")
public class TemplateController {
    @Resource
    private TemplateService templateService;

    @ApiOperation("新增")
    @PostMapping("/save")
    @LogRecordAnnotation(menu = "管理中心-消息管理-模板管理-{{T(com.caidaocloud.message.service.interfaces.dto.template.TemplateDto).getCategoryTxt(#dto.category)}}",
            category = "新增", success = "新增了{{#dto.getTemplateName()}}")
    public Result save(@RequestBody TemplateDto dto) {
        templateService.saveOrUpdateObj(dto);
        return Result.ok(true);
    }

    @ApiOperation("修改")
    @PostMapping("/update")
    @LogRecordAnnotation(menu = "管理中心-消息管理-模板管理-{{T(com.caidaocloud.message.service.interfaces.dto.template.TemplateDto).getCategoryTxt(#dto.category)}}",
            category = "编辑", success = "编辑了{{#dto.getTemplateName()}}")
    public Result update(@RequestBody TemplateDto dto) {
        templateService.saveOrUpdateObj(dto);
        return Result.ok(true);
    }

    @ApiOperation("列表")
    @PostMapping("/list")
    public PageResult<TemplateVo> getList(@RequestBody TemplateQueryDto query) {
        if(null == query.getCategory() || StringUtil.isEmpty(query.getCategory().getValue())){
            return emptyPage(query, 0);
        }

        PageResult<TemplateDo> pageList = templateService.getPage(query);
        if(null == pageList || null == pageList.getItems() || pageList.getItems().isEmpty()){
            return emptyPage(query, pageList.getTotal());
        }
        List<TemplateVo> dataList = TemplateVo.toVoList(pageList.getItems());
        return new PageResult<>(dataList, pageList.getPageNo(), pageList.getPageSize(), pageList.getTotal());
    }

    @ApiOperation("消息模板下拉框列表")
    @GetMapping("/all")
    public Result<List<TemplateVo>> getAll(String category) {
        if (null == category) {
            return Result.ok(Lists.newArrayList());
        }

        List<TemplateDo> list = templateService.getAllByCategory(category);
        return Result.ok(TemplateVo.toVoList(list));
    }


    private PageResult<TemplateVo> emptyPage(TemplateQueryDto query, int total){
        return new PageResult<>(Lists.newArrayList(), query.getPageNo(), query.getPageSize(), total);
    }

    @ApiOperation("删除")
    @DeleteMapping("/remove")
    @LogRecordAnnotation(menu = "管理中心-消息管理-模板管理-{tplType{#bid}}", category = "删除", success = "删除了{tplName{#bid}}")
    public Result removeDoc(@RequestParam("bid") String bid) {
        TemplateDo data = templateService.getById(bid);
        if (null == data || StringUtil.isEmpty(data.getBid()) || !DEFAULT_CONFIG.equals(data.getMsgConfig())) {
            return Result.ok(true);
        }
        templateService.delete(bid);
        return Result.ok(true);
    }


    @ApiOperation("钉钉消息模板类型")
    @GetMapping("/messageTypeList")
    public Result<List<KeyValue>> getMessageTypeList() {
        List<KeyValue> collect = Arrays.stream(DingtalkTemplateTypeEnum.values()).map(type -> {
            KeyValue kv = new KeyValue();
            kv.setText(type.getName());
            kv.setValue(type);
            return kv;
        }).collect(Collectors.toList());
        return Result.ok(collect);
    }

}
