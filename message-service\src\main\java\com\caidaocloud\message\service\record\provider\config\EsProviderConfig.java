package com.caidaocloud.message.service.record.provider.config;

import com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusLanguageDriverAutoConfiguration;
import com.caidaocloud.message.service.record.provider.RecordRepository;
import com.caidaocloud.message.service.record.provider.es.CusEsRepository;
import com.caidaocloud.message.service.record.provider.es.LogEsRepository;
import com.caidaocloud.message.service.record.provider.es.RecordEsRepository;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * created by: FoAng
 * create time: 2/7/2024 3:59 下午
 */
@Configuration
@ConditionalOnProperty(name = "caidaocloud.log.record.provider", havingValue = "es", matchIfMissing = true)
@EnableAutoConfiguration(exclude = {MybatisPlusAutoConfiguration.class,
        DataSourceAutoConfiguration.class,
        IdentifierGeneratorAutoConfiguration.class,
        MybatisPlusLanguageDriverAutoConfiguration.class
})
public class EsProviderConfig {

    @Resource
    private LogEsRepository logEsRepository;

    @Resource
    private CusEsRepository cusEsRepository;


    @Bean("es")
    public RecordRepository esRecordRepository() {
        return new RecordEsRepository(logEsRepository, cusEsRepository);
    }


}
