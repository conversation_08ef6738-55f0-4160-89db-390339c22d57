package com.caidaocloud.record.core.annotation;

import com.caidaocloud.record.core.ext.FieldType;
import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

/**
 * 日志记录字段
 * created by: FoAng
 * create time: 21/5/2024 4:15 下午
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LogRecordField {

    @AliasFor("value")
    String name() default "";

    /**
     * 默认字段的名称
     */
    @AliasFor("name")
    String value() default "";

    /**
     * 字段解析类型
     */
    FieldType type() default FieldType.STRING;

    /**
     * 默认格式化，用于时间类型
     */
    String format() default "yyyy-MM-dd";
}
