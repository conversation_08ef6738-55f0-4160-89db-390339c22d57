package com.caidaocloud.message.service.application.message.notifier.input;

import com.caidaocloud.util.SpringUtil;

public class NotifierDataFactory {
    private static INotifierDataService service;
    public static INotifierDataService getNotifierService(){
        if(null != service){
            return service;
        }

        synchronized (NotifierDataFactory.class){
            if(null == service){
                service = SpringUtil.getBean(INotifierDataService.class);
            }
        }

        return service;
    }
}
