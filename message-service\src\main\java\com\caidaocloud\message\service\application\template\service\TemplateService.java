package com.caidaocloud.message.service.application.template.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.message.service.application.common.service.BaseServiceImpl;
import com.caidaocloud.message.service.domain.base.service.BaseDomainService;
import com.caidaocloud.message.service.domain.template.entity.TemplateDo;
import com.caidaocloud.message.service.domain.template.service.TemplateDomainService;
import com.caidaocloud.message.service.infrastructure.utils.LangUtil;
import com.caidaocloud.message.service.interfaces.dto.template.TemplateDto;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.caidaocloud.message.service.application.common.constant.LangCodeConstant.code_70020;

@Slf4j
@Service
public class TemplateService extends BaseServiceImpl<TemplateDo, BasePage> {
    @Resource
    private TemplateDomainService domainService;

    @Override
    protected BaseDomainService getDomainService() {
        return domainService;
    }

    /**
     * 查询模板分类下所有模板
     * @param category
     * @return
     */
    public List<TemplateDo> getAllByCategory(String category) {
        return domainService.getAllByCategory(category);
    }

    @Override
    public TemplateDo dto2do(Object data) {
        TemplateDo template = super.dto2do(data);
        template.setVariable(FastjsonUtil.toJson(((TemplateDto) data).getVariable()));


        TemplateDto dto = (TemplateDto) data;
        template.setName(String.valueOf(dto.getI18nName().get("default")));
        template.setI18nName(FastjsonUtil.toJson(dto.getI18nName()));
        template.setI18nCategory(FastjsonUtil.toJson(dto.getI18nCategory()));
        if (dto.getTemplateType() != null) {
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(dto.getTemplateType().getIndex());
            template.setTemplateType(enumSimple);
        }

        return template;
    }

    @Override
    protected void checkObj(TemplateDo data) {
        super.checkObj(data);
        domainService.checkAttachment(data);
    }
}
