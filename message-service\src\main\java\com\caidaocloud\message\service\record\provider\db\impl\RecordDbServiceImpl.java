package com.caidaocloud.message.service.record.provider.db.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.caidaocloud.message.service.record.bean.LogRecord;
import com.caidaocloud.message.service.record.mapper.LogRecordMapper;
import com.caidaocloud.message.service.record.provider.db.IRecordDbService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * created by: FoAng
 * create time: 22/5/2024 4:17 下午
 */
@Slf4j
@AllArgsConstructor
public class RecordDbServiceImpl extends ServiceImpl<LogRecordMapper, LogRecord> implements IRecordDbService {

    @Override
    public void initRecordTable(String initSql) {
        this.baseMapper.initRecordTable(initSql);
    }
}
