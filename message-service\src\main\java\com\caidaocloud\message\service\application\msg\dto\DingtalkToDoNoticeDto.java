package com.caidaocloud.message.service.application.msg.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import com.caidaocloud.message.service.application.common.enums.SendStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 钉钉待办通知dto
 * @Date 2023/2/24 下午2:24
 * @Version 1.0
 **/
@Data
@Accessors(chain = true)
public class DingtalkToDoNoticeDto {

    /**
     * 第三方id 钉钉待办任务id
     */
    private String thirdId;

    /**
     * 钉钉待办任务与审批任务关联表的主键ID
     */
    private String dingCaidaoId;

    /**
     * 通知标题
     */
    private String subject;

    /**
     * 创建者的unionId
     */
    private String creatorId;


    /**
     * 待办备注描述，最大长度4096
     */
    private String description;


    /**
     * 截止时间，Unix时间戳，单位毫秒
     */
    private Long dueTime;

    /**
     * 执行者的unionId，最大数量1000
     */
    private List<String> executorIds;

    /**
     * 参与者的unionId，最大数量1000
     */
    private List<String> participantIds;

    /**
     * 详情页url跳转地址
     */
    private DetailUrl detailurl;

    /**
     * 用户手机
     */
    private List<PhoneSimple> mobiles;

    /**
     * 才到流程业务key
     */
    private String businessKey;

    /**
     * 才到流程任务id
     */
    private String taskId;

    /**
     * 发送状态
     */
    private SendStatusEnum status = SendStatusEnum.SUCESS;


    /**
     * 详情页url跳转地址
     */
    @Data
    public static class DetailUrl{

        /**
         * APP端详情页url跳转地址
         */
        private String appUrl;


        /**
         * PC端详情页url跳转地址
         */
        private String pcUrl;
    }

//    public String msgReceiver(){
//        // 多个手机号逗号分隔
//        String phoneNumbers = mobiles.stream()
//                .filter(m -> StringUtil.isNotEmpty(m.getValue()))
//                .map(m -> {
//                    if(m.getValue().indexOf("/") > 0){
//                        return m.getValue().substring(0, m.getValue().indexOf("/"));
//                    }
//                    return m.getValue();
//                }).collect(Collectors.joining(","));
//        return phoneNumbers;
//    }

    /**
     * 接受消息的empId
     */
    private List<String> empIds;

    public void addMobile(String phone){
        if(null == phone){
            return;
        }
        if(null == mobiles){
            this.mobiles = new ArrayList<>();
        }
        addMobile(phone);
    }

    public void addMobile(PhoneSimple ps){
        if(null == mobiles){
            this.mobiles = new ArrayList<>();
        }
        mobiles.add(ps);
    }

}

 
    
    
    
    