package com.caidaocloud.message.service.application.app.service;

import com.caidaocloud.util.PropUtil;
import com.caidaocloud.util.StringUtil;

public class AppNotifyFactory {
    public static String appType = null;

    public static AppNotifyService getService(String appType) {
        NotifyService service = NotifyService.notifyManager.get(appType);
        return (AppNotifyService) service;
    }

    public static AppNotifyService getService() {
        if(StringUtil.isEmpty(appType)){
            appType = PropUtil.getProp("caidaocloud.appType");
            appType = StringUtil.isEmpty(appType) ? "caidaoapp" : appType;
        }

        return getService(appType);
    }

}
